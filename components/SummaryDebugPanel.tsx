import { useState } from "react";
import { Button } from "./ui/button";
import { Input } from "./ui/input";
import { Textarea } from "./ui/textarea";
import { Label } from "./ui/label";
import { Separator } from "./ui/separator";
import { Trash2, Plus } from "lucide-react";

interface SummaryDebugPanelProps {
  currentTopic: string;
  onTopicChange: (topic: string) => void;
  onClearAllSummaries?: () => void;
  onAddSummary?: (timeRange: string, summary: string, keyPoints: string[]) => void;
}

export function SummaryDebugPanel({
  currentTopic,
  onTopicChange,
  onClearAllSummaries,
  onAddSummary
}: SummaryDebugPanelProps) {
  const [topicInput, setTopicInput] = useState(currentTopic);
  const [timeRangeInput, setTimeRangeInput] = useState("");
  const [summaryInput, setSummaryInput] = useState("");
  const [keyPointsInput, setKeyPointsInput] = useState("");

  const handleApplyTopic = () => {
    if (topicInput.trim()) {
      onTopicChange(topicInput.trim());
      console.log("현재 주제가 적용되었습니다:", topicInput.trim());
    } else {
      console.log("주제 적용 실패: 빈 텍스트입니다.");
    }
  };

  const handleTopicKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleApplyTopic();
    }
  };

  const handleClearAllSummaries = () => {
    if (onClearAllSummaries) {
      onClearAllSummaries();
      console.log("모든 구간별 요약이 삭제되었습니다.");
    }
  };

  const handleAddSummary = () => {
    if (!timeRangeInput.trim() || !summaryInput.trim() || !keyPointsInput.trim()) {
      console.log("요약 추가 실패: 모든 필드를 입력해주세요.");
      return;
    }

    // 주요 포인트를 줄바꿈으로 분리하여 배열로 변환
    const keyPoints = keyPointsInput
      .split('\n')
      .map(point => point.trim())
      .filter(point => point !== '');

    if (onAddSummary) {
      onAddSummary(timeRangeInput.trim(), summaryInput.trim(), keyPoints);
      
      // 입력 필드 초기화
      setTimeRangeInput("");
      setSummaryInput("");
      setKeyPointsInput("");
      
      console.log("새 구간별 요약이 추가되었습니다.");
    }
  };

  const canAddSummary = timeRangeInput.trim() && summaryInput.trim() && keyPointsInput.trim();

  return (
    <div className="bg-blue-50 border border-blue-200 rounded-lg p-3 shadow-sm">
      <div className="space-y-4">
        <div className="text-center">
          <Label className="text-sm text-blue-700">요약패널 디버그</Label>
        </div>
        
        {/* 현재 주제 편집 섹션 */}
        <div className="space-y-2">
          <Label className="text-xs text-blue-600">현재 주제 편집:</Label>
          <Input
            value={topicInput}
            onChange={(e) => setTopicInput(e.target.value)}
            onKeyPress={handleTopicKeyPress}
            placeholder="새로운 주제를 입력하세요..."
            className="text-sm bg-white border-blue-200 focus:border-blue-400"
          />
          <div className="flex justify-center">
            <Button
              onClick={handleApplyTopic}
              size="sm"
              className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-1"
            >
              현재 주제 적용
            </Button>
          </div>
        </div>

        <Separator className="bg-blue-200" />

        {/* 구간별 요약 관리 섹션 */}
        <div className="space-y-3">
          <Label className="text-xs text-blue-600">구간별 요약 관리:</Label>
          
          {/* 전체 지우기 버튼 */}
          <div className="flex justify-center">
            <Button
              onClick={handleClearAllSummaries}
              variant="destructive"
              size="sm"
              className="px-3 py-1"
            >
              <Trash2 size={14} className="mr-1" />
              구간별 요약 전체 지우기
            </Button>
          </div>

          {/* 새 요약 추가 폼 */}
          <div className="space-y-2">
            <Label className="text-xs text-blue-600">새 구간별 요약 추가:</Label>
            
            {/* 시간 범위 입력 */}
            <div className="space-y-1">
              <Label className="text-xs text-gray-600">시간 범위:</Label>
              <Input
                value={timeRangeInput}
                onChange={(e) => setTimeRangeInput(e.target.value)}
                placeholder="예: 10:50-10:55"
                className="text-sm bg-white border-blue-200 focus:border-blue-400"
              />
            </div>

            {/* 요약 주제 입력 */}
            <div className="space-y-1">
              <Label className="text-xs text-gray-600">요약 주제:</Label>
              <Input
                value={summaryInput}
                onChange={(e) => setSummaryInput(e.target.value)}
                placeholder="요약 내용을 입력하세요..."
                className="text-sm bg-white border-blue-200 focus:border-blue-400"
              />
            </div>

            {/* 주요 포인트 입력 */}
            <div className="space-y-1">
              <Label className="text-xs text-gray-600">주요 포인트 (각 줄마다 하나씩):</Label>
              <Textarea
                value={keyPointsInput}
                onChange={(e) => setKeyPointsInput(e.target.value)}
                placeholder="주요 포인트를 입력하세요...&#10;각 줄마다 하나의 포인트를 입력하면 됩니다"
                className="text-sm bg-white border-blue-200 focus:border-blue-400 h-16 resize-none"
              />
            </div>

            {/* 추가 버튼 */}
            <div className="flex justify-center pt-2">
              <Button
                onClick={handleAddSummary}
                disabled={!canAddSummary}
                size="sm"
                className="bg-green-600 hover:bg-green-700 disabled:bg-gray-400 text-white px-4 py-1"
              >
                <Plus size={14} className="mr-1" />
                구간별 요약 추가
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}