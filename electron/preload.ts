import { contextBridge, ipc<PERSON>enderer } from 'electron';
import { ipcEvents } from './audio-config.js';

// Expose protected methods that allow the renderer process to use
// the ipcRenderer without exposing the entire object
contextBridge.exposeInMainWorld('electronAPI', {
  // Add any APIs you want to expose to your React app here
  platform: process.platform,
  versions: process.versions,

  // Audio recording APIs
  checkAudioPermissions: () => ipcRenderer.invoke(ipcEvents.checkPermissions),
  requestAudioPermissions: () => ipcRenderer.invoke(ipcEvents.requestPermissions),
  enableLoopbackAudio: () => ipcRenderer.invoke(ipcEvents.enableLoopbackAudio),
  disableLoopbackAudio: () => ipcRenderer.invoke(ipcEvents.disableLoopbackAudio),
  saveAudioFile: (filePath: string, arrayBuffer: ArrayBuffer) =>
    ipcRenderer.invoke(ipcEvents.saveAudioFile, filePath, arrayBuffer),
});

// Remove this line to stop exposing dangerous node APIs to the renderer
// window.eval = global.eval = function () {
//   throw new Error(`Sorry, this app does not support window.eval().`)
// } 