function ay(n,o){for(var l=0;l<o.length;l++){const s=o[l];if(typeof s!="string"&&!Array.isArray(s)){for(const a in s)if(a!=="default"&&!(a in n)){const c=Object.getOwnPropertyDescriptor(s,a);c&&Object.defineProperty(n,a,c.get?c:{enumerable:!0,get:()=>s[a]})}}}return Object.freeze(Object.defineProperty(n,Symbol.toStringTag,{value:"Module"}))}(function(){const o=document.createElement("link").relList;if(o&&o.supports&&o.supports("modulepreload"))return;for(const a of document.querySelectorAll('link[rel="modulepreload"]'))s(a);new MutationObserver(a=>{for(const c of a)if(c.type==="childList")for(const p of c.addedNodes)p.tagName==="LINK"&&p.rel==="modulepreload"&&s(p)}).observe(document,{childList:!0,subtree:!0});function l(a){const c={};return a.integrity&&(c.integrity=a.integrity),a.referrerPolicy&&(c.referrerPolicy=a.referrerPolicy),a.crossOrigin==="use-credentials"?c.credentials="include":a.crossOrigin==="anonymous"?c.credentials="omit":c.credentials="same-origin",c}function s(a){if(a.ep)return;a.ep=!0;const c=l(a);fetch(a.href,c)}})();function Qp(n){return n&&n.__esModule&&Object.prototype.hasOwnProperty.call(n,"default")?n.default:n}var Ua={exports:{}},Go={},Ka={exports:{}},ke={};/**
 * @license React
 * react.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Df;function uy(){if(Df)return ke;Df=1;var n=Symbol.for("react.element"),o=Symbol.for("react.portal"),l=Symbol.for("react.fragment"),s=Symbol.for("react.strict_mode"),a=Symbol.for("react.profiler"),c=Symbol.for("react.provider"),p=Symbol.for("react.context"),f=Symbol.for("react.forward_ref"),m=Symbol.for("react.suspense"),g=Symbol.for("react.memo"),w=Symbol.for("react.lazy"),x=Symbol.iterator;function S(_){return _===null||typeof _!="object"?null:(_=x&&_[x]||_["@@iterator"],typeof _=="function"?_:null)}var E={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},b=Object.assign,C={};function P(_,W,H){this.props=_,this.context=W,this.refs=C,this.updater=H||E}P.prototype.isReactComponent={},P.prototype.setState=function(_,W){if(typeof _!="object"&&typeof _!="function"&&_!=null)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,_,W,"setState")},P.prototype.forceUpdate=function(_){this.updater.enqueueForceUpdate(this,_,"forceUpdate")};function N(){}N.prototype=P.prototype;function j(_,W,H){this.props=_,this.context=W,this.refs=C,this.updater=H||E}var z=j.prototype=new N;z.constructor=j,b(z,P.prototype),z.isPureReactComponent=!0;var M=Array.isArray,D=Object.prototype.hasOwnProperty,V={current:null},O={key:!0,ref:!0,__self:!0,__source:!0};function ee(_,W,H){var G,ne={},te=null,F=null;if(W!=null)for(G in W.ref!==void 0&&(F=W.ref),W.key!==void 0&&(te=""+W.key),W)D.call(W,G)&&!O.hasOwnProperty(G)&&(ne[G]=W[G]);var X=arguments.length-2;if(X===1)ne.children=H;else if(1<X){for(var K=Array(X),oe=0;oe<X;oe++)K[oe]=arguments[oe+2];ne.children=K}if(_&&_.defaultProps)for(G in X=_.defaultProps,X)ne[G]===void 0&&(ne[G]=X[G]);return{$$typeof:n,type:_,key:te,ref:F,props:ne,_owner:V.current}}function re(_,W){return{$$typeof:n,type:_.type,key:W,ref:_.ref,props:_.props,_owner:_._owner}}function ae(_){return typeof _=="object"&&_!==null&&_.$$typeof===n}function pe(_){var W={"=":"=0",":":"=2"};return"$"+_.replace(/[=:]/g,function(H){return W[H]})}var q=/\/+/g;function ve(_,W){return typeof _=="object"&&_!==null&&_.key!=null?pe(""+_.key):W.toString(36)}function se(_,W,H,G,ne){var te=typeof _;(te==="undefined"||te==="boolean")&&(_=null);var F=!1;if(_===null)F=!0;else switch(te){case"string":case"number":F=!0;break;case"object":switch(_.$$typeof){case n:case o:F=!0}}if(F)return F=_,ne=ne(F),_=G===""?"."+ve(F,0):G,M(ne)?(H="",_!=null&&(H=_.replace(q,"$&/")+"/"),se(ne,W,H,"",function(oe){return oe})):ne!=null&&(ae(ne)&&(ne=re(ne,H+(!ne.key||F&&F.key===ne.key?"":(""+ne.key).replace(q,"$&/")+"/")+_)),W.push(ne)),1;if(F=0,G=G===""?".":G+":",M(_))for(var X=0;X<_.length;X++){te=_[X];var K=G+ve(te,X);F+=se(te,W,H,K,ne)}else if(K=S(_),typeof K=="function")for(_=K.call(_),X=0;!(te=_.next()).done;)te=te.value,K=G+ve(te,X++),F+=se(te,W,H,K,ne);else if(te==="object")throw W=String(_),Error("Objects are not valid as a React child (found: "+(W==="[object Object]"?"object with keys {"+Object.keys(_).join(", ")+"}":W)+"). If you meant to render a collection of children, use an array instead.");return F}function ge(_,W,H){if(_==null)return _;var G=[],ne=0;return se(_,G,"","",function(te){return W.call(H,te,ne++)}),G}function le(_){if(_._status===-1){var W=_._result;W=W(),W.then(function(H){(_._status===0||_._status===-1)&&(_._status=1,_._result=H)},function(H){(_._status===0||_._status===-1)&&(_._status=2,_._result=H)}),_._status===-1&&(_._status=0,_._result=W)}if(_._status===1)return _._result.default;throw _._result}var ce={current:null},$={transition:null},J={ReactCurrentDispatcher:ce,ReactCurrentBatchConfig:$,ReactCurrentOwner:V};function Y(){throw Error("act(...) is not supported in production builds of React.")}return ke.Children={map:ge,forEach:function(_,W,H){ge(_,function(){W.apply(this,arguments)},H)},count:function(_){var W=0;return ge(_,function(){W++}),W},toArray:function(_){return ge(_,function(W){return W})||[]},only:function(_){if(!ae(_))throw Error("React.Children.only expected to receive a single React element child.");return _}},ke.Component=P,ke.Fragment=l,ke.Profiler=a,ke.PureComponent=j,ke.StrictMode=s,ke.Suspense=m,ke.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=J,ke.act=Y,ke.cloneElement=function(_,W,H){if(_==null)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+_+".");var G=b({},_.props),ne=_.key,te=_.ref,F=_._owner;if(W!=null){if(W.ref!==void 0&&(te=W.ref,F=V.current),W.key!==void 0&&(ne=""+W.key),_.type&&_.type.defaultProps)var X=_.type.defaultProps;for(K in W)D.call(W,K)&&!O.hasOwnProperty(K)&&(G[K]=W[K]===void 0&&X!==void 0?X[K]:W[K])}var K=arguments.length-2;if(K===1)G.children=H;else if(1<K){X=Array(K);for(var oe=0;oe<K;oe++)X[oe]=arguments[oe+2];G.children=X}return{$$typeof:n,type:_.type,key:ne,ref:te,props:G,_owner:F}},ke.createContext=function(_){return _={$$typeof:p,_currentValue:_,_currentValue2:_,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null},_.Provider={$$typeof:c,_context:_},_.Consumer=_},ke.createElement=ee,ke.createFactory=function(_){var W=ee.bind(null,_);return W.type=_,W},ke.createRef=function(){return{current:null}},ke.forwardRef=function(_){return{$$typeof:f,render:_}},ke.isValidElement=ae,ke.lazy=function(_){return{$$typeof:w,_payload:{_status:-1,_result:_},_init:le}},ke.memo=function(_,W){return{$$typeof:g,type:_,compare:W===void 0?null:W}},ke.startTransition=function(_){var W=$.transition;$.transition={};try{_()}finally{$.transition=W}},ke.unstable_act=Y,ke.useCallback=function(_,W){return ce.current.useCallback(_,W)},ke.useContext=function(_){return ce.current.useContext(_)},ke.useDebugValue=function(){},ke.useDeferredValue=function(_){return ce.current.useDeferredValue(_)},ke.useEffect=function(_,W){return ce.current.useEffect(_,W)},ke.useId=function(){return ce.current.useId()},ke.useImperativeHandle=function(_,W,H){return ce.current.useImperativeHandle(_,W,H)},ke.useInsertionEffect=function(_,W){return ce.current.useInsertionEffect(_,W)},ke.useLayoutEffect=function(_,W){return ce.current.useLayoutEffect(_,W)},ke.useMemo=function(_,W){return ce.current.useMemo(_,W)},ke.useReducer=function(_,W,H){return ce.current.useReducer(_,W,H)},ke.useRef=function(_){return ce.current.useRef(_)},ke.useState=function(_){return ce.current.useState(_)},ke.useSyncExternalStore=function(_,W,H){return ce.current.useSyncExternalStore(_,W,H)},ke.useTransition=function(){return ce.current.useTransition()},ke.version="18.3.1",ke}var $f;function Eu(){return $f||($f=1,Ka.exports=uy()),Ka.exports}/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Ff;function cy(){if(Ff)return Go;Ff=1;var n=Eu(),o=Symbol.for("react.element"),l=Symbol.for("react.fragment"),s=Object.prototype.hasOwnProperty,a=n.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,c={key:!0,ref:!0,__self:!0,__source:!0};function p(f,m,g){var w,x={},S=null,E=null;g!==void 0&&(S=""+g),m.key!==void 0&&(S=""+m.key),m.ref!==void 0&&(E=m.ref);for(w in m)s.call(m,w)&&!c.hasOwnProperty(w)&&(x[w]=m[w]);if(f&&f.defaultProps)for(w in m=f.defaultProps,m)x[w]===void 0&&(x[w]=m[w]);return{$$typeof:o,type:f,key:S,ref:E,props:x,_owner:a.current}}return Go.Fragment=l,Go.jsx=p,Go.jsxs=p,Go}var Wf;function dy(){return Wf||(Wf=1,Ua.exports=cy()),Ua.exports}var v=dy(),h=Eu();const $n=Qp(h),Wi=ay({__proto__:null,default:$n},[h]);var Si={},Qa={exports:{}},pt={},Ga={exports:{}},Ya={};/**
 * @license React
 * scheduler.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Hf;function fy(){return Hf||(Hf=1,function(n){function o($,J){var Y=$.length;$.push(J);e:for(;0<Y;){var _=Y-1>>>1,W=$[_];if(0<a(W,J))$[_]=J,$[Y]=W,Y=_;else break e}}function l($){return $.length===0?null:$[0]}function s($){if($.length===0)return null;var J=$[0],Y=$.pop();if(Y!==J){$[0]=Y;e:for(var _=0,W=$.length,H=W>>>1;_<H;){var G=2*(_+1)-1,ne=$[G],te=G+1,F=$[te];if(0>a(ne,Y))te<W&&0>a(F,ne)?($[_]=F,$[te]=Y,_=te):($[_]=ne,$[G]=Y,_=G);else if(te<W&&0>a(F,Y))$[_]=F,$[te]=Y,_=te;else break e}}return J}function a($,J){var Y=$.sortIndex-J.sortIndex;return Y!==0?Y:$.id-J.id}if(typeof performance=="object"&&typeof performance.now=="function"){var c=performance;n.unstable_now=function(){return c.now()}}else{var p=Date,f=p.now();n.unstable_now=function(){return p.now()-f}}var m=[],g=[],w=1,x=null,S=3,E=!1,b=!1,C=!1,P=typeof setTimeout=="function"?setTimeout:null,N=typeof clearTimeout=="function"?clearTimeout:null,j=typeof setImmediate<"u"?setImmediate:null;typeof navigator<"u"&&navigator.scheduling!==void 0&&navigator.scheduling.isInputPending!==void 0&&navigator.scheduling.isInputPending.bind(navigator.scheduling);function z($){for(var J=l(g);J!==null;){if(J.callback===null)s(g);else if(J.startTime<=$)s(g),J.sortIndex=J.expirationTime,o(m,J);else break;J=l(g)}}function M($){if(C=!1,z($),!b)if(l(m)!==null)b=!0,le(D);else{var J=l(g);J!==null&&ce(M,J.startTime-$)}}function D($,J){b=!1,C&&(C=!1,N(ee),ee=-1),E=!0;var Y=S;try{for(z(J),x=l(m);x!==null&&(!(x.expirationTime>J)||$&&!pe());){var _=x.callback;if(typeof _=="function"){x.callback=null,S=x.priorityLevel;var W=_(x.expirationTime<=J);J=n.unstable_now(),typeof W=="function"?x.callback=W:x===l(m)&&s(m),z(J)}else s(m);x=l(m)}if(x!==null)var H=!0;else{var G=l(g);G!==null&&ce(M,G.startTime-J),H=!1}return H}finally{x=null,S=Y,E=!1}}var V=!1,O=null,ee=-1,re=5,ae=-1;function pe(){return!(n.unstable_now()-ae<re)}function q(){if(O!==null){var $=n.unstable_now();ae=$;var J=!0;try{J=O(!0,$)}finally{J?ve():(V=!1,O=null)}}else V=!1}var ve;if(typeof j=="function")ve=function(){j(q)};else if(typeof MessageChannel<"u"){var se=new MessageChannel,ge=se.port2;se.port1.onmessage=q,ve=function(){ge.postMessage(null)}}else ve=function(){P(q,0)};function le($){O=$,V||(V=!0,ve())}function ce($,J){ee=P(function(){$(n.unstable_now())},J)}n.unstable_IdlePriority=5,n.unstable_ImmediatePriority=1,n.unstable_LowPriority=4,n.unstable_NormalPriority=3,n.unstable_Profiling=null,n.unstable_UserBlockingPriority=2,n.unstable_cancelCallback=function($){$.callback=null},n.unstable_continueExecution=function(){b||E||(b=!0,le(D))},n.unstable_forceFrameRate=function($){0>$||125<$?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):re=0<$?Math.floor(1e3/$):5},n.unstable_getCurrentPriorityLevel=function(){return S},n.unstable_getFirstCallbackNode=function(){return l(m)},n.unstable_next=function($){switch(S){case 1:case 2:case 3:var J=3;break;default:J=S}var Y=S;S=J;try{return $()}finally{S=Y}},n.unstable_pauseExecution=function(){},n.unstable_requestPaint=function(){},n.unstable_runWithPriority=function($,J){switch($){case 1:case 2:case 3:case 4:case 5:break;default:$=3}var Y=S;S=$;try{return J()}finally{S=Y}},n.unstable_scheduleCallback=function($,J,Y){var _=n.unstable_now();switch(typeof Y=="object"&&Y!==null?(Y=Y.delay,Y=typeof Y=="number"&&0<Y?_+Y:_):Y=_,$){case 1:var W=-1;break;case 2:W=250;break;case 5:W=**********;break;case 4:W=1e4;break;default:W=5e3}return W=Y+W,$={id:w++,callback:J,priorityLevel:$,startTime:Y,expirationTime:W,sortIndex:-1},Y>_?($.sortIndex=Y,o(g,$),l(m)===null&&$===l(g)&&(C?(N(ee),ee=-1):C=!0,ce(M,Y-_))):($.sortIndex=W,o(m,$),b||E||(b=!0,le(D))),$},n.unstable_shouldYield=pe,n.unstable_wrapCallback=function($){var J=S;return function(){var Y=S;S=J;try{return $.apply(this,arguments)}finally{S=Y}}}}(Ya)),Ya}var Vf;function py(){return Vf||(Vf=1,Ga.exports=fy()),Ga.exports}/**
 * @license React
 * react-dom.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Bf;function my(){if(Bf)return pt;Bf=1;var n=Eu(),o=py();function l(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,r=1;r<arguments.length;r++)t+="&args[]="+encodeURIComponent(arguments[r]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var s=new Set,a={};function c(e,t){p(e,t),p(e+"Capture",t)}function p(e,t){for(a[e]=t,e=0;e<t.length;e++)s.add(t[e])}var f=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),m=Object.prototype.hasOwnProperty,g=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,w={},x={};function S(e){return m.call(x,e)?!0:m.call(w,e)?!1:g.test(e)?x[e]=!0:(w[e]=!0,!1)}function E(e,t,r,i){if(r!==null&&r.type===0)return!1;switch(typeof t){case"function":case"symbol":return!0;case"boolean":return i?!1:r!==null?!r.acceptsBooleans:(e=e.toLowerCase().slice(0,5),e!=="data-"&&e!=="aria-");default:return!1}}function b(e,t,r,i){if(t===null||typeof t>"u"||E(e,t,r,i))return!0;if(i)return!1;if(r!==null)switch(r.type){case 3:return!t;case 4:return t===!1;case 5:return isNaN(t);case 6:return isNaN(t)||1>t}return!1}function C(e,t,r,i,u,d,y){this.acceptsBooleans=t===2||t===3||t===4,this.attributeName=i,this.attributeNamespace=u,this.mustUseProperty=r,this.propertyName=e,this.type=t,this.sanitizeURL=d,this.removeEmptyString=y}var P={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach(function(e){P[e]=new C(e,0,!1,e,null,!1,!1)}),[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach(function(e){var t=e[0];P[t]=new C(t,1,!1,e[1],null,!1,!1)}),["contentEditable","draggable","spellCheck","value"].forEach(function(e){P[e]=new C(e,2,!1,e.toLowerCase(),null,!1,!1)}),["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach(function(e){P[e]=new C(e,2,!1,e,null,!1,!1)}),"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach(function(e){P[e]=new C(e,3,!1,e.toLowerCase(),null,!1,!1)}),["checked","multiple","muted","selected"].forEach(function(e){P[e]=new C(e,3,!0,e,null,!1,!1)}),["capture","download"].forEach(function(e){P[e]=new C(e,4,!1,e,null,!1,!1)}),["cols","rows","size","span"].forEach(function(e){P[e]=new C(e,6,!1,e,null,!1,!1)}),["rowSpan","start"].forEach(function(e){P[e]=new C(e,5,!1,e.toLowerCase(),null,!1,!1)});var N=/[\-:]([a-z])/g;function j(e){return e[1].toUpperCase()}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach(function(e){var t=e.replace(N,j);P[t]=new C(t,1,!1,e,null,!1,!1)}),"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach(function(e){var t=e.replace(N,j);P[t]=new C(t,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)}),["xml:base","xml:lang","xml:space"].forEach(function(e){var t=e.replace(N,j);P[t]=new C(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)}),["tabIndex","crossOrigin"].forEach(function(e){P[e]=new C(e,1,!1,e.toLowerCase(),null,!1,!1)}),P.xlinkHref=new C("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1),["src","href","action","formAction"].forEach(function(e){P[e]=new C(e,1,!1,e.toLowerCase(),null,!0,!0)});function z(e,t,r,i){var u=P.hasOwnProperty(t)?P[t]:null;(u!==null?u.type!==0:i||!(2<t.length)||t[0]!=="o"&&t[0]!=="O"||t[1]!=="n"&&t[1]!=="N")&&(b(t,r,u,i)&&(r=null),i||u===null?S(t)&&(r===null?e.removeAttribute(t):e.setAttribute(t,""+r)):u.mustUseProperty?e[u.propertyName]=r===null?u.type===3?!1:"":r:(t=u.attributeName,i=u.attributeNamespace,r===null?e.removeAttribute(t):(u=u.type,r=u===3||u===4&&r===!0?"":""+r,i?e.setAttributeNS(i,t,r):e.setAttribute(t,r))))}var M=n.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,D=Symbol.for("react.element"),V=Symbol.for("react.portal"),O=Symbol.for("react.fragment"),ee=Symbol.for("react.strict_mode"),re=Symbol.for("react.profiler"),ae=Symbol.for("react.provider"),pe=Symbol.for("react.context"),q=Symbol.for("react.forward_ref"),ve=Symbol.for("react.suspense"),se=Symbol.for("react.suspense_list"),ge=Symbol.for("react.memo"),le=Symbol.for("react.lazy"),ce=Symbol.for("react.offscreen"),$=Symbol.iterator;function J(e){return e===null||typeof e!="object"?null:(e=$&&e[$]||e["@@iterator"],typeof e=="function"?e:null)}var Y=Object.assign,_;function W(e){if(_===void 0)try{throw Error()}catch(r){var t=r.stack.trim().match(/\n( *(at )?)/);_=t&&t[1]||""}return`
`+_+e}var H=!1;function G(e,t){if(!e||H)return"";H=!0;var r=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(t)if(t=function(){throw Error()},Object.defineProperty(t.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(t,[])}catch(I){var i=I}Reflect.construct(e,[],t)}else{try{t.call()}catch(I){i=I}e.call(t.prototype)}else{try{throw Error()}catch(I){i=I}e()}}catch(I){if(I&&i&&typeof I.stack=="string"){for(var u=I.stack.split(`
`),d=i.stack.split(`
`),y=u.length-1,k=d.length-1;1<=y&&0<=k&&u[y]!==d[k];)k--;for(;1<=y&&0<=k;y--,k--)if(u[y]!==d[k]){if(y!==1||k!==1)do if(y--,k--,0>k||u[y]!==d[k]){var R=`
`+u[y].replace(" at new "," at ");return e.displayName&&R.includes("<anonymous>")&&(R=R.replace("<anonymous>",e.displayName)),R}while(1<=y&&0<=k);break}}}finally{H=!1,Error.prepareStackTrace=r}return(e=e?e.displayName||e.name:"")?W(e):""}function ne(e){switch(e.tag){case 5:return W(e.type);case 16:return W("Lazy");case 13:return W("Suspense");case 19:return W("SuspenseList");case 0:case 2:case 15:return e=G(e.type,!1),e;case 11:return e=G(e.type.render,!1),e;case 1:return e=G(e.type,!0),e;default:return""}}function te(e){if(e==null)return null;if(typeof e=="function")return e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case O:return"Fragment";case V:return"Portal";case re:return"Profiler";case ee:return"StrictMode";case ve:return"Suspense";case se:return"SuspenseList"}if(typeof e=="object")switch(e.$$typeof){case pe:return(e.displayName||"Context")+".Consumer";case ae:return(e._context.displayName||"Context")+".Provider";case q:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case ge:return t=e.displayName||null,t!==null?t:te(e.type)||"Memo";case le:t=e._payload,e=e._init;try{return te(e(t))}catch{}}return null}function F(e){var t=e.type;switch(e.tag){case 24:return"Cache";case 9:return(t.displayName||"Context")+".Consumer";case 10:return(t._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return e=t.render,e=e.displayName||e.name||"",t.displayName||(e!==""?"ForwardRef("+e+")":"ForwardRef");case 7:return"Fragment";case 5:return t;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return te(t);case 8:return t===ee?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if(typeof t=="function")return t.displayName||t.name||null;if(typeof t=="string")return t}return null}function X(e){switch(typeof e){case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function K(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function oe(e){var t=K(e)?"checked":"value",r=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),i=""+e[t];if(!e.hasOwnProperty(t)&&typeof r<"u"&&typeof r.get=="function"&&typeof r.set=="function"){var u=r.get,d=r.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return u.call(this)},set:function(y){i=""+y,d.call(this,y)}}),Object.defineProperty(e,t,{enumerable:r.enumerable}),{getValue:function(){return i},setValue:function(y){i=""+y},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function me(e){e._valueTracker||(e._valueTracker=oe(e))}function ye(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var r=t.getValue(),i="";return e&&(i=K(e)?e.checked?"true":"false":e.value),e=i,e!==r?(t.setValue(e),!0):!1}function Se(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}function _e(e,t){var r=t.checked;return Y({},t,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:r??e._wrapperState.initialChecked})}function st(e,t){var r=t.defaultValue==null?"":t.defaultValue,i=t.checked!=null?t.checked:t.defaultChecked;r=X(t.value!=null?t.value:r),e._wrapperState={initialChecked:i,initialValue:r,controlled:t.type==="checkbox"||t.type==="radio"?t.checked!=null:t.value!=null}}function gt(e,t){t=t.checked,t!=null&&z(e,"checked",t,!1)}function vt(e,t){gt(e,t);var r=X(t.value),i=t.type;if(r!=null)i==="number"?(r===0&&e.value===""||e.value!=r)&&(e.value=""+r):e.value!==""+r&&(e.value=""+r);else if(i==="submit"||i==="reset"){e.removeAttribute("value");return}t.hasOwnProperty("value")?Mt(e,t.type,r):t.hasOwnProperty("defaultValue")&&Mt(e,t.type,X(t.defaultValue)),t.checked==null&&t.defaultChecked!=null&&(e.defaultChecked=!!t.defaultChecked)}function Ue(e,t,r){if(t.hasOwnProperty("value")||t.hasOwnProperty("defaultValue")){var i=t.type;if(!(i!=="submit"&&i!=="reset"||t.value!==void 0&&t.value!==null))return;t=""+e._wrapperState.initialValue,r||t===e.value||(e.value=t),e.defaultValue=t}r=e.name,r!==""&&(e.name=""),e.defaultChecked=!!e._wrapperState.initialChecked,r!==""&&(e.name=r)}function Mt(e,t,r){(t!=="number"||Se(e.ownerDocument)!==e)&&(r==null?e.defaultValue=""+e._wrapperState.initialValue:e.defaultValue!==""+r&&(e.defaultValue=""+r))}var Kn=Array.isArray;function kt(e,t,r,i){if(e=e.options,t){t={};for(var u=0;u<r.length;u++)t["$"+r[u]]=!0;for(r=0;r<e.length;r++)u=t.hasOwnProperty("$"+e[r].value),e[r].selected!==u&&(e[r].selected=u),u&&i&&(e[r].defaultSelected=!0)}else{for(r=""+X(r),t=null,u=0;u<e.length;u++){if(e[u].value===r){e[u].selected=!0,i&&(e[u].defaultSelected=!0);return}t!==null||e[u].disabled||(t=e[u])}t!==null&&(t.selected=!0)}}function vr(e,t){if(t.dangerouslySetInnerHTML!=null)throw Error(l(91));return Y({},t,{value:void 0,defaultValue:void 0,children:""+e._wrapperState.initialValue})}function fl(e,t){var r=t.value;if(r==null){if(r=t.children,t=t.defaultValue,r!=null){if(t!=null)throw Error(l(92));if(Kn(r)){if(1<r.length)throw Error(l(93));r=r[0]}t=r}t==null&&(t=""),r=t}e._wrapperState={initialValue:X(r)}}function Gu(e,t){var r=X(t.value),i=X(t.defaultValue);r!=null&&(r=""+r,r!==e.value&&(e.value=r),t.defaultValue==null&&e.defaultValue!==r&&(e.defaultValue=r)),i!=null&&(e.defaultValue=""+i)}function Yu(e){var t=e.textContent;t===e._wrapperState.initialValue&&t!==""&&t!==null&&(e.value=t)}function Xu(e){switch(e){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function ns(e,t){return e==null||e==="http://www.w3.org/1999/xhtml"?Xu(t):e==="http://www.w3.org/2000/svg"&&t==="foreignObject"?"http://www.w3.org/1999/xhtml":e}var pl,Zu=function(e){return typeof MSApp<"u"&&MSApp.execUnsafeLocalFunction?function(t,r,i,u){MSApp.execUnsafeLocalFunction(function(){return e(t,r,i,u)})}:e}(function(e,t){if(e.namespaceURI!=="http://www.w3.org/2000/svg"||"innerHTML"in e)e.innerHTML=t;else{for(pl=pl||document.createElement("div"),pl.innerHTML="<svg>"+t.valueOf().toString()+"</svg>",t=pl.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;t.firstChild;)e.appendChild(t.firstChild)}});function uo(e,t){if(t){var r=e.firstChild;if(r&&r===e.lastChild&&r.nodeType===3){r.nodeValue=t;return}}e.textContent=t}var co={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},fg=["Webkit","ms","Moz","O"];Object.keys(co).forEach(function(e){fg.forEach(function(t){t=t+e.charAt(0).toUpperCase()+e.substring(1),co[t]=co[e]})});function qu(e,t,r){return t==null||typeof t=="boolean"||t===""?"":r||typeof t!="number"||t===0||co.hasOwnProperty(e)&&co[e]?(""+t).trim():t+"px"}function Ju(e,t){e=e.style;for(var r in t)if(t.hasOwnProperty(r)){var i=r.indexOf("--")===0,u=qu(r,t[r],i);r==="float"&&(r="cssFloat"),i?e.setProperty(r,u):e[r]=u}}var pg=Y({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function rs(e,t){if(t){if(pg[e]&&(t.children!=null||t.dangerouslySetInnerHTML!=null))throw Error(l(137,e));if(t.dangerouslySetInnerHTML!=null){if(t.children!=null)throw Error(l(60));if(typeof t.dangerouslySetInnerHTML!="object"||!("__html"in t.dangerouslySetInnerHTML))throw Error(l(61))}if(t.style!=null&&typeof t.style!="object")throw Error(l(62))}}function os(e,t){if(e.indexOf("-")===-1)return typeof t.is=="string";switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var ls=null;function is(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var ss=null,yr=null,xr=null;function ec(e){if(e=Ao(e)){if(typeof ss!="function")throw Error(l(280));var t=e.stateNode;t&&(t=Il(t),ss(e.stateNode,e.type,t))}}function tc(e){yr?xr?xr.push(e):xr=[e]:yr=e}function nc(){if(yr){var e=yr,t=xr;if(xr=yr=null,ec(e),t)for(e=0;e<t.length;e++)ec(t[e])}}function rc(e,t){return e(t)}function oc(){}var as=!1;function lc(e,t,r){if(as)return e(t,r);as=!0;try{return rc(e,t,r)}finally{as=!1,(yr!==null||xr!==null)&&(oc(),nc())}}function fo(e,t){var r=e.stateNode;if(r===null)return null;var i=Il(r);if(i===null)return null;r=i[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(i=!i.disabled)||(e=e.type,i=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!i;break e;default:e=!1}if(e)return null;if(r&&typeof r!="function")throw Error(l(231,t,typeof r));return r}var us=!1;if(f)try{var po={};Object.defineProperty(po,"passive",{get:function(){us=!0}}),window.addEventListener("test",po,po),window.removeEventListener("test",po,po)}catch{us=!1}function mg(e,t,r,i,u,d,y,k,R){var I=Array.prototype.slice.call(arguments,3);try{t.apply(r,I)}catch(U){this.onError(U)}}var mo=!1,ml=null,hl=!1,cs=null,hg={onError:function(e){mo=!0,ml=e}};function gg(e,t,r,i,u,d,y,k,R){mo=!1,ml=null,mg.apply(hg,arguments)}function vg(e,t,r,i,u,d,y,k,R){if(gg.apply(this,arguments),mo){if(mo){var I=ml;mo=!1,ml=null}else throw Error(l(198));hl||(hl=!0,cs=I)}}function Qn(e){var t=e,r=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,(t.flags&4098)!==0&&(r=t.return),e=t.return;while(e)}return t.tag===3?r:null}function ic(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function sc(e){if(Qn(e)!==e)throw Error(l(188))}function yg(e){var t=e.alternate;if(!t){if(t=Qn(e),t===null)throw Error(l(188));return t!==e?null:e}for(var r=e,i=t;;){var u=r.return;if(u===null)break;var d=u.alternate;if(d===null){if(i=u.return,i!==null){r=i;continue}break}if(u.child===d.child){for(d=u.child;d;){if(d===r)return sc(u),e;if(d===i)return sc(u),t;d=d.sibling}throw Error(l(188))}if(r.return!==i.return)r=u,i=d;else{for(var y=!1,k=u.child;k;){if(k===r){y=!0,r=u,i=d;break}if(k===i){y=!0,i=u,r=d;break}k=k.sibling}if(!y){for(k=d.child;k;){if(k===r){y=!0,r=d,i=u;break}if(k===i){y=!0,i=d,r=u;break}k=k.sibling}if(!y)throw Error(l(189))}}if(r.alternate!==i)throw Error(l(190))}if(r.tag!==3)throw Error(l(188));return r.stateNode.current===r?e:t}function ac(e){return e=yg(e),e!==null?uc(e):null}function uc(e){if(e.tag===5||e.tag===6)return e;for(e=e.child;e!==null;){var t=uc(e);if(t!==null)return t;e=e.sibling}return null}var cc=o.unstable_scheduleCallback,dc=o.unstable_cancelCallback,xg=o.unstable_shouldYield,wg=o.unstable_requestPaint,$e=o.unstable_now,Sg=o.unstable_getCurrentPriorityLevel,ds=o.unstable_ImmediatePriority,fc=o.unstable_UserBlockingPriority,gl=o.unstable_NormalPriority,Cg=o.unstable_LowPriority,pc=o.unstable_IdlePriority,vl=null,Qt=null;function Eg(e){if(Qt&&typeof Qt.onCommitFiberRoot=="function")try{Qt.onCommitFiberRoot(vl,e,void 0,(e.current.flags&128)===128)}catch{}}var It=Math.clz32?Math.clz32:Pg,bg=Math.log,kg=Math.LN2;function Pg(e){return e>>>=0,e===0?32:31-(bg(e)/kg|0)|0}var yl=64,xl=4194304;function ho(e){switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194240;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return e&130023424;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return e}}function wl(e,t){var r=e.pendingLanes;if(r===0)return 0;var i=0,u=e.suspendedLanes,d=e.pingedLanes,y=r&268435455;if(y!==0){var k=y&~u;k!==0?i=ho(k):(d&=y,d!==0&&(i=ho(d)))}else y=r&~u,y!==0?i=ho(y):d!==0&&(i=ho(d));if(i===0)return 0;if(t!==0&&t!==i&&(t&u)===0&&(u=i&-i,d=t&-t,u>=d||u===16&&(d&4194240)!==0))return t;if((i&4)!==0&&(i|=r&16),t=e.entangledLanes,t!==0)for(e=e.entanglements,t&=i;0<t;)r=31-It(t),u=1<<r,i|=e[r],t&=~u;return i}function Ng(e,t){switch(e){case 1:case 2:case 4:return t+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return-1;case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function Rg(e,t){for(var r=e.suspendedLanes,i=e.pingedLanes,u=e.expirationTimes,d=e.pendingLanes;0<d;){var y=31-It(d),k=1<<y,R=u[y];R===-1?((k&r)===0||(k&i)!==0)&&(u[y]=Ng(k,t)):R<=t&&(e.expiredLanes|=k),d&=~k}}function fs(e){return e=e.pendingLanes&-1073741825,e!==0?e:e&1073741824?1073741824:0}function mc(){var e=yl;return yl<<=1,(yl&4194240)===0&&(yl=64),e}function ps(e){for(var t=[],r=0;31>r;r++)t.push(e);return t}function go(e,t,r){e.pendingLanes|=t,t!==536870912&&(e.suspendedLanes=0,e.pingedLanes=0),e=e.eventTimes,t=31-It(t),e[t]=r}function _g(e,t){var r=e.pendingLanes&~t;e.pendingLanes=t,e.suspendedLanes=0,e.pingedLanes=0,e.expiredLanes&=t,e.mutableReadLanes&=t,e.entangledLanes&=t,t=e.entanglements;var i=e.eventTimes;for(e=e.expirationTimes;0<r;){var u=31-It(r),d=1<<u;t[u]=0,i[u]=-1,e[u]=-1,r&=~d}}function ms(e,t){var r=e.entangledLanes|=t;for(e=e.entanglements;r;){var i=31-It(r),u=1<<i;u&t|e[i]&t&&(e[i]|=t),r&=~u}}var Re=0;function hc(e){return e&=-e,1<e?4<e?(e&268435455)!==0?16:536870912:4:1}var gc,hs,vc,yc,xc,gs=!1,Sl=[],vn=null,yn=null,xn=null,vo=new Map,yo=new Map,wn=[],Tg="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function wc(e,t){switch(e){case"focusin":case"focusout":vn=null;break;case"dragenter":case"dragleave":yn=null;break;case"mouseover":case"mouseout":xn=null;break;case"pointerover":case"pointerout":vo.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":yo.delete(t.pointerId)}}function xo(e,t,r,i,u,d){return e===null||e.nativeEvent!==d?(e={blockedOn:t,domEventName:r,eventSystemFlags:i,nativeEvent:d,targetContainers:[u]},t!==null&&(t=Ao(t),t!==null&&hs(t)),e):(e.eventSystemFlags|=i,t=e.targetContainers,u!==null&&t.indexOf(u)===-1&&t.push(u),e)}function jg(e,t,r,i,u){switch(t){case"focusin":return vn=xo(vn,e,t,r,i,u),!0;case"dragenter":return yn=xo(yn,e,t,r,i,u),!0;case"mouseover":return xn=xo(xn,e,t,r,i,u),!0;case"pointerover":var d=u.pointerId;return vo.set(d,xo(vo.get(d)||null,e,t,r,i,u)),!0;case"gotpointercapture":return d=u.pointerId,yo.set(d,xo(yo.get(d)||null,e,t,r,i,u)),!0}return!1}function Sc(e){var t=Gn(e.target);if(t!==null){var r=Qn(t);if(r!==null){if(t=r.tag,t===13){if(t=ic(r),t!==null){e.blockedOn=t,xc(e.priority,function(){vc(r)});return}}else if(t===3&&r.stateNode.current.memoizedState.isDehydrated){e.blockedOn=r.tag===3?r.stateNode.containerInfo:null;return}}}e.blockedOn=null}function Cl(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var r=ys(e.domEventName,e.eventSystemFlags,t[0],e.nativeEvent);if(r===null){r=e.nativeEvent;var i=new r.constructor(r.type,r);ls=i,r.target.dispatchEvent(i),ls=null}else return t=Ao(r),t!==null&&hs(t),e.blockedOn=r,!1;t.shift()}return!0}function Cc(e,t,r){Cl(e)&&r.delete(t)}function zg(){gs=!1,vn!==null&&Cl(vn)&&(vn=null),yn!==null&&Cl(yn)&&(yn=null),xn!==null&&Cl(xn)&&(xn=null),vo.forEach(Cc),yo.forEach(Cc)}function wo(e,t){e.blockedOn===t&&(e.blockedOn=null,gs||(gs=!0,o.unstable_scheduleCallback(o.unstable_NormalPriority,zg)))}function So(e){function t(u){return wo(u,e)}if(0<Sl.length){wo(Sl[0],e);for(var r=1;r<Sl.length;r++){var i=Sl[r];i.blockedOn===e&&(i.blockedOn=null)}}for(vn!==null&&wo(vn,e),yn!==null&&wo(yn,e),xn!==null&&wo(xn,e),vo.forEach(t),yo.forEach(t),r=0;r<wn.length;r++)i=wn[r],i.blockedOn===e&&(i.blockedOn=null);for(;0<wn.length&&(r=wn[0],r.blockedOn===null);)Sc(r),r.blockedOn===null&&wn.shift()}var wr=M.ReactCurrentBatchConfig,El=!0;function Ag(e,t,r,i){var u=Re,d=wr.transition;wr.transition=null;try{Re=1,vs(e,t,r,i)}finally{Re=u,wr.transition=d}}function Lg(e,t,r,i){var u=Re,d=wr.transition;wr.transition=null;try{Re=4,vs(e,t,r,i)}finally{Re=u,wr.transition=d}}function vs(e,t,r,i){if(El){var u=ys(e,t,r,i);if(u===null)Ms(e,t,i,bl,r),wc(e,i);else if(jg(u,e,t,r,i))i.stopPropagation();else if(wc(e,i),t&4&&-1<Tg.indexOf(e)){for(;u!==null;){var d=Ao(u);if(d!==null&&gc(d),d=ys(e,t,r,i),d===null&&Ms(e,t,i,bl,r),d===u)break;u=d}u!==null&&i.stopPropagation()}else Ms(e,t,i,null,r)}}var bl=null;function ys(e,t,r,i){if(bl=null,e=is(i),e=Gn(e),e!==null)if(t=Qn(e),t===null)e=null;else if(r=t.tag,r===13){if(e=ic(t),e!==null)return e;e=null}else if(r===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null);return bl=e,null}function Ec(e){switch(e){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(Sg()){case ds:return 1;case fc:return 4;case gl:case Cg:return 16;case pc:return 536870912;default:return 16}default:return 16}}var Sn=null,xs=null,kl=null;function bc(){if(kl)return kl;var e,t=xs,r=t.length,i,u="value"in Sn?Sn.value:Sn.textContent,d=u.length;for(e=0;e<r&&t[e]===u[e];e++);var y=r-e;for(i=1;i<=y&&t[r-i]===u[d-i];i++);return kl=u.slice(e,1<i?1-i:void 0)}function Pl(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function Nl(){return!0}function kc(){return!1}function yt(e){function t(r,i,u,d,y){this._reactName=r,this._targetInst=u,this.type=i,this.nativeEvent=d,this.target=y,this.currentTarget=null;for(var k in e)e.hasOwnProperty(k)&&(r=e[k],this[k]=r?r(d):d[k]);return this.isDefaultPrevented=(d.defaultPrevented!=null?d.defaultPrevented:d.returnValue===!1)?Nl:kc,this.isPropagationStopped=kc,this}return Y(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var r=this.nativeEvent;r&&(r.preventDefault?r.preventDefault():typeof r.returnValue!="unknown"&&(r.returnValue=!1),this.isDefaultPrevented=Nl)},stopPropagation:function(){var r=this.nativeEvent;r&&(r.stopPropagation?r.stopPropagation():typeof r.cancelBubble!="unknown"&&(r.cancelBubble=!0),this.isPropagationStopped=Nl)},persist:function(){},isPersistent:Nl}),t}var Sr={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},ws=yt(Sr),Co=Y({},Sr,{view:0,detail:0}),Mg=yt(Co),Ss,Cs,Eo,Rl=Y({},Co,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:bs,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==Eo&&(Eo&&e.type==="mousemove"?(Ss=e.screenX-Eo.screenX,Cs=e.screenY-Eo.screenY):Cs=Ss=0,Eo=e),Ss)},movementY:function(e){return"movementY"in e?e.movementY:Cs}}),Pc=yt(Rl),Ig=Y({},Rl,{dataTransfer:0}),Og=yt(Ig),Dg=Y({},Co,{relatedTarget:0}),Es=yt(Dg),$g=Y({},Sr,{animationName:0,elapsedTime:0,pseudoElement:0}),Fg=yt($g),Wg=Y({},Sr,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),Hg=yt(Wg),Vg=Y({},Sr,{data:0}),Nc=yt(Vg),Bg={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},Ug={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},Kg={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function Qg(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=Kg[e])?!!t[e]:!1}function bs(){return Qg}var Gg=Y({},Co,{key:function(e){if(e.key){var t=Bg[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=Pl(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?Ug[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:bs,charCode:function(e){return e.type==="keypress"?Pl(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?Pl(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),Yg=yt(Gg),Xg=Y({},Rl,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),Rc=yt(Xg),Zg=Y({},Co,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:bs}),qg=yt(Zg),Jg=Y({},Sr,{propertyName:0,elapsedTime:0,pseudoElement:0}),ev=yt(Jg),tv=Y({},Rl,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),nv=yt(tv),rv=[9,13,27,32],ks=f&&"CompositionEvent"in window,bo=null;f&&"documentMode"in document&&(bo=document.documentMode);var ov=f&&"TextEvent"in window&&!bo,_c=f&&(!ks||bo&&8<bo&&11>=bo),Tc=" ",jc=!1;function zc(e,t){switch(e){case"keyup":return rv.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function Ac(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var Cr=!1;function lv(e,t){switch(e){case"compositionend":return Ac(t);case"keypress":return t.which!==32?null:(jc=!0,Tc);case"textInput":return e=t.data,e===Tc&&jc?null:e;default:return null}}function iv(e,t){if(Cr)return e==="compositionend"||!ks&&zc(e,t)?(e=bc(),kl=xs=Sn=null,Cr=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return _c&&t.locale!=="ko"?null:t.data;default:return null}}var sv={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function Lc(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!sv[e.type]:t==="textarea"}function Mc(e,t,r,i){tc(i),t=Al(t,"onChange"),0<t.length&&(r=new ws("onChange","change",null,r,i),e.push({event:r,listeners:t}))}var ko=null,Po=null;function av(e){Jc(e,0)}function _l(e){var t=Nr(e);if(ye(t))return e}function uv(e,t){if(e==="change")return t}var Ic=!1;if(f){var Ps;if(f){var Ns="oninput"in document;if(!Ns){var Oc=document.createElement("div");Oc.setAttribute("oninput","return;"),Ns=typeof Oc.oninput=="function"}Ps=Ns}else Ps=!1;Ic=Ps&&(!document.documentMode||9<document.documentMode)}function Dc(){ko&&(ko.detachEvent("onpropertychange",$c),Po=ko=null)}function $c(e){if(e.propertyName==="value"&&_l(Po)){var t=[];Mc(t,Po,e,is(e)),lc(av,t)}}function cv(e,t,r){e==="focusin"?(Dc(),ko=t,Po=r,ko.attachEvent("onpropertychange",$c)):e==="focusout"&&Dc()}function dv(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return _l(Po)}function fv(e,t){if(e==="click")return _l(t)}function pv(e,t){if(e==="input"||e==="change")return _l(t)}function mv(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var Ot=typeof Object.is=="function"?Object.is:mv;function No(e,t){if(Ot(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var r=Object.keys(e),i=Object.keys(t);if(r.length!==i.length)return!1;for(i=0;i<r.length;i++){var u=r[i];if(!m.call(t,u)||!Ot(e[u],t[u]))return!1}return!0}function Fc(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function Wc(e,t){var r=Fc(e);e=0;for(var i;r;){if(r.nodeType===3){if(i=e+r.textContent.length,e<=t&&i>=t)return{node:r,offset:t-e};e=i}e:{for(;r;){if(r.nextSibling){r=r.nextSibling;break e}r=r.parentNode}r=void 0}r=Fc(r)}}function Hc(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?Hc(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function Vc(){for(var e=window,t=Se();t instanceof e.HTMLIFrameElement;){try{var r=typeof t.contentWindow.location.href=="string"}catch{r=!1}if(r)e=t.contentWindow;else break;t=Se(e.document)}return t}function Rs(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}function hv(e){var t=Vc(),r=e.focusedElem,i=e.selectionRange;if(t!==r&&r&&r.ownerDocument&&Hc(r.ownerDocument.documentElement,r)){if(i!==null&&Rs(r)){if(t=i.start,e=i.end,e===void 0&&(e=t),"selectionStart"in r)r.selectionStart=t,r.selectionEnd=Math.min(e,r.value.length);else if(e=(t=r.ownerDocument||document)&&t.defaultView||window,e.getSelection){e=e.getSelection();var u=r.textContent.length,d=Math.min(i.start,u);i=i.end===void 0?d:Math.min(i.end,u),!e.extend&&d>i&&(u=i,i=d,d=u),u=Wc(r,d);var y=Wc(r,i);u&&y&&(e.rangeCount!==1||e.anchorNode!==u.node||e.anchorOffset!==u.offset||e.focusNode!==y.node||e.focusOffset!==y.offset)&&(t=t.createRange(),t.setStart(u.node,u.offset),e.removeAllRanges(),d>i?(e.addRange(t),e.extend(y.node,y.offset)):(t.setEnd(y.node,y.offset),e.addRange(t)))}}for(t=[],e=r;e=e.parentNode;)e.nodeType===1&&t.push({element:e,left:e.scrollLeft,top:e.scrollTop});for(typeof r.focus=="function"&&r.focus(),r=0;r<t.length;r++)e=t[r],e.element.scrollLeft=e.left,e.element.scrollTop=e.top}}var gv=f&&"documentMode"in document&&11>=document.documentMode,Er=null,_s=null,Ro=null,Ts=!1;function Bc(e,t,r){var i=r.window===r?r.document:r.nodeType===9?r:r.ownerDocument;Ts||Er==null||Er!==Se(i)||(i=Er,"selectionStart"in i&&Rs(i)?i={start:i.selectionStart,end:i.selectionEnd}:(i=(i.ownerDocument&&i.ownerDocument.defaultView||window).getSelection(),i={anchorNode:i.anchorNode,anchorOffset:i.anchorOffset,focusNode:i.focusNode,focusOffset:i.focusOffset}),Ro&&No(Ro,i)||(Ro=i,i=Al(_s,"onSelect"),0<i.length&&(t=new ws("onSelect","select",null,t,r),e.push({event:t,listeners:i}),t.target=Er)))}function Tl(e,t){var r={};return r[e.toLowerCase()]=t.toLowerCase(),r["Webkit"+e]="webkit"+t,r["Moz"+e]="moz"+t,r}var br={animationend:Tl("Animation","AnimationEnd"),animationiteration:Tl("Animation","AnimationIteration"),animationstart:Tl("Animation","AnimationStart"),transitionend:Tl("Transition","TransitionEnd")},js={},Uc={};f&&(Uc=document.createElement("div").style,"AnimationEvent"in window||(delete br.animationend.animation,delete br.animationiteration.animation,delete br.animationstart.animation),"TransitionEvent"in window||delete br.transitionend.transition);function jl(e){if(js[e])return js[e];if(!br[e])return e;var t=br[e],r;for(r in t)if(t.hasOwnProperty(r)&&r in Uc)return js[e]=t[r];return e}var Kc=jl("animationend"),Qc=jl("animationiteration"),Gc=jl("animationstart"),Yc=jl("transitionend"),Xc=new Map,Zc="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function Cn(e,t){Xc.set(e,t),c(t,[e])}for(var zs=0;zs<Zc.length;zs++){var As=Zc[zs],vv=As.toLowerCase(),yv=As[0].toUpperCase()+As.slice(1);Cn(vv,"on"+yv)}Cn(Kc,"onAnimationEnd"),Cn(Qc,"onAnimationIteration"),Cn(Gc,"onAnimationStart"),Cn("dblclick","onDoubleClick"),Cn("focusin","onFocus"),Cn("focusout","onBlur"),Cn(Yc,"onTransitionEnd"),p("onMouseEnter",["mouseout","mouseover"]),p("onMouseLeave",["mouseout","mouseover"]),p("onPointerEnter",["pointerout","pointerover"]),p("onPointerLeave",["pointerout","pointerover"]),c("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),c("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),c("onBeforeInput",["compositionend","keypress","textInput","paste"]),c("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),c("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),c("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var _o="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),xv=new Set("cancel close invalid load scroll toggle".split(" ").concat(_o));function qc(e,t,r){var i=e.type||"unknown-event";e.currentTarget=r,vg(i,t,void 0,e),e.currentTarget=null}function Jc(e,t){t=(t&4)!==0;for(var r=0;r<e.length;r++){var i=e[r],u=i.event;i=i.listeners;e:{var d=void 0;if(t)for(var y=i.length-1;0<=y;y--){var k=i[y],R=k.instance,I=k.currentTarget;if(k=k.listener,R!==d&&u.isPropagationStopped())break e;qc(u,k,I),d=R}else for(y=0;y<i.length;y++){if(k=i[y],R=k.instance,I=k.currentTarget,k=k.listener,R!==d&&u.isPropagationStopped())break e;qc(u,k,I),d=R}}}if(hl)throw e=cs,hl=!1,cs=null,e}function je(e,t){var r=t[Ws];r===void 0&&(r=t[Ws]=new Set);var i=e+"__bubble";r.has(i)||(ed(t,e,2,!1),r.add(i))}function Ls(e,t,r){var i=0;t&&(i|=4),ed(r,e,i,t)}var zl="_reactListening"+Math.random().toString(36).slice(2);function To(e){if(!e[zl]){e[zl]=!0,s.forEach(function(r){r!=="selectionchange"&&(xv.has(r)||Ls(r,!1,e),Ls(r,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[zl]||(t[zl]=!0,Ls("selectionchange",!1,t))}}function ed(e,t,r,i){switch(Ec(t)){case 1:var u=Ag;break;case 4:u=Lg;break;default:u=vs}r=u.bind(null,t,r,e),u=void 0,!us||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(u=!0),i?u!==void 0?e.addEventListener(t,r,{capture:!0,passive:u}):e.addEventListener(t,r,!0):u!==void 0?e.addEventListener(t,r,{passive:u}):e.addEventListener(t,r,!1)}function Ms(e,t,r,i,u){var d=i;if((t&1)===0&&(t&2)===0&&i!==null)e:for(;;){if(i===null)return;var y=i.tag;if(y===3||y===4){var k=i.stateNode.containerInfo;if(k===u||k.nodeType===8&&k.parentNode===u)break;if(y===4)for(y=i.return;y!==null;){var R=y.tag;if((R===3||R===4)&&(R=y.stateNode.containerInfo,R===u||R.nodeType===8&&R.parentNode===u))return;y=y.return}for(;k!==null;){if(y=Gn(k),y===null)return;if(R=y.tag,R===5||R===6){i=d=y;continue e}k=k.parentNode}}i=i.return}lc(function(){var I=d,U=is(r),Q=[];e:{var B=Xc.get(e);if(B!==void 0){var ie=ws,de=e;switch(e){case"keypress":if(Pl(r)===0)break e;case"keydown":case"keyup":ie=Yg;break;case"focusin":de="focus",ie=Es;break;case"focusout":de="blur",ie=Es;break;case"beforeblur":case"afterblur":ie=Es;break;case"click":if(r.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":ie=Pc;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":ie=Og;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":ie=qg;break;case Kc:case Qc:case Gc:ie=Fg;break;case Yc:ie=ev;break;case"scroll":ie=Mg;break;case"wheel":ie=nv;break;case"copy":case"cut":case"paste":ie=Hg;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":ie=Rc}var fe=(t&4)!==0,Fe=!fe&&e==="scroll",A=fe?B!==null?B+"Capture":null:B;fe=[];for(var T=I,L;T!==null;){L=T;var Z=L.stateNode;if(L.tag===5&&Z!==null&&(L=Z,A!==null&&(Z=fo(T,A),Z!=null&&fe.push(jo(T,Z,L)))),Fe)break;T=T.return}0<fe.length&&(B=new ie(B,de,null,r,U),Q.push({event:B,listeners:fe}))}}if((t&7)===0){e:{if(B=e==="mouseover"||e==="pointerover",ie=e==="mouseout"||e==="pointerout",B&&r!==ls&&(de=r.relatedTarget||r.fromElement)&&(Gn(de)||de[on]))break e;if((ie||B)&&(B=U.window===U?U:(B=U.ownerDocument)?B.defaultView||B.parentWindow:window,ie?(de=r.relatedTarget||r.toElement,ie=I,de=de?Gn(de):null,de!==null&&(Fe=Qn(de),de!==Fe||de.tag!==5&&de.tag!==6)&&(de=null)):(ie=null,de=I),ie!==de)){if(fe=Pc,Z="onMouseLeave",A="onMouseEnter",T="mouse",(e==="pointerout"||e==="pointerover")&&(fe=Rc,Z="onPointerLeave",A="onPointerEnter",T="pointer"),Fe=ie==null?B:Nr(ie),L=de==null?B:Nr(de),B=new fe(Z,T+"leave",ie,r,U),B.target=Fe,B.relatedTarget=L,Z=null,Gn(U)===I&&(fe=new fe(A,T+"enter",de,r,U),fe.target=L,fe.relatedTarget=Fe,Z=fe),Fe=Z,ie&&de)t:{for(fe=ie,A=de,T=0,L=fe;L;L=kr(L))T++;for(L=0,Z=A;Z;Z=kr(Z))L++;for(;0<T-L;)fe=kr(fe),T--;for(;0<L-T;)A=kr(A),L--;for(;T--;){if(fe===A||A!==null&&fe===A.alternate)break t;fe=kr(fe),A=kr(A)}fe=null}else fe=null;ie!==null&&td(Q,B,ie,fe,!1),de!==null&&Fe!==null&&td(Q,Fe,de,fe,!0)}}e:{if(B=I?Nr(I):window,ie=B.nodeName&&B.nodeName.toLowerCase(),ie==="select"||ie==="input"&&B.type==="file")var he=uv;else if(Lc(B))if(Ic)he=pv;else{he=dv;var xe=cv}else(ie=B.nodeName)&&ie.toLowerCase()==="input"&&(B.type==="checkbox"||B.type==="radio")&&(he=fv);if(he&&(he=he(e,I))){Mc(Q,he,r,U);break e}xe&&xe(e,B,I),e==="focusout"&&(xe=B._wrapperState)&&xe.controlled&&B.type==="number"&&Mt(B,"number",B.value)}switch(xe=I?Nr(I):window,e){case"focusin":(Lc(xe)||xe.contentEditable==="true")&&(Er=xe,_s=I,Ro=null);break;case"focusout":Ro=_s=Er=null;break;case"mousedown":Ts=!0;break;case"contextmenu":case"mouseup":case"dragend":Ts=!1,Bc(Q,r,U);break;case"selectionchange":if(gv)break;case"keydown":case"keyup":Bc(Q,r,U)}var we;if(ks)e:{switch(e){case"compositionstart":var Ce="onCompositionStart";break e;case"compositionend":Ce="onCompositionEnd";break e;case"compositionupdate":Ce="onCompositionUpdate";break e}Ce=void 0}else Cr?zc(e,r)&&(Ce="onCompositionEnd"):e==="keydown"&&r.keyCode===229&&(Ce="onCompositionStart");Ce&&(_c&&r.locale!=="ko"&&(Cr||Ce!=="onCompositionStart"?Ce==="onCompositionEnd"&&Cr&&(we=bc()):(Sn=U,xs="value"in Sn?Sn.value:Sn.textContent,Cr=!0)),xe=Al(I,Ce),0<xe.length&&(Ce=new Nc(Ce,e,null,r,U),Q.push({event:Ce,listeners:xe}),we?Ce.data=we:(we=Ac(r),we!==null&&(Ce.data=we)))),(we=ov?lv(e,r):iv(e,r))&&(I=Al(I,"onBeforeInput"),0<I.length&&(U=new Nc("onBeforeInput","beforeinput",null,r,U),Q.push({event:U,listeners:I}),U.data=we))}Jc(Q,t)})}function jo(e,t,r){return{instance:e,listener:t,currentTarget:r}}function Al(e,t){for(var r=t+"Capture",i=[];e!==null;){var u=e,d=u.stateNode;u.tag===5&&d!==null&&(u=d,d=fo(e,r),d!=null&&i.unshift(jo(e,d,u)),d=fo(e,t),d!=null&&i.push(jo(e,d,u))),e=e.return}return i}function kr(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5);return e||null}function td(e,t,r,i,u){for(var d=t._reactName,y=[];r!==null&&r!==i;){var k=r,R=k.alternate,I=k.stateNode;if(R!==null&&R===i)break;k.tag===5&&I!==null&&(k=I,u?(R=fo(r,d),R!=null&&y.unshift(jo(r,R,k))):u||(R=fo(r,d),R!=null&&y.push(jo(r,R,k)))),r=r.return}y.length!==0&&e.push({event:t,listeners:y})}var wv=/\r\n?/g,Sv=/\u0000|\uFFFD/g;function nd(e){return(typeof e=="string"?e:""+e).replace(wv,`
`).replace(Sv,"")}function Ll(e,t,r){if(t=nd(t),nd(e)!==t&&r)throw Error(l(425))}function Ml(){}var Is=null,Os=null;function Ds(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var $s=typeof setTimeout=="function"?setTimeout:void 0,Cv=typeof clearTimeout=="function"?clearTimeout:void 0,rd=typeof Promise=="function"?Promise:void 0,Ev=typeof queueMicrotask=="function"?queueMicrotask:typeof rd<"u"?function(e){return rd.resolve(null).then(e).catch(bv)}:$s;function bv(e){setTimeout(function(){throw e})}function Fs(e,t){var r=t,i=0;do{var u=r.nextSibling;if(e.removeChild(r),u&&u.nodeType===8)if(r=u.data,r==="/$"){if(i===0){e.removeChild(u),So(t);return}i--}else r!=="$"&&r!=="$?"&&r!=="$!"||i++;r=u}while(r);So(t)}function En(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?")break;if(t==="/$")return null}}return e}function od(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var r=e.data;if(r==="$"||r==="$!"||r==="$?"){if(t===0)return e;t--}else r==="/$"&&t++}e=e.previousSibling}return null}var Pr=Math.random().toString(36).slice(2),Gt="__reactFiber$"+Pr,zo="__reactProps$"+Pr,on="__reactContainer$"+Pr,Ws="__reactEvents$"+Pr,kv="__reactListeners$"+Pr,Pv="__reactHandles$"+Pr;function Gn(e){var t=e[Gt];if(t)return t;for(var r=e.parentNode;r;){if(t=r[on]||r[Gt]){if(r=t.alternate,t.child!==null||r!==null&&r.child!==null)for(e=od(e);e!==null;){if(r=e[Gt])return r;e=od(e)}return t}e=r,r=e.parentNode}return null}function Ao(e){return e=e[Gt]||e[on],!e||e.tag!==5&&e.tag!==6&&e.tag!==13&&e.tag!==3?null:e}function Nr(e){if(e.tag===5||e.tag===6)return e.stateNode;throw Error(l(33))}function Il(e){return e[zo]||null}var Hs=[],Rr=-1;function bn(e){return{current:e}}function ze(e){0>Rr||(e.current=Hs[Rr],Hs[Rr]=null,Rr--)}function Te(e,t){Rr++,Hs[Rr]=e.current,e.current=t}var kn={},et=bn(kn),at=bn(!1),Yn=kn;function _r(e,t){var r=e.type.contextTypes;if(!r)return kn;var i=e.stateNode;if(i&&i.__reactInternalMemoizedUnmaskedChildContext===t)return i.__reactInternalMemoizedMaskedChildContext;var u={},d;for(d in r)u[d]=t[d];return i&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=t,e.__reactInternalMemoizedMaskedChildContext=u),u}function ut(e){return e=e.childContextTypes,e!=null}function Ol(){ze(at),ze(et)}function ld(e,t,r){if(et.current!==kn)throw Error(l(168));Te(et,t),Te(at,r)}function id(e,t,r){var i=e.stateNode;if(t=t.childContextTypes,typeof i.getChildContext!="function")return r;i=i.getChildContext();for(var u in i)if(!(u in t))throw Error(l(108,F(e)||"Unknown",u));return Y({},r,i)}function Dl(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||kn,Yn=et.current,Te(et,e),Te(at,at.current),!0}function sd(e,t,r){var i=e.stateNode;if(!i)throw Error(l(169));r?(e=id(e,t,Yn),i.__reactInternalMemoizedMergedChildContext=e,ze(at),ze(et),Te(et,e)):ze(at),Te(at,r)}var ln=null,$l=!1,Vs=!1;function ad(e){ln===null?ln=[e]:ln.push(e)}function Nv(e){$l=!0,ad(e)}function Pn(){if(!Vs&&ln!==null){Vs=!0;var e=0,t=Re;try{var r=ln;for(Re=1;e<r.length;e++){var i=r[e];do i=i(!0);while(i!==null)}ln=null,$l=!1}catch(u){throw ln!==null&&(ln=ln.slice(e+1)),cc(ds,Pn),u}finally{Re=t,Vs=!1}}return null}var Tr=[],jr=0,Fl=null,Wl=0,Pt=[],Nt=0,Xn=null,sn=1,an="";function Zn(e,t){Tr[jr++]=Wl,Tr[jr++]=Fl,Fl=e,Wl=t}function ud(e,t,r){Pt[Nt++]=sn,Pt[Nt++]=an,Pt[Nt++]=Xn,Xn=e;var i=sn;e=an;var u=32-It(i)-1;i&=~(1<<u),r+=1;var d=32-It(t)+u;if(30<d){var y=u-u%5;d=(i&(1<<y)-1).toString(32),i>>=y,u-=y,sn=1<<32-It(t)+u|r<<u|i,an=d+e}else sn=1<<d|r<<u|i,an=e}function Bs(e){e.return!==null&&(Zn(e,1),ud(e,1,0))}function Us(e){for(;e===Fl;)Fl=Tr[--jr],Tr[jr]=null,Wl=Tr[--jr],Tr[jr]=null;for(;e===Xn;)Xn=Pt[--Nt],Pt[Nt]=null,an=Pt[--Nt],Pt[Nt]=null,sn=Pt[--Nt],Pt[Nt]=null}var xt=null,wt=null,Le=!1,Dt=null;function cd(e,t){var r=jt(5,null,null,0);r.elementType="DELETED",r.stateNode=t,r.return=e,t=e.deletions,t===null?(e.deletions=[r],e.flags|=16):t.push(r)}function dd(e,t){switch(e.tag){case 5:var r=e.type;return t=t.nodeType!==1||r.toLowerCase()!==t.nodeName.toLowerCase()?null:t,t!==null?(e.stateNode=t,xt=e,wt=En(t.firstChild),!0):!1;case 6:return t=e.pendingProps===""||t.nodeType!==3?null:t,t!==null?(e.stateNode=t,xt=e,wt=null,!0):!1;case 13:return t=t.nodeType!==8?null:t,t!==null?(r=Xn!==null?{id:sn,overflow:an}:null,e.memoizedState={dehydrated:t,treeContext:r,retryLane:1073741824},r=jt(18,null,null,0),r.stateNode=t,r.return=e,e.child=r,xt=e,wt=null,!0):!1;default:return!1}}function Ks(e){return(e.mode&1)!==0&&(e.flags&128)===0}function Qs(e){if(Le){var t=wt;if(t){var r=t;if(!dd(e,t)){if(Ks(e))throw Error(l(418));t=En(r.nextSibling);var i=xt;t&&dd(e,t)?cd(i,r):(e.flags=e.flags&-4097|2,Le=!1,xt=e)}}else{if(Ks(e))throw Error(l(418));e.flags=e.flags&-4097|2,Le=!1,xt=e}}}function fd(e){for(e=e.return;e!==null&&e.tag!==5&&e.tag!==3&&e.tag!==13;)e=e.return;xt=e}function Hl(e){if(e!==xt)return!1;if(!Le)return fd(e),Le=!0,!1;var t;if((t=e.tag!==3)&&!(t=e.tag!==5)&&(t=e.type,t=t!=="head"&&t!=="body"&&!Ds(e.type,e.memoizedProps)),t&&(t=wt)){if(Ks(e))throw pd(),Error(l(418));for(;t;)cd(e,t),t=En(t.nextSibling)}if(fd(e),e.tag===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(l(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8){var r=e.data;if(r==="/$"){if(t===0){wt=En(e.nextSibling);break e}t--}else r!=="$"&&r!=="$!"&&r!=="$?"||t++}e=e.nextSibling}wt=null}}else wt=xt?En(e.stateNode.nextSibling):null;return!0}function pd(){for(var e=wt;e;)e=En(e.nextSibling)}function zr(){wt=xt=null,Le=!1}function Gs(e){Dt===null?Dt=[e]:Dt.push(e)}var Rv=M.ReactCurrentBatchConfig;function Lo(e,t,r){if(e=r.ref,e!==null&&typeof e!="function"&&typeof e!="object"){if(r._owner){if(r=r._owner,r){if(r.tag!==1)throw Error(l(309));var i=r.stateNode}if(!i)throw Error(l(147,e));var u=i,d=""+e;return t!==null&&t.ref!==null&&typeof t.ref=="function"&&t.ref._stringRef===d?t.ref:(t=function(y){var k=u.refs;y===null?delete k[d]:k[d]=y},t._stringRef=d,t)}if(typeof e!="string")throw Error(l(284));if(!r._owner)throw Error(l(290,e))}return e}function Vl(e,t){throw e=Object.prototype.toString.call(t),Error(l(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function md(e){var t=e._init;return t(e._payload)}function hd(e){function t(A,T){if(e){var L=A.deletions;L===null?(A.deletions=[T],A.flags|=16):L.push(T)}}function r(A,T){if(!e)return null;for(;T!==null;)t(A,T),T=T.sibling;return null}function i(A,T){for(A=new Map;T!==null;)T.key!==null?A.set(T.key,T):A.set(T.index,T),T=T.sibling;return A}function u(A,T){return A=Ln(A,T),A.index=0,A.sibling=null,A}function d(A,T,L){return A.index=L,e?(L=A.alternate,L!==null?(L=L.index,L<T?(A.flags|=2,T):L):(A.flags|=2,T)):(A.flags|=1048576,T)}function y(A){return e&&A.alternate===null&&(A.flags|=2),A}function k(A,T,L,Z){return T===null||T.tag!==6?(T=$a(L,A.mode,Z),T.return=A,T):(T=u(T,L),T.return=A,T)}function R(A,T,L,Z){var he=L.type;return he===O?U(A,T,L.props.children,Z,L.key):T!==null&&(T.elementType===he||typeof he=="object"&&he!==null&&he.$$typeof===le&&md(he)===T.type)?(Z=u(T,L.props),Z.ref=Lo(A,T,L),Z.return=A,Z):(Z=pi(L.type,L.key,L.props,null,A.mode,Z),Z.ref=Lo(A,T,L),Z.return=A,Z)}function I(A,T,L,Z){return T===null||T.tag!==4||T.stateNode.containerInfo!==L.containerInfo||T.stateNode.implementation!==L.implementation?(T=Fa(L,A.mode,Z),T.return=A,T):(T=u(T,L.children||[]),T.return=A,T)}function U(A,T,L,Z,he){return T===null||T.tag!==7?(T=lr(L,A.mode,Z,he),T.return=A,T):(T=u(T,L),T.return=A,T)}function Q(A,T,L){if(typeof T=="string"&&T!==""||typeof T=="number")return T=$a(""+T,A.mode,L),T.return=A,T;if(typeof T=="object"&&T!==null){switch(T.$$typeof){case D:return L=pi(T.type,T.key,T.props,null,A.mode,L),L.ref=Lo(A,null,T),L.return=A,L;case V:return T=Fa(T,A.mode,L),T.return=A,T;case le:var Z=T._init;return Q(A,Z(T._payload),L)}if(Kn(T)||J(T))return T=lr(T,A.mode,L,null),T.return=A,T;Vl(A,T)}return null}function B(A,T,L,Z){var he=T!==null?T.key:null;if(typeof L=="string"&&L!==""||typeof L=="number")return he!==null?null:k(A,T,""+L,Z);if(typeof L=="object"&&L!==null){switch(L.$$typeof){case D:return L.key===he?R(A,T,L,Z):null;case V:return L.key===he?I(A,T,L,Z):null;case le:return he=L._init,B(A,T,he(L._payload),Z)}if(Kn(L)||J(L))return he!==null?null:U(A,T,L,Z,null);Vl(A,L)}return null}function ie(A,T,L,Z,he){if(typeof Z=="string"&&Z!==""||typeof Z=="number")return A=A.get(L)||null,k(T,A,""+Z,he);if(typeof Z=="object"&&Z!==null){switch(Z.$$typeof){case D:return A=A.get(Z.key===null?L:Z.key)||null,R(T,A,Z,he);case V:return A=A.get(Z.key===null?L:Z.key)||null,I(T,A,Z,he);case le:var xe=Z._init;return ie(A,T,L,xe(Z._payload),he)}if(Kn(Z)||J(Z))return A=A.get(L)||null,U(T,A,Z,he,null);Vl(T,Z)}return null}function de(A,T,L,Z){for(var he=null,xe=null,we=T,Ce=T=0,Xe=null;we!==null&&Ce<L.length;Ce++){we.index>Ce?(Xe=we,we=null):Xe=we.sibling;var Ne=B(A,we,L[Ce],Z);if(Ne===null){we===null&&(we=Xe);break}e&&we&&Ne.alternate===null&&t(A,we),T=d(Ne,T,Ce),xe===null?he=Ne:xe.sibling=Ne,xe=Ne,we=Xe}if(Ce===L.length)return r(A,we),Le&&Zn(A,Ce),he;if(we===null){for(;Ce<L.length;Ce++)we=Q(A,L[Ce],Z),we!==null&&(T=d(we,T,Ce),xe===null?he=we:xe.sibling=we,xe=we);return Le&&Zn(A,Ce),he}for(we=i(A,we);Ce<L.length;Ce++)Xe=ie(we,A,Ce,L[Ce],Z),Xe!==null&&(e&&Xe.alternate!==null&&we.delete(Xe.key===null?Ce:Xe.key),T=d(Xe,T,Ce),xe===null?he=Xe:xe.sibling=Xe,xe=Xe);return e&&we.forEach(function(Mn){return t(A,Mn)}),Le&&Zn(A,Ce),he}function fe(A,T,L,Z){var he=J(L);if(typeof he!="function")throw Error(l(150));if(L=he.call(L),L==null)throw Error(l(151));for(var xe=he=null,we=T,Ce=T=0,Xe=null,Ne=L.next();we!==null&&!Ne.done;Ce++,Ne=L.next()){we.index>Ce?(Xe=we,we=null):Xe=we.sibling;var Mn=B(A,we,Ne.value,Z);if(Mn===null){we===null&&(we=Xe);break}e&&we&&Mn.alternate===null&&t(A,we),T=d(Mn,T,Ce),xe===null?he=Mn:xe.sibling=Mn,xe=Mn,we=Xe}if(Ne.done)return r(A,we),Le&&Zn(A,Ce),he;if(we===null){for(;!Ne.done;Ce++,Ne=L.next())Ne=Q(A,Ne.value,Z),Ne!==null&&(T=d(Ne,T,Ce),xe===null?he=Ne:xe.sibling=Ne,xe=Ne);return Le&&Zn(A,Ce),he}for(we=i(A,we);!Ne.done;Ce++,Ne=L.next())Ne=ie(we,A,Ce,Ne.value,Z),Ne!==null&&(e&&Ne.alternate!==null&&we.delete(Ne.key===null?Ce:Ne.key),T=d(Ne,T,Ce),xe===null?he=Ne:xe.sibling=Ne,xe=Ne);return e&&we.forEach(function(sy){return t(A,sy)}),Le&&Zn(A,Ce),he}function Fe(A,T,L,Z){if(typeof L=="object"&&L!==null&&L.type===O&&L.key===null&&(L=L.props.children),typeof L=="object"&&L!==null){switch(L.$$typeof){case D:e:{for(var he=L.key,xe=T;xe!==null;){if(xe.key===he){if(he=L.type,he===O){if(xe.tag===7){r(A,xe.sibling),T=u(xe,L.props.children),T.return=A,A=T;break e}}else if(xe.elementType===he||typeof he=="object"&&he!==null&&he.$$typeof===le&&md(he)===xe.type){r(A,xe.sibling),T=u(xe,L.props),T.ref=Lo(A,xe,L),T.return=A,A=T;break e}r(A,xe);break}else t(A,xe);xe=xe.sibling}L.type===O?(T=lr(L.props.children,A.mode,Z,L.key),T.return=A,A=T):(Z=pi(L.type,L.key,L.props,null,A.mode,Z),Z.ref=Lo(A,T,L),Z.return=A,A=Z)}return y(A);case V:e:{for(xe=L.key;T!==null;){if(T.key===xe)if(T.tag===4&&T.stateNode.containerInfo===L.containerInfo&&T.stateNode.implementation===L.implementation){r(A,T.sibling),T=u(T,L.children||[]),T.return=A,A=T;break e}else{r(A,T);break}else t(A,T);T=T.sibling}T=Fa(L,A.mode,Z),T.return=A,A=T}return y(A);case le:return xe=L._init,Fe(A,T,xe(L._payload),Z)}if(Kn(L))return de(A,T,L,Z);if(J(L))return fe(A,T,L,Z);Vl(A,L)}return typeof L=="string"&&L!==""||typeof L=="number"?(L=""+L,T!==null&&T.tag===6?(r(A,T.sibling),T=u(T,L),T.return=A,A=T):(r(A,T),T=$a(L,A.mode,Z),T.return=A,A=T),y(A)):r(A,T)}return Fe}var Ar=hd(!0),gd=hd(!1),Bl=bn(null),Ul=null,Lr=null,Ys=null;function Xs(){Ys=Lr=Ul=null}function Zs(e){var t=Bl.current;ze(Bl),e._currentValue=t}function qs(e,t,r){for(;e!==null;){var i=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,i!==null&&(i.childLanes|=t)):i!==null&&(i.childLanes&t)!==t&&(i.childLanes|=t),e===r)break;e=e.return}}function Mr(e,t){Ul=e,Ys=Lr=null,e=e.dependencies,e!==null&&e.firstContext!==null&&((e.lanes&t)!==0&&(ct=!0),e.firstContext=null)}function Rt(e){var t=e._currentValue;if(Ys!==e)if(e={context:e,memoizedValue:t,next:null},Lr===null){if(Ul===null)throw Error(l(308));Lr=e,Ul.dependencies={lanes:0,firstContext:e}}else Lr=Lr.next=e;return t}var qn=null;function Js(e){qn===null?qn=[e]:qn.push(e)}function vd(e,t,r,i){var u=t.interleaved;return u===null?(r.next=r,Js(t)):(r.next=u.next,u.next=r),t.interleaved=r,un(e,i)}function un(e,t){e.lanes|=t;var r=e.alternate;for(r!==null&&(r.lanes|=t),r=e,e=e.return;e!==null;)e.childLanes|=t,r=e.alternate,r!==null&&(r.childLanes|=t),r=e,e=e.return;return r.tag===3?r.stateNode:null}var Nn=!1;function ea(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function yd(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,effects:e.effects})}function cn(e,t){return{eventTime:e,lane:t,tag:0,payload:null,callback:null,next:null}}function Rn(e,t,r){var i=e.updateQueue;if(i===null)return null;if(i=i.shared,(Pe&2)!==0){var u=i.pending;return u===null?t.next=t:(t.next=u.next,u.next=t),i.pending=t,un(e,r)}return u=i.interleaved,u===null?(t.next=t,Js(i)):(t.next=u.next,u.next=t),i.interleaved=t,un(e,r)}function Kl(e,t,r){if(t=t.updateQueue,t!==null&&(t=t.shared,(r&4194240)!==0)){var i=t.lanes;i&=e.pendingLanes,r|=i,t.lanes=r,ms(e,r)}}function xd(e,t){var r=e.updateQueue,i=e.alternate;if(i!==null&&(i=i.updateQueue,r===i)){var u=null,d=null;if(r=r.firstBaseUpdate,r!==null){do{var y={eventTime:r.eventTime,lane:r.lane,tag:r.tag,payload:r.payload,callback:r.callback,next:null};d===null?u=d=y:d=d.next=y,r=r.next}while(r!==null);d===null?u=d=t:d=d.next=t}else u=d=t;r={baseState:i.baseState,firstBaseUpdate:u,lastBaseUpdate:d,shared:i.shared,effects:i.effects},e.updateQueue=r;return}e=r.lastBaseUpdate,e===null?r.firstBaseUpdate=t:e.next=t,r.lastBaseUpdate=t}function Ql(e,t,r,i){var u=e.updateQueue;Nn=!1;var d=u.firstBaseUpdate,y=u.lastBaseUpdate,k=u.shared.pending;if(k!==null){u.shared.pending=null;var R=k,I=R.next;R.next=null,y===null?d=I:y.next=I,y=R;var U=e.alternate;U!==null&&(U=U.updateQueue,k=U.lastBaseUpdate,k!==y&&(k===null?U.firstBaseUpdate=I:k.next=I,U.lastBaseUpdate=R))}if(d!==null){var Q=u.baseState;y=0,U=I=R=null,k=d;do{var B=k.lane,ie=k.eventTime;if((i&B)===B){U!==null&&(U=U.next={eventTime:ie,lane:0,tag:k.tag,payload:k.payload,callback:k.callback,next:null});e:{var de=e,fe=k;switch(B=t,ie=r,fe.tag){case 1:if(de=fe.payload,typeof de=="function"){Q=de.call(ie,Q,B);break e}Q=de;break e;case 3:de.flags=de.flags&-65537|128;case 0:if(de=fe.payload,B=typeof de=="function"?de.call(ie,Q,B):de,B==null)break e;Q=Y({},Q,B);break e;case 2:Nn=!0}}k.callback!==null&&k.lane!==0&&(e.flags|=64,B=u.effects,B===null?u.effects=[k]:B.push(k))}else ie={eventTime:ie,lane:B,tag:k.tag,payload:k.payload,callback:k.callback,next:null},U===null?(I=U=ie,R=Q):U=U.next=ie,y|=B;if(k=k.next,k===null){if(k=u.shared.pending,k===null)break;B=k,k=B.next,B.next=null,u.lastBaseUpdate=B,u.shared.pending=null}}while(!0);if(U===null&&(R=Q),u.baseState=R,u.firstBaseUpdate=I,u.lastBaseUpdate=U,t=u.shared.interleaved,t!==null){u=t;do y|=u.lane,u=u.next;while(u!==t)}else d===null&&(u.shared.lanes=0);tr|=y,e.lanes=y,e.memoizedState=Q}}function wd(e,t,r){if(e=t.effects,t.effects=null,e!==null)for(t=0;t<e.length;t++){var i=e[t],u=i.callback;if(u!==null){if(i.callback=null,i=r,typeof u!="function")throw Error(l(191,u));u.call(i)}}}var Mo={},Yt=bn(Mo),Io=bn(Mo),Oo=bn(Mo);function Jn(e){if(e===Mo)throw Error(l(174));return e}function ta(e,t){switch(Te(Oo,t),Te(Io,e),Te(Yt,Mo),e=t.nodeType,e){case 9:case 11:t=(t=t.documentElement)?t.namespaceURI:ns(null,"");break;default:e=e===8?t.parentNode:t,t=e.namespaceURI||null,e=e.tagName,t=ns(t,e)}ze(Yt),Te(Yt,t)}function Ir(){ze(Yt),ze(Io),ze(Oo)}function Sd(e){Jn(Oo.current);var t=Jn(Yt.current),r=ns(t,e.type);t!==r&&(Te(Io,e),Te(Yt,r))}function na(e){Io.current===e&&(ze(Yt),ze(Io))}var Me=bn(0);function Gl(e){for(var t=e;t!==null;){if(t.tag===13){var r=t.memoizedState;if(r!==null&&(r=r.dehydrated,r===null||r.data==="$?"||r.data==="$!"))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if((t.flags&128)!==0)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}var ra=[];function oa(){for(var e=0;e<ra.length;e++)ra[e]._workInProgressVersionPrimary=null;ra.length=0}var Yl=M.ReactCurrentDispatcher,la=M.ReactCurrentBatchConfig,er=0,Ie=null,Ke=null,Ge=null,Xl=!1,Do=!1,$o=0,_v=0;function tt(){throw Error(l(321))}function ia(e,t){if(t===null)return!1;for(var r=0;r<t.length&&r<e.length;r++)if(!Ot(e[r],t[r]))return!1;return!0}function sa(e,t,r,i,u,d){if(er=d,Ie=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,Yl.current=e===null||e.memoizedState===null?Av:Lv,e=r(i,u),Do){d=0;do{if(Do=!1,$o=0,25<=d)throw Error(l(301));d+=1,Ge=Ke=null,t.updateQueue=null,Yl.current=Mv,e=r(i,u)}while(Do)}if(Yl.current=Jl,t=Ke!==null&&Ke.next!==null,er=0,Ge=Ke=Ie=null,Xl=!1,t)throw Error(l(300));return e}function aa(){var e=$o!==0;return $o=0,e}function Xt(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return Ge===null?Ie.memoizedState=Ge=e:Ge=Ge.next=e,Ge}function _t(){if(Ke===null){var e=Ie.alternate;e=e!==null?e.memoizedState:null}else e=Ke.next;var t=Ge===null?Ie.memoizedState:Ge.next;if(t!==null)Ge=t,Ke=e;else{if(e===null)throw Error(l(310));Ke=e,e={memoizedState:Ke.memoizedState,baseState:Ke.baseState,baseQueue:Ke.baseQueue,queue:Ke.queue,next:null},Ge===null?Ie.memoizedState=Ge=e:Ge=Ge.next=e}return Ge}function Fo(e,t){return typeof t=="function"?t(e):t}function ua(e){var t=_t(),r=t.queue;if(r===null)throw Error(l(311));r.lastRenderedReducer=e;var i=Ke,u=i.baseQueue,d=r.pending;if(d!==null){if(u!==null){var y=u.next;u.next=d.next,d.next=y}i.baseQueue=u=d,r.pending=null}if(u!==null){d=u.next,i=i.baseState;var k=y=null,R=null,I=d;do{var U=I.lane;if((er&U)===U)R!==null&&(R=R.next={lane:0,action:I.action,hasEagerState:I.hasEagerState,eagerState:I.eagerState,next:null}),i=I.hasEagerState?I.eagerState:e(i,I.action);else{var Q={lane:U,action:I.action,hasEagerState:I.hasEagerState,eagerState:I.eagerState,next:null};R===null?(k=R=Q,y=i):R=R.next=Q,Ie.lanes|=U,tr|=U}I=I.next}while(I!==null&&I!==d);R===null?y=i:R.next=k,Ot(i,t.memoizedState)||(ct=!0),t.memoizedState=i,t.baseState=y,t.baseQueue=R,r.lastRenderedState=i}if(e=r.interleaved,e!==null){u=e;do d=u.lane,Ie.lanes|=d,tr|=d,u=u.next;while(u!==e)}else u===null&&(r.lanes=0);return[t.memoizedState,r.dispatch]}function ca(e){var t=_t(),r=t.queue;if(r===null)throw Error(l(311));r.lastRenderedReducer=e;var i=r.dispatch,u=r.pending,d=t.memoizedState;if(u!==null){r.pending=null;var y=u=u.next;do d=e(d,y.action),y=y.next;while(y!==u);Ot(d,t.memoizedState)||(ct=!0),t.memoizedState=d,t.baseQueue===null&&(t.baseState=d),r.lastRenderedState=d}return[d,i]}function Cd(){}function Ed(e,t){var r=Ie,i=_t(),u=t(),d=!Ot(i.memoizedState,u);if(d&&(i.memoizedState=u,ct=!0),i=i.queue,da(Pd.bind(null,r,i,e),[e]),i.getSnapshot!==t||d||Ge!==null&&Ge.memoizedState.tag&1){if(r.flags|=2048,Wo(9,kd.bind(null,r,i,u,t),void 0,null),Ye===null)throw Error(l(349));(er&30)!==0||bd(r,t,u)}return u}function bd(e,t,r){e.flags|=16384,e={getSnapshot:t,value:r},t=Ie.updateQueue,t===null?(t={lastEffect:null,stores:null},Ie.updateQueue=t,t.stores=[e]):(r=t.stores,r===null?t.stores=[e]:r.push(e))}function kd(e,t,r,i){t.value=r,t.getSnapshot=i,Nd(t)&&Rd(e)}function Pd(e,t,r){return r(function(){Nd(t)&&Rd(e)})}function Nd(e){var t=e.getSnapshot;e=e.value;try{var r=t();return!Ot(e,r)}catch{return!0}}function Rd(e){var t=un(e,1);t!==null&&Ht(t,e,1,-1)}function _d(e){var t=Xt();return typeof e=="function"&&(e=e()),t.memoizedState=t.baseState=e,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:Fo,lastRenderedState:e},t.queue=e,e=e.dispatch=zv.bind(null,Ie,e),[t.memoizedState,e]}function Wo(e,t,r,i){return e={tag:e,create:t,destroy:r,deps:i,next:null},t=Ie.updateQueue,t===null?(t={lastEffect:null,stores:null},Ie.updateQueue=t,t.lastEffect=e.next=e):(r=t.lastEffect,r===null?t.lastEffect=e.next=e:(i=r.next,r.next=e,e.next=i,t.lastEffect=e)),e}function Td(){return _t().memoizedState}function Zl(e,t,r,i){var u=Xt();Ie.flags|=e,u.memoizedState=Wo(1|t,r,void 0,i===void 0?null:i)}function ql(e,t,r,i){var u=_t();i=i===void 0?null:i;var d=void 0;if(Ke!==null){var y=Ke.memoizedState;if(d=y.destroy,i!==null&&ia(i,y.deps)){u.memoizedState=Wo(t,r,d,i);return}}Ie.flags|=e,u.memoizedState=Wo(1|t,r,d,i)}function jd(e,t){return Zl(8390656,8,e,t)}function da(e,t){return ql(2048,8,e,t)}function zd(e,t){return ql(4,2,e,t)}function Ad(e,t){return ql(4,4,e,t)}function Ld(e,t){if(typeof t=="function")return e=e(),t(e),function(){t(null)};if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function Md(e,t,r){return r=r!=null?r.concat([e]):null,ql(4,4,Ld.bind(null,t,e),r)}function fa(){}function Id(e,t){var r=_t();t=t===void 0?null:t;var i=r.memoizedState;return i!==null&&t!==null&&ia(t,i[1])?i[0]:(r.memoizedState=[e,t],e)}function Od(e,t){var r=_t();t=t===void 0?null:t;var i=r.memoizedState;return i!==null&&t!==null&&ia(t,i[1])?i[0]:(e=e(),r.memoizedState=[e,t],e)}function Dd(e,t,r){return(er&21)===0?(e.baseState&&(e.baseState=!1,ct=!0),e.memoizedState=r):(Ot(r,t)||(r=mc(),Ie.lanes|=r,tr|=r,e.baseState=!0),t)}function Tv(e,t){var r=Re;Re=r!==0&&4>r?r:4,e(!0);var i=la.transition;la.transition={};try{e(!1),t()}finally{Re=r,la.transition=i}}function $d(){return _t().memoizedState}function jv(e,t,r){var i=zn(e);if(r={lane:i,action:r,hasEagerState:!1,eagerState:null,next:null},Fd(e))Wd(t,r);else if(r=vd(e,t,r,i),r!==null){var u=it();Ht(r,e,i,u),Hd(r,t,i)}}function zv(e,t,r){var i=zn(e),u={lane:i,action:r,hasEagerState:!1,eagerState:null,next:null};if(Fd(e))Wd(t,u);else{var d=e.alternate;if(e.lanes===0&&(d===null||d.lanes===0)&&(d=t.lastRenderedReducer,d!==null))try{var y=t.lastRenderedState,k=d(y,r);if(u.hasEagerState=!0,u.eagerState=k,Ot(k,y)){var R=t.interleaved;R===null?(u.next=u,Js(t)):(u.next=R.next,R.next=u),t.interleaved=u;return}}catch{}finally{}r=vd(e,t,u,i),r!==null&&(u=it(),Ht(r,e,i,u),Hd(r,t,i))}}function Fd(e){var t=e.alternate;return e===Ie||t!==null&&t===Ie}function Wd(e,t){Do=Xl=!0;var r=e.pending;r===null?t.next=t:(t.next=r.next,r.next=t),e.pending=t}function Hd(e,t,r){if((r&4194240)!==0){var i=t.lanes;i&=e.pendingLanes,r|=i,t.lanes=r,ms(e,r)}}var Jl={readContext:Rt,useCallback:tt,useContext:tt,useEffect:tt,useImperativeHandle:tt,useInsertionEffect:tt,useLayoutEffect:tt,useMemo:tt,useReducer:tt,useRef:tt,useState:tt,useDebugValue:tt,useDeferredValue:tt,useTransition:tt,useMutableSource:tt,useSyncExternalStore:tt,useId:tt,unstable_isNewReconciler:!1},Av={readContext:Rt,useCallback:function(e,t){return Xt().memoizedState=[e,t===void 0?null:t],e},useContext:Rt,useEffect:jd,useImperativeHandle:function(e,t,r){return r=r!=null?r.concat([e]):null,Zl(4194308,4,Ld.bind(null,t,e),r)},useLayoutEffect:function(e,t){return Zl(4194308,4,e,t)},useInsertionEffect:function(e,t){return Zl(4,2,e,t)},useMemo:function(e,t){var r=Xt();return t=t===void 0?null:t,e=e(),r.memoizedState=[e,t],e},useReducer:function(e,t,r){var i=Xt();return t=r!==void 0?r(t):t,i.memoizedState=i.baseState=t,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:t},i.queue=e,e=e.dispatch=jv.bind(null,Ie,e),[i.memoizedState,e]},useRef:function(e){var t=Xt();return e={current:e},t.memoizedState=e},useState:_d,useDebugValue:fa,useDeferredValue:function(e){return Xt().memoizedState=e},useTransition:function(){var e=_d(!1),t=e[0];return e=Tv.bind(null,e[1]),Xt().memoizedState=e,[t,e]},useMutableSource:function(){},useSyncExternalStore:function(e,t,r){var i=Ie,u=Xt();if(Le){if(r===void 0)throw Error(l(407));r=r()}else{if(r=t(),Ye===null)throw Error(l(349));(er&30)!==0||bd(i,t,r)}u.memoizedState=r;var d={value:r,getSnapshot:t};return u.queue=d,jd(Pd.bind(null,i,d,e),[e]),i.flags|=2048,Wo(9,kd.bind(null,i,d,r,t),void 0,null),r},useId:function(){var e=Xt(),t=Ye.identifierPrefix;if(Le){var r=an,i=sn;r=(i&~(1<<32-It(i)-1)).toString(32)+r,t=":"+t+"R"+r,r=$o++,0<r&&(t+="H"+r.toString(32)),t+=":"}else r=_v++,t=":"+t+"r"+r.toString(32)+":";return e.memoizedState=t},unstable_isNewReconciler:!1},Lv={readContext:Rt,useCallback:Id,useContext:Rt,useEffect:da,useImperativeHandle:Md,useInsertionEffect:zd,useLayoutEffect:Ad,useMemo:Od,useReducer:ua,useRef:Td,useState:function(){return ua(Fo)},useDebugValue:fa,useDeferredValue:function(e){var t=_t();return Dd(t,Ke.memoizedState,e)},useTransition:function(){var e=ua(Fo)[0],t=_t().memoizedState;return[e,t]},useMutableSource:Cd,useSyncExternalStore:Ed,useId:$d,unstable_isNewReconciler:!1},Mv={readContext:Rt,useCallback:Id,useContext:Rt,useEffect:da,useImperativeHandle:Md,useInsertionEffect:zd,useLayoutEffect:Ad,useMemo:Od,useReducer:ca,useRef:Td,useState:function(){return ca(Fo)},useDebugValue:fa,useDeferredValue:function(e){var t=_t();return Ke===null?t.memoizedState=e:Dd(t,Ke.memoizedState,e)},useTransition:function(){var e=ca(Fo)[0],t=_t().memoizedState;return[e,t]},useMutableSource:Cd,useSyncExternalStore:Ed,useId:$d,unstable_isNewReconciler:!1};function $t(e,t){if(e&&e.defaultProps){t=Y({},t),e=e.defaultProps;for(var r in e)t[r]===void 0&&(t[r]=e[r]);return t}return t}function pa(e,t,r,i){t=e.memoizedState,r=r(i,t),r=r==null?t:Y({},t,r),e.memoizedState=r,e.lanes===0&&(e.updateQueue.baseState=r)}var ei={isMounted:function(e){return(e=e._reactInternals)?Qn(e)===e:!1},enqueueSetState:function(e,t,r){e=e._reactInternals;var i=it(),u=zn(e),d=cn(i,u);d.payload=t,r!=null&&(d.callback=r),t=Rn(e,d,u),t!==null&&(Ht(t,e,u,i),Kl(t,e,u))},enqueueReplaceState:function(e,t,r){e=e._reactInternals;var i=it(),u=zn(e),d=cn(i,u);d.tag=1,d.payload=t,r!=null&&(d.callback=r),t=Rn(e,d,u),t!==null&&(Ht(t,e,u,i),Kl(t,e,u))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var r=it(),i=zn(e),u=cn(r,i);u.tag=2,t!=null&&(u.callback=t),t=Rn(e,u,i),t!==null&&(Ht(t,e,i,r),Kl(t,e,i))}};function Vd(e,t,r,i,u,d,y){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(i,d,y):t.prototype&&t.prototype.isPureReactComponent?!No(r,i)||!No(u,d):!0}function Bd(e,t,r){var i=!1,u=kn,d=t.contextType;return typeof d=="object"&&d!==null?d=Rt(d):(u=ut(t)?Yn:et.current,i=t.contextTypes,d=(i=i!=null)?_r(e,u):kn),t=new t(r,d),e.memoizedState=t.state!==null&&t.state!==void 0?t.state:null,t.updater=ei,e.stateNode=t,t._reactInternals=e,i&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=u,e.__reactInternalMemoizedMaskedChildContext=d),t}function Ud(e,t,r,i){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(r,i),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(r,i),t.state!==e&&ei.enqueueReplaceState(t,t.state,null)}function ma(e,t,r,i){var u=e.stateNode;u.props=r,u.state=e.memoizedState,u.refs={},ea(e);var d=t.contextType;typeof d=="object"&&d!==null?u.context=Rt(d):(d=ut(t)?Yn:et.current,u.context=_r(e,d)),u.state=e.memoizedState,d=t.getDerivedStateFromProps,typeof d=="function"&&(pa(e,t,d,r),u.state=e.memoizedState),typeof t.getDerivedStateFromProps=="function"||typeof u.getSnapshotBeforeUpdate=="function"||typeof u.UNSAFE_componentWillMount!="function"&&typeof u.componentWillMount!="function"||(t=u.state,typeof u.componentWillMount=="function"&&u.componentWillMount(),typeof u.UNSAFE_componentWillMount=="function"&&u.UNSAFE_componentWillMount(),t!==u.state&&ei.enqueueReplaceState(u,u.state,null),Ql(e,r,u,i),u.state=e.memoizedState),typeof u.componentDidMount=="function"&&(e.flags|=4194308)}function Or(e,t){try{var r="",i=t;do r+=ne(i),i=i.return;while(i);var u=r}catch(d){u=`
Error generating stack: `+d.message+`
`+d.stack}return{value:e,source:t,stack:u,digest:null}}function ha(e,t,r){return{value:e,source:null,stack:r??null,digest:t??null}}function ga(e,t){try{console.error(t.value)}catch(r){setTimeout(function(){throw r})}}var Iv=typeof WeakMap=="function"?WeakMap:Map;function Kd(e,t,r){r=cn(-1,r),r.tag=3,r.payload={element:null};var i=t.value;return r.callback=function(){si||(si=!0,ja=i),ga(e,t)},r}function Qd(e,t,r){r=cn(-1,r),r.tag=3;var i=e.type.getDerivedStateFromError;if(typeof i=="function"){var u=t.value;r.payload=function(){return i(u)},r.callback=function(){ga(e,t)}}var d=e.stateNode;return d!==null&&typeof d.componentDidCatch=="function"&&(r.callback=function(){ga(e,t),typeof i!="function"&&(Tn===null?Tn=new Set([this]):Tn.add(this));var y=t.stack;this.componentDidCatch(t.value,{componentStack:y!==null?y:""})}),r}function Gd(e,t,r){var i=e.pingCache;if(i===null){i=e.pingCache=new Iv;var u=new Set;i.set(t,u)}else u=i.get(t),u===void 0&&(u=new Set,i.set(t,u));u.has(r)||(u.add(r),e=Xv.bind(null,e,t,r),t.then(e,e))}function Yd(e){do{var t;if((t=e.tag===13)&&(t=e.memoizedState,t=t!==null?t.dehydrated!==null:!0),t)return e;e=e.return}while(e!==null);return null}function Xd(e,t,r,i,u){return(e.mode&1)===0?(e===t?e.flags|=65536:(e.flags|=128,r.flags|=131072,r.flags&=-52805,r.tag===1&&(r.alternate===null?r.tag=17:(t=cn(-1,1),t.tag=2,Rn(r,t,1))),r.lanes|=1),e):(e.flags|=65536,e.lanes=u,e)}var Ov=M.ReactCurrentOwner,ct=!1;function lt(e,t,r,i){t.child=e===null?gd(t,null,r,i):Ar(t,e.child,r,i)}function Zd(e,t,r,i,u){r=r.render;var d=t.ref;return Mr(t,u),i=sa(e,t,r,i,d,u),r=aa(),e!==null&&!ct?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~u,dn(e,t,u)):(Le&&r&&Bs(t),t.flags|=1,lt(e,t,i,u),t.child)}function qd(e,t,r,i,u){if(e===null){var d=r.type;return typeof d=="function"&&!Da(d)&&d.defaultProps===void 0&&r.compare===null&&r.defaultProps===void 0?(t.tag=15,t.type=d,Jd(e,t,d,i,u)):(e=pi(r.type,null,i,t,t.mode,u),e.ref=t.ref,e.return=t,t.child=e)}if(d=e.child,(e.lanes&u)===0){var y=d.memoizedProps;if(r=r.compare,r=r!==null?r:No,r(y,i)&&e.ref===t.ref)return dn(e,t,u)}return t.flags|=1,e=Ln(d,i),e.ref=t.ref,e.return=t,t.child=e}function Jd(e,t,r,i,u){if(e!==null){var d=e.memoizedProps;if(No(d,i)&&e.ref===t.ref)if(ct=!1,t.pendingProps=i=d,(e.lanes&u)!==0)(e.flags&131072)!==0&&(ct=!0);else return t.lanes=e.lanes,dn(e,t,u)}return va(e,t,r,i,u)}function ef(e,t,r){var i=t.pendingProps,u=i.children,d=e!==null?e.memoizedState:null;if(i.mode==="hidden")if((t.mode&1)===0)t.memoizedState={baseLanes:0,cachePool:null,transitions:null},Te($r,St),St|=r;else{if((r&1073741824)===0)return e=d!==null?d.baseLanes|r:r,t.lanes=t.childLanes=1073741824,t.memoizedState={baseLanes:e,cachePool:null,transitions:null},t.updateQueue=null,Te($r,St),St|=e,null;t.memoizedState={baseLanes:0,cachePool:null,transitions:null},i=d!==null?d.baseLanes:r,Te($r,St),St|=i}else d!==null?(i=d.baseLanes|r,t.memoizedState=null):i=r,Te($r,St),St|=i;return lt(e,t,u,r),t.child}function tf(e,t){var r=t.ref;(e===null&&r!==null||e!==null&&e.ref!==r)&&(t.flags|=512,t.flags|=2097152)}function va(e,t,r,i,u){var d=ut(r)?Yn:et.current;return d=_r(t,d),Mr(t,u),r=sa(e,t,r,i,d,u),i=aa(),e!==null&&!ct?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~u,dn(e,t,u)):(Le&&i&&Bs(t),t.flags|=1,lt(e,t,r,u),t.child)}function nf(e,t,r,i,u){if(ut(r)){var d=!0;Dl(t)}else d=!1;if(Mr(t,u),t.stateNode===null)ni(e,t),Bd(t,r,i),ma(t,r,i,u),i=!0;else if(e===null){var y=t.stateNode,k=t.memoizedProps;y.props=k;var R=y.context,I=r.contextType;typeof I=="object"&&I!==null?I=Rt(I):(I=ut(r)?Yn:et.current,I=_r(t,I));var U=r.getDerivedStateFromProps,Q=typeof U=="function"||typeof y.getSnapshotBeforeUpdate=="function";Q||typeof y.UNSAFE_componentWillReceiveProps!="function"&&typeof y.componentWillReceiveProps!="function"||(k!==i||R!==I)&&Ud(t,y,i,I),Nn=!1;var B=t.memoizedState;y.state=B,Ql(t,i,y,u),R=t.memoizedState,k!==i||B!==R||at.current||Nn?(typeof U=="function"&&(pa(t,r,U,i),R=t.memoizedState),(k=Nn||Vd(t,r,k,i,B,R,I))?(Q||typeof y.UNSAFE_componentWillMount!="function"&&typeof y.componentWillMount!="function"||(typeof y.componentWillMount=="function"&&y.componentWillMount(),typeof y.UNSAFE_componentWillMount=="function"&&y.UNSAFE_componentWillMount()),typeof y.componentDidMount=="function"&&(t.flags|=4194308)):(typeof y.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=i,t.memoizedState=R),y.props=i,y.state=R,y.context=I,i=k):(typeof y.componentDidMount=="function"&&(t.flags|=4194308),i=!1)}else{y=t.stateNode,yd(e,t),k=t.memoizedProps,I=t.type===t.elementType?k:$t(t.type,k),y.props=I,Q=t.pendingProps,B=y.context,R=r.contextType,typeof R=="object"&&R!==null?R=Rt(R):(R=ut(r)?Yn:et.current,R=_r(t,R));var ie=r.getDerivedStateFromProps;(U=typeof ie=="function"||typeof y.getSnapshotBeforeUpdate=="function")||typeof y.UNSAFE_componentWillReceiveProps!="function"&&typeof y.componentWillReceiveProps!="function"||(k!==Q||B!==R)&&Ud(t,y,i,R),Nn=!1,B=t.memoizedState,y.state=B,Ql(t,i,y,u);var de=t.memoizedState;k!==Q||B!==de||at.current||Nn?(typeof ie=="function"&&(pa(t,r,ie,i),de=t.memoizedState),(I=Nn||Vd(t,r,I,i,B,de,R)||!1)?(U||typeof y.UNSAFE_componentWillUpdate!="function"&&typeof y.componentWillUpdate!="function"||(typeof y.componentWillUpdate=="function"&&y.componentWillUpdate(i,de,R),typeof y.UNSAFE_componentWillUpdate=="function"&&y.UNSAFE_componentWillUpdate(i,de,R)),typeof y.componentDidUpdate=="function"&&(t.flags|=4),typeof y.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof y.componentDidUpdate!="function"||k===e.memoizedProps&&B===e.memoizedState||(t.flags|=4),typeof y.getSnapshotBeforeUpdate!="function"||k===e.memoizedProps&&B===e.memoizedState||(t.flags|=1024),t.memoizedProps=i,t.memoizedState=de),y.props=i,y.state=de,y.context=R,i=I):(typeof y.componentDidUpdate!="function"||k===e.memoizedProps&&B===e.memoizedState||(t.flags|=4),typeof y.getSnapshotBeforeUpdate!="function"||k===e.memoizedProps&&B===e.memoizedState||(t.flags|=1024),i=!1)}return ya(e,t,r,i,d,u)}function ya(e,t,r,i,u,d){tf(e,t);var y=(t.flags&128)!==0;if(!i&&!y)return u&&sd(t,r,!1),dn(e,t,d);i=t.stateNode,Ov.current=t;var k=y&&typeof r.getDerivedStateFromError!="function"?null:i.render();return t.flags|=1,e!==null&&y?(t.child=Ar(t,e.child,null,d),t.child=Ar(t,null,k,d)):lt(e,t,k,d),t.memoizedState=i.state,u&&sd(t,r,!0),t.child}function rf(e){var t=e.stateNode;t.pendingContext?ld(e,t.pendingContext,t.pendingContext!==t.context):t.context&&ld(e,t.context,!1),ta(e,t.containerInfo)}function of(e,t,r,i,u){return zr(),Gs(u),t.flags|=256,lt(e,t,r,i),t.child}var xa={dehydrated:null,treeContext:null,retryLane:0};function wa(e){return{baseLanes:e,cachePool:null,transitions:null}}function lf(e,t,r){var i=t.pendingProps,u=Me.current,d=!1,y=(t.flags&128)!==0,k;if((k=y)||(k=e!==null&&e.memoizedState===null?!1:(u&2)!==0),k?(d=!0,t.flags&=-129):(e===null||e.memoizedState!==null)&&(u|=1),Te(Me,u&1),e===null)return Qs(t),e=t.memoizedState,e!==null&&(e=e.dehydrated,e!==null)?((t.mode&1)===0?t.lanes=1:e.data==="$!"?t.lanes=8:t.lanes=1073741824,null):(y=i.children,e=i.fallback,d?(i=t.mode,d=t.child,y={mode:"hidden",children:y},(i&1)===0&&d!==null?(d.childLanes=0,d.pendingProps=y):d=mi(y,i,0,null),e=lr(e,i,r,null),d.return=t,e.return=t,d.sibling=e,t.child=d,t.child.memoizedState=wa(r),t.memoizedState=xa,e):Sa(t,y));if(u=e.memoizedState,u!==null&&(k=u.dehydrated,k!==null))return Dv(e,t,y,i,k,u,r);if(d){d=i.fallback,y=t.mode,u=e.child,k=u.sibling;var R={mode:"hidden",children:i.children};return(y&1)===0&&t.child!==u?(i=t.child,i.childLanes=0,i.pendingProps=R,t.deletions=null):(i=Ln(u,R),i.subtreeFlags=u.subtreeFlags&14680064),k!==null?d=Ln(k,d):(d=lr(d,y,r,null),d.flags|=2),d.return=t,i.return=t,i.sibling=d,t.child=i,i=d,d=t.child,y=e.child.memoizedState,y=y===null?wa(r):{baseLanes:y.baseLanes|r,cachePool:null,transitions:y.transitions},d.memoizedState=y,d.childLanes=e.childLanes&~r,t.memoizedState=xa,i}return d=e.child,e=d.sibling,i=Ln(d,{mode:"visible",children:i.children}),(t.mode&1)===0&&(i.lanes=r),i.return=t,i.sibling=null,e!==null&&(r=t.deletions,r===null?(t.deletions=[e],t.flags|=16):r.push(e)),t.child=i,t.memoizedState=null,i}function Sa(e,t){return t=mi({mode:"visible",children:t},e.mode,0,null),t.return=e,e.child=t}function ti(e,t,r,i){return i!==null&&Gs(i),Ar(t,e.child,null,r),e=Sa(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function Dv(e,t,r,i,u,d,y){if(r)return t.flags&256?(t.flags&=-257,i=ha(Error(l(422))),ti(e,t,y,i)):t.memoizedState!==null?(t.child=e.child,t.flags|=128,null):(d=i.fallback,u=t.mode,i=mi({mode:"visible",children:i.children},u,0,null),d=lr(d,u,y,null),d.flags|=2,i.return=t,d.return=t,i.sibling=d,t.child=i,(t.mode&1)!==0&&Ar(t,e.child,null,y),t.child.memoizedState=wa(y),t.memoizedState=xa,d);if((t.mode&1)===0)return ti(e,t,y,null);if(u.data==="$!"){if(i=u.nextSibling&&u.nextSibling.dataset,i)var k=i.dgst;return i=k,d=Error(l(419)),i=ha(d,i,void 0),ti(e,t,y,i)}if(k=(y&e.childLanes)!==0,ct||k){if(i=Ye,i!==null){switch(y&-y){case 4:u=2;break;case 16:u=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:u=32;break;case 536870912:u=268435456;break;default:u=0}u=(u&(i.suspendedLanes|y))!==0?0:u,u!==0&&u!==d.retryLane&&(d.retryLane=u,un(e,u),Ht(i,e,u,-1))}return Oa(),i=ha(Error(l(421))),ti(e,t,y,i)}return u.data==="$?"?(t.flags|=128,t.child=e.child,t=Zv.bind(null,e),u._reactRetry=t,null):(e=d.treeContext,wt=En(u.nextSibling),xt=t,Le=!0,Dt=null,e!==null&&(Pt[Nt++]=sn,Pt[Nt++]=an,Pt[Nt++]=Xn,sn=e.id,an=e.overflow,Xn=t),t=Sa(t,i.children),t.flags|=4096,t)}function sf(e,t,r){e.lanes|=t;var i=e.alternate;i!==null&&(i.lanes|=t),qs(e.return,t,r)}function Ca(e,t,r,i,u){var d=e.memoizedState;d===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:i,tail:r,tailMode:u}:(d.isBackwards=t,d.rendering=null,d.renderingStartTime=0,d.last=i,d.tail=r,d.tailMode=u)}function af(e,t,r){var i=t.pendingProps,u=i.revealOrder,d=i.tail;if(lt(e,t,i.children,r),i=Me.current,(i&2)!==0)i=i&1|2,t.flags|=128;else{if(e!==null&&(e.flags&128)!==0)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&sf(e,r,t);else if(e.tag===19)sf(e,r,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}i&=1}if(Te(Me,i),(t.mode&1)===0)t.memoizedState=null;else switch(u){case"forwards":for(r=t.child,u=null;r!==null;)e=r.alternate,e!==null&&Gl(e)===null&&(u=r),r=r.sibling;r=u,r===null?(u=t.child,t.child=null):(u=r.sibling,r.sibling=null),Ca(t,!1,u,r,d);break;case"backwards":for(r=null,u=t.child,t.child=null;u!==null;){if(e=u.alternate,e!==null&&Gl(e)===null){t.child=u;break}e=u.sibling,u.sibling=r,r=u,u=e}Ca(t,!0,r,null,d);break;case"together":Ca(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function ni(e,t){(t.mode&1)===0&&e!==null&&(e.alternate=null,t.alternate=null,t.flags|=2)}function dn(e,t,r){if(e!==null&&(t.dependencies=e.dependencies),tr|=t.lanes,(r&t.childLanes)===0)return null;if(e!==null&&t.child!==e.child)throw Error(l(153));if(t.child!==null){for(e=t.child,r=Ln(e,e.pendingProps),t.child=r,r.return=t;e.sibling!==null;)e=e.sibling,r=r.sibling=Ln(e,e.pendingProps),r.return=t;r.sibling=null}return t.child}function $v(e,t,r){switch(t.tag){case 3:rf(t),zr();break;case 5:Sd(t);break;case 1:ut(t.type)&&Dl(t);break;case 4:ta(t,t.stateNode.containerInfo);break;case 10:var i=t.type._context,u=t.memoizedProps.value;Te(Bl,i._currentValue),i._currentValue=u;break;case 13:if(i=t.memoizedState,i!==null)return i.dehydrated!==null?(Te(Me,Me.current&1),t.flags|=128,null):(r&t.child.childLanes)!==0?lf(e,t,r):(Te(Me,Me.current&1),e=dn(e,t,r),e!==null?e.sibling:null);Te(Me,Me.current&1);break;case 19:if(i=(r&t.childLanes)!==0,(e.flags&128)!==0){if(i)return af(e,t,r);t.flags|=128}if(u=t.memoizedState,u!==null&&(u.rendering=null,u.tail=null,u.lastEffect=null),Te(Me,Me.current),i)break;return null;case 22:case 23:return t.lanes=0,ef(e,t,r)}return dn(e,t,r)}var uf,Ea,cf,df;uf=function(e,t){for(var r=t.child;r!==null;){if(r.tag===5||r.tag===6)e.appendChild(r.stateNode);else if(r.tag!==4&&r.child!==null){r.child.return=r,r=r.child;continue}if(r===t)break;for(;r.sibling===null;){if(r.return===null||r.return===t)return;r=r.return}r.sibling.return=r.return,r=r.sibling}},Ea=function(){},cf=function(e,t,r,i){var u=e.memoizedProps;if(u!==i){e=t.stateNode,Jn(Yt.current);var d=null;switch(r){case"input":u=_e(e,u),i=_e(e,i),d=[];break;case"select":u=Y({},u,{value:void 0}),i=Y({},i,{value:void 0}),d=[];break;case"textarea":u=vr(e,u),i=vr(e,i),d=[];break;default:typeof u.onClick!="function"&&typeof i.onClick=="function"&&(e.onclick=Ml)}rs(r,i);var y;r=null;for(I in u)if(!i.hasOwnProperty(I)&&u.hasOwnProperty(I)&&u[I]!=null)if(I==="style"){var k=u[I];for(y in k)k.hasOwnProperty(y)&&(r||(r={}),r[y]="")}else I!=="dangerouslySetInnerHTML"&&I!=="children"&&I!=="suppressContentEditableWarning"&&I!=="suppressHydrationWarning"&&I!=="autoFocus"&&(a.hasOwnProperty(I)?d||(d=[]):(d=d||[]).push(I,null));for(I in i){var R=i[I];if(k=u!=null?u[I]:void 0,i.hasOwnProperty(I)&&R!==k&&(R!=null||k!=null))if(I==="style")if(k){for(y in k)!k.hasOwnProperty(y)||R&&R.hasOwnProperty(y)||(r||(r={}),r[y]="");for(y in R)R.hasOwnProperty(y)&&k[y]!==R[y]&&(r||(r={}),r[y]=R[y])}else r||(d||(d=[]),d.push(I,r)),r=R;else I==="dangerouslySetInnerHTML"?(R=R?R.__html:void 0,k=k?k.__html:void 0,R!=null&&k!==R&&(d=d||[]).push(I,R)):I==="children"?typeof R!="string"&&typeof R!="number"||(d=d||[]).push(I,""+R):I!=="suppressContentEditableWarning"&&I!=="suppressHydrationWarning"&&(a.hasOwnProperty(I)?(R!=null&&I==="onScroll"&&je("scroll",e),d||k===R||(d=[])):(d=d||[]).push(I,R))}r&&(d=d||[]).push("style",r);var I=d;(t.updateQueue=I)&&(t.flags|=4)}},df=function(e,t,r,i){r!==i&&(t.flags|=4)};function Ho(e,t){if(!Le)switch(e.tailMode){case"hidden":t=e.tail;for(var r=null;t!==null;)t.alternate!==null&&(r=t),t=t.sibling;r===null?e.tail=null:r.sibling=null;break;case"collapsed":r=e.tail;for(var i=null;r!==null;)r.alternate!==null&&(i=r),r=r.sibling;i===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:i.sibling=null}}function nt(e){var t=e.alternate!==null&&e.alternate.child===e.child,r=0,i=0;if(t)for(var u=e.child;u!==null;)r|=u.lanes|u.childLanes,i|=u.subtreeFlags&14680064,i|=u.flags&14680064,u.return=e,u=u.sibling;else for(u=e.child;u!==null;)r|=u.lanes|u.childLanes,i|=u.subtreeFlags,i|=u.flags,u.return=e,u=u.sibling;return e.subtreeFlags|=i,e.childLanes=r,t}function Fv(e,t,r){var i=t.pendingProps;switch(Us(t),t.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return nt(t),null;case 1:return ut(t.type)&&Ol(),nt(t),null;case 3:return i=t.stateNode,Ir(),ze(at),ze(et),oa(),i.pendingContext&&(i.context=i.pendingContext,i.pendingContext=null),(e===null||e.child===null)&&(Hl(t)?t.flags|=4:e===null||e.memoizedState.isDehydrated&&(t.flags&256)===0||(t.flags|=1024,Dt!==null&&(La(Dt),Dt=null))),Ea(e,t),nt(t),null;case 5:na(t);var u=Jn(Oo.current);if(r=t.type,e!==null&&t.stateNode!=null)cf(e,t,r,i,u),e.ref!==t.ref&&(t.flags|=512,t.flags|=2097152);else{if(!i){if(t.stateNode===null)throw Error(l(166));return nt(t),null}if(e=Jn(Yt.current),Hl(t)){i=t.stateNode,r=t.type;var d=t.memoizedProps;switch(i[Gt]=t,i[zo]=d,e=(t.mode&1)!==0,r){case"dialog":je("cancel",i),je("close",i);break;case"iframe":case"object":case"embed":je("load",i);break;case"video":case"audio":for(u=0;u<_o.length;u++)je(_o[u],i);break;case"source":je("error",i);break;case"img":case"image":case"link":je("error",i),je("load",i);break;case"details":je("toggle",i);break;case"input":st(i,d),je("invalid",i);break;case"select":i._wrapperState={wasMultiple:!!d.multiple},je("invalid",i);break;case"textarea":fl(i,d),je("invalid",i)}rs(r,d),u=null;for(var y in d)if(d.hasOwnProperty(y)){var k=d[y];y==="children"?typeof k=="string"?i.textContent!==k&&(d.suppressHydrationWarning!==!0&&Ll(i.textContent,k,e),u=["children",k]):typeof k=="number"&&i.textContent!==""+k&&(d.suppressHydrationWarning!==!0&&Ll(i.textContent,k,e),u=["children",""+k]):a.hasOwnProperty(y)&&k!=null&&y==="onScroll"&&je("scroll",i)}switch(r){case"input":me(i),Ue(i,d,!0);break;case"textarea":me(i),Yu(i);break;case"select":case"option":break;default:typeof d.onClick=="function"&&(i.onclick=Ml)}i=u,t.updateQueue=i,i!==null&&(t.flags|=4)}else{y=u.nodeType===9?u:u.ownerDocument,e==="http://www.w3.org/1999/xhtml"&&(e=Xu(r)),e==="http://www.w3.org/1999/xhtml"?r==="script"?(e=y.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild)):typeof i.is=="string"?e=y.createElement(r,{is:i.is}):(e=y.createElement(r),r==="select"&&(y=e,i.multiple?y.multiple=!0:i.size&&(y.size=i.size))):e=y.createElementNS(e,r),e[Gt]=t,e[zo]=i,uf(e,t,!1,!1),t.stateNode=e;e:{switch(y=os(r,i),r){case"dialog":je("cancel",e),je("close",e),u=i;break;case"iframe":case"object":case"embed":je("load",e),u=i;break;case"video":case"audio":for(u=0;u<_o.length;u++)je(_o[u],e);u=i;break;case"source":je("error",e),u=i;break;case"img":case"image":case"link":je("error",e),je("load",e),u=i;break;case"details":je("toggle",e),u=i;break;case"input":st(e,i),u=_e(e,i),je("invalid",e);break;case"option":u=i;break;case"select":e._wrapperState={wasMultiple:!!i.multiple},u=Y({},i,{value:void 0}),je("invalid",e);break;case"textarea":fl(e,i),u=vr(e,i),je("invalid",e);break;default:u=i}rs(r,u),k=u;for(d in k)if(k.hasOwnProperty(d)){var R=k[d];d==="style"?Ju(e,R):d==="dangerouslySetInnerHTML"?(R=R?R.__html:void 0,R!=null&&Zu(e,R)):d==="children"?typeof R=="string"?(r!=="textarea"||R!=="")&&uo(e,R):typeof R=="number"&&uo(e,""+R):d!=="suppressContentEditableWarning"&&d!=="suppressHydrationWarning"&&d!=="autoFocus"&&(a.hasOwnProperty(d)?R!=null&&d==="onScroll"&&je("scroll",e):R!=null&&z(e,d,R,y))}switch(r){case"input":me(e),Ue(e,i,!1);break;case"textarea":me(e),Yu(e);break;case"option":i.value!=null&&e.setAttribute("value",""+X(i.value));break;case"select":e.multiple=!!i.multiple,d=i.value,d!=null?kt(e,!!i.multiple,d,!1):i.defaultValue!=null&&kt(e,!!i.multiple,i.defaultValue,!0);break;default:typeof u.onClick=="function"&&(e.onclick=Ml)}switch(r){case"button":case"input":case"select":case"textarea":i=!!i.autoFocus;break e;case"img":i=!0;break e;default:i=!1}}i&&(t.flags|=4)}t.ref!==null&&(t.flags|=512,t.flags|=2097152)}return nt(t),null;case 6:if(e&&t.stateNode!=null)df(e,t,e.memoizedProps,i);else{if(typeof i!="string"&&t.stateNode===null)throw Error(l(166));if(r=Jn(Oo.current),Jn(Yt.current),Hl(t)){if(i=t.stateNode,r=t.memoizedProps,i[Gt]=t,(d=i.nodeValue!==r)&&(e=xt,e!==null))switch(e.tag){case 3:Ll(i.nodeValue,r,(e.mode&1)!==0);break;case 5:e.memoizedProps.suppressHydrationWarning!==!0&&Ll(i.nodeValue,r,(e.mode&1)!==0)}d&&(t.flags|=4)}else i=(r.nodeType===9?r:r.ownerDocument).createTextNode(i),i[Gt]=t,t.stateNode=i}return nt(t),null;case 13:if(ze(Me),i=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(Le&&wt!==null&&(t.mode&1)!==0&&(t.flags&128)===0)pd(),zr(),t.flags|=98560,d=!1;else if(d=Hl(t),i!==null&&i.dehydrated!==null){if(e===null){if(!d)throw Error(l(318));if(d=t.memoizedState,d=d!==null?d.dehydrated:null,!d)throw Error(l(317));d[Gt]=t}else zr(),(t.flags&128)===0&&(t.memoizedState=null),t.flags|=4;nt(t),d=!1}else Dt!==null&&(La(Dt),Dt=null),d=!0;if(!d)return t.flags&65536?t:null}return(t.flags&128)!==0?(t.lanes=r,t):(i=i!==null,i!==(e!==null&&e.memoizedState!==null)&&i&&(t.child.flags|=8192,(t.mode&1)!==0&&(e===null||(Me.current&1)!==0?Qe===0&&(Qe=3):Oa())),t.updateQueue!==null&&(t.flags|=4),nt(t),null);case 4:return Ir(),Ea(e,t),e===null&&To(t.stateNode.containerInfo),nt(t),null;case 10:return Zs(t.type._context),nt(t),null;case 17:return ut(t.type)&&Ol(),nt(t),null;case 19:if(ze(Me),d=t.memoizedState,d===null)return nt(t),null;if(i=(t.flags&128)!==0,y=d.rendering,y===null)if(i)Ho(d,!1);else{if(Qe!==0||e!==null&&(e.flags&128)!==0)for(e=t.child;e!==null;){if(y=Gl(e),y!==null){for(t.flags|=128,Ho(d,!1),i=y.updateQueue,i!==null&&(t.updateQueue=i,t.flags|=4),t.subtreeFlags=0,i=r,r=t.child;r!==null;)d=r,e=i,d.flags&=14680066,y=d.alternate,y===null?(d.childLanes=0,d.lanes=e,d.child=null,d.subtreeFlags=0,d.memoizedProps=null,d.memoizedState=null,d.updateQueue=null,d.dependencies=null,d.stateNode=null):(d.childLanes=y.childLanes,d.lanes=y.lanes,d.child=y.child,d.subtreeFlags=0,d.deletions=null,d.memoizedProps=y.memoizedProps,d.memoizedState=y.memoizedState,d.updateQueue=y.updateQueue,d.type=y.type,e=y.dependencies,d.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),r=r.sibling;return Te(Me,Me.current&1|2),t.child}e=e.sibling}d.tail!==null&&$e()>Fr&&(t.flags|=128,i=!0,Ho(d,!1),t.lanes=4194304)}else{if(!i)if(e=Gl(y),e!==null){if(t.flags|=128,i=!0,r=e.updateQueue,r!==null&&(t.updateQueue=r,t.flags|=4),Ho(d,!0),d.tail===null&&d.tailMode==="hidden"&&!y.alternate&&!Le)return nt(t),null}else 2*$e()-d.renderingStartTime>Fr&&r!==1073741824&&(t.flags|=128,i=!0,Ho(d,!1),t.lanes=4194304);d.isBackwards?(y.sibling=t.child,t.child=y):(r=d.last,r!==null?r.sibling=y:t.child=y,d.last=y)}return d.tail!==null?(t=d.tail,d.rendering=t,d.tail=t.sibling,d.renderingStartTime=$e(),t.sibling=null,r=Me.current,Te(Me,i?r&1|2:r&1),t):(nt(t),null);case 22:case 23:return Ia(),i=t.memoizedState!==null,e!==null&&e.memoizedState!==null!==i&&(t.flags|=8192),i&&(t.mode&1)!==0?(St&1073741824)!==0&&(nt(t),t.subtreeFlags&6&&(t.flags|=8192)):nt(t),null;case 24:return null;case 25:return null}throw Error(l(156,t.tag))}function Wv(e,t){switch(Us(t),t.tag){case 1:return ut(t.type)&&Ol(),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return Ir(),ze(at),ze(et),oa(),e=t.flags,(e&65536)!==0&&(e&128)===0?(t.flags=e&-65537|128,t):null;case 5:return na(t),null;case 13:if(ze(Me),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(l(340));zr()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return ze(Me),null;case 4:return Ir(),null;case 10:return Zs(t.type._context),null;case 22:case 23:return Ia(),null;case 24:return null;default:return null}}var ri=!1,rt=!1,Hv=typeof WeakSet=="function"?WeakSet:Set,ue=null;function Dr(e,t){var r=e.ref;if(r!==null)if(typeof r=="function")try{r(null)}catch(i){Oe(e,t,i)}else r.current=null}function ba(e,t,r){try{r()}catch(i){Oe(e,t,i)}}var ff=!1;function Vv(e,t){if(Is=El,e=Vc(),Rs(e)){if("selectionStart"in e)var r={start:e.selectionStart,end:e.selectionEnd};else e:{r=(r=e.ownerDocument)&&r.defaultView||window;var i=r.getSelection&&r.getSelection();if(i&&i.rangeCount!==0){r=i.anchorNode;var u=i.anchorOffset,d=i.focusNode;i=i.focusOffset;try{r.nodeType,d.nodeType}catch{r=null;break e}var y=0,k=-1,R=-1,I=0,U=0,Q=e,B=null;t:for(;;){for(var ie;Q!==r||u!==0&&Q.nodeType!==3||(k=y+u),Q!==d||i!==0&&Q.nodeType!==3||(R=y+i),Q.nodeType===3&&(y+=Q.nodeValue.length),(ie=Q.firstChild)!==null;)B=Q,Q=ie;for(;;){if(Q===e)break t;if(B===r&&++I===u&&(k=y),B===d&&++U===i&&(R=y),(ie=Q.nextSibling)!==null)break;Q=B,B=Q.parentNode}Q=ie}r=k===-1||R===-1?null:{start:k,end:R}}else r=null}r=r||{start:0,end:0}}else r=null;for(Os={focusedElem:e,selectionRange:r},El=!1,ue=t;ue!==null;)if(t=ue,e=t.child,(t.subtreeFlags&1028)!==0&&e!==null)e.return=t,ue=e;else for(;ue!==null;){t=ue;try{var de=t.alternate;if((t.flags&1024)!==0)switch(t.tag){case 0:case 11:case 15:break;case 1:if(de!==null){var fe=de.memoizedProps,Fe=de.memoizedState,A=t.stateNode,T=A.getSnapshotBeforeUpdate(t.elementType===t.type?fe:$t(t.type,fe),Fe);A.__reactInternalSnapshotBeforeUpdate=T}break;case 3:var L=t.stateNode.containerInfo;L.nodeType===1?L.textContent="":L.nodeType===9&&L.documentElement&&L.removeChild(L.documentElement);break;case 5:case 6:case 4:case 17:break;default:throw Error(l(163))}}catch(Z){Oe(t,t.return,Z)}if(e=t.sibling,e!==null){e.return=t.return,ue=e;break}ue=t.return}return de=ff,ff=!1,de}function Vo(e,t,r){var i=t.updateQueue;if(i=i!==null?i.lastEffect:null,i!==null){var u=i=i.next;do{if((u.tag&e)===e){var d=u.destroy;u.destroy=void 0,d!==void 0&&ba(t,r,d)}u=u.next}while(u!==i)}}function oi(e,t){if(t=t.updateQueue,t=t!==null?t.lastEffect:null,t!==null){var r=t=t.next;do{if((r.tag&e)===e){var i=r.create;r.destroy=i()}r=r.next}while(r!==t)}}function ka(e){var t=e.ref;if(t!==null){var r=e.stateNode;switch(e.tag){case 5:e=r;break;default:e=r}typeof t=="function"?t(e):t.current=e}}function pf(e){var t=e.alternate;t!==null&&(e.alternate=null,pf(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&(delete t[Gt],delete t[zo],delete t[Ws],delete t[kv],delete t[Pv])),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}function mf(e){return e.tag===5||e.tag===3||e.tag===4}function hf(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||mf(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function Pa(e,t,r){var i=e.tag;if(i===5||i===6)e=e.stateNode,t?r.nodeType===8?r.parentNode.insertBefore(e,t):r.insertBefore(e,t):(r.nodeType===8?(t=r.parentNode,t.insertBefore(e,r)):(t=r,t.appendChild(e)),r=r._reactRootContainer,r!=null||t.onclick!==null||(t.onclick=Ml));else if(i!==4&&(e=e.child,e!==null))for(Pa(e,t,r),e=e.sibling;e!==null;)Pa(e,t,r),e=e.sibling}function Na(e,t,r){var i=e.tag;if(i===5||i===6)e=e.stateNode,t?r.insertBefore(e,t):r.appendChild(e);else if(i!==4&&(e=e.child,e!==null))for(Na(e,t,r),e=e.sibling;e!==null;)Na(e,t,r),e=e.sibling}var Ze=null,Ft=!1;function _n(e,t,r){for(r=r.child;r!==null;)gf(e,t,r),r=r.sibling}function gf(e,t,r){if(Qt&&typeof Qt.onCommitFiberUnmount=="function")try{Qt.onCommitFiberUnmount(vl,r)}catch{}switch(r.tag){case 5:rt||Dr(r,t);case 6:var i=Ze,u=Ft;Ze=null,_n(e,t,r),Ze=i,Ft=u,Ze!==null&&(Ft?(e=Ze,r=r.stateNode,e.nodeType===8?e.parentNode.removeChild(r):e.removeChild(r)):Ze.removeChild(r.stateNode));break;case 18:Ze!==null&&(Ft?(e=Ze,r=r.stateNode,e.nodeType===8?Fs(e.parentNode,r):e.nodeType===1&&Fs(e,r),So(e)):Fs(Ze,r.stateNode));break;case 4:i=Ze,u=Ft,Ze=r.stateNode.containerInfo,Ft=!0,_n(e,t,r),Ze=i,Ft=u;break;case 0:case 11:case 14:case 15:if(!rt&&(i=r.updateQueue,i!==null&&(i=i.lastEffect,i!==null))){u=i=i.next;do{var d=u,y=d.destroy;d=d.tag,y!==void 0&&((d&2)!==0||(d&4)!==0)&&ba(r,t,y),u=u.next}while(u!==i)}_n(e,t,r);break;case 1:if(!rt&&(Dr(r,t),i=r.stateNode,typeof i.componentWillUnmount=="function"))try{i.props=r.memoizedProps,i.state=r.memoizedState,i.componentWillUnmount()}catch(k){Oe(r,t,k)}_n(e,t,r);break;case 21:_n(e,t,r);break;case 22:r.mode&1?(rt=(i=rt)||r.memoizedState!==null,_n(e,t,r),rt=i):_n(e,t,r);break;default:_n(e,t,r)}}function vf(e){var t=e.updateQueue;if(t!==null){e.updateQueue=null;var r=e.stateNode;r===null&&(r=e.stateNode=new Hv),t.forEach(function(i){var u=qv.bind(null,e,i);r.has(i)||(r.add(i),i.then(u,u))})}}function Wt(e,t){var r=t.deletions;if(r!==null)for(var i=0;i<r.length;i++){var u=r[i];try{var d=e,y=t,k=y;e:for(;k!==null;){switch(k.tag){case 5:Ze=k.stateNode,Ft=!1;break e;case 3:Ze=k.stateNode.containerInfo,Ft=!0;break e;case 4:Ze=k.stateNode.containerInfo,Ft=!0;break e}k=k.return}if(Ze===null)throw Error(l(160));gf(d,y,u),Ze=null,Ft=!1;var R=u.alternate;R!==null&&(R.return=null),u.return=null}catch(I){Oe(u,t,I)}}if(t.subtreeFlags&12854)for(t=t.child;t!==null;)yf(t,e),t=t.sibling}function yf(e,t){var r=e.alternate,i=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:if(Wt(t,e),Zt(e),i&4){try{Vo(3,e,e.return),oi(3,e)}catch(fe){Oe(e,e.return,fe)}try{Vo(5,e,e.return)}catch(fe){Oe(e,e.return,fe)}}break;case 1:Wt(t,e),Zt(e),i&512&&r!==null&&Dr(r,r.return);break;case 5:if(Wt(t,e),Zt(e),i&512&&r!==null&&Dr(r,r.return),e.flags&32){var u=e.stateNode;try{uo(u,"")}catch(fe){Oe(e,e.return,fe)}}if(i&4&&(u=e.stateNode,u!=null)){var d=e.memoizedProps,y=r!==null?r.memoizedProps:d,k=e.type,R=e.updateQueue;if(e.updateQueue=null,R!==null)try{k==="input"&&d.type==="radio"&&d.name!=null&&gt(u,d),os(k,y);var I=os(k,d);for(y=0;y<R.length;y+=2){var U=R[y],Q=R[y+1];U==="style"?Ju(u,Q):U==="dangerouslySetInnerHTML"?Zu(u,Q):U==="children"?uo(u,Q):z(u,U,Q,I)}switch(k){case"input":vt(u,d);break;case"textarea":Gu(u,d);break;case"select":var B=u._wrapperState.wasMultiple;u._wrapperState.wasMultiple=!!d.multiple;var ie=d.value;ie!=null?kt(u,!!d.multiple,ie,!1):B!==!!d.multiple&&(d.defaultValue!=null?kt(u,!!d.multiple,d.defaultValue,!0):kt(u,!!d.multiple,d.multiple?[]:"",!1))}u[zo]=d}catch(fe){Oe(e,e.return,fe)}}break;case 6:if(Wt(t,e),Zt(e),i&4){if(e.stateNode===null)throw Error(l(162));u=e.stateNode,d=e.memoizedProps;try{u.nodeValue=d}catch(fe){Oe(e,e.return,fe)}}break;case 3:if(Wt(t,e),Zt(e),i&4&&r!==null&&r.memoizedState.isDehydrated)try{So(t.containerInfo)}catch(fe){Oe(e,e.return,fe)}break;case 4:Wt(t,e),Zt(e);break;case 13:Wt(t,e),Zt(e),u=e.child,u.flags&8192&&(d=u.memoizedState!==null,u.stateNode.isHidden=d,!d||u.alternate!==null&&u.alternate.memoizedState!==null||(Ta=$e())),i&4&&vf(e);break;case 22:if(U=r!==null&&r.memoizedState!==null,e.mode&1?(rt=(I=rt)||U,Wt(t,e),rt=I):Wt(t,e),Zt(e),i&8192){if(I=e.memoizedState!==null,(e.stateNode.isHidden=I)&&!U&&(e.mode&1)!==0)for(ue=e,U=e.child;U!==null;){for(Q=ue=U;ue!==null;){switch(B=ue,ie=B.child,B.tag){case 0:case 11:case 14:case 15:Vo(4,B,B.return);break;case 1:Dr(B,B.return);var de=B.stateNode;if(typeof de.componentWillUnmount=="function"){i=B,r=B.return;try{t=i,de.props=t.memoizedProps,de.state=t.memoizedState,de.componentWillUnmount()}catch(fe){Oe(i,r,fe)}}break;case 5:Dr(B,B.return);break;case 22:if(B.memoizedState!==null){Sf(Q);continue}}ie!==null?(ie.return=B,ue=ie):Sf(Q)}U=U.sibling}e:for(U=null,Q=e;;){if(Q.tag===5){if(U===null){U=Q;try{u=Q.stateNode,I?(d=u.style,typeof d.setProperty=="function"?d.setProperty("display","none","important"):d.display="none"):(k=Q.stateNode,R=Q.memoizedProps.style,y=R!=null&&R.hasOwnProperty("display")?R.display:null,k.style.display=qu("display",y))}catch(fe){Oe(e,e.return,fe)}}}else if(Q.tag===6){if(U===null)try{Q.stateNode.nodeValue=I?"":Q.memoizedProps}catch(fe){Oe(e,e.return,fe)}}else if((Q.tag!==22&&Q.tag!==23||Q.memoizedState===null||Q===e)&&Q.child!==null){Q.child.return=Q,Q=Q.child;continue}if(Q===e)break e;for(;Q.sibling===null;){if(Q.return===null||Q.return===e)break e;U===Q&&(U=null),Q=Q.return}U===Q&&(U=null),Q.sibling.return=Q.return,Q=Q.sibling}}break;case 19:Wt(t,e),Zt(e),i&4&&vf(e);break;case 21:break;default:Wt(t,e),Zt(e)}}function Zt(e){var t=e.flags;if(t&2){try{e:{for(var r=e.return;r!==null;){if(mf(r)){var i=r;break e}r=r.return}throw Error(l(160))}switch(i.tag){case 5:var u=i.stateNode;i.flags&32&&(uo(u,""),i.flags&=-33);var d=hf(e);Na(e,d,u);break;case 3:case 4:var y=i.stateNode.containerInfo,k=hf(e);Pa(e,k,y);break;default:throw Error(l(161))}}catch(R){Oe(e,e.return,R)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function Bv(e,t,r){ue=e,xf(e)}function xf(e,t,r){for(var i=(e.mode&1)!==0;ue!==null;){var u=ue,d=u.child;if(u.tag===22&&i){var y=u.memoizedState!==null||ri;if(!y){var k=u.alternate,R=k!==null&&k.memoizedState!==null||rt;k=ri;var I=rt;if(ri=y,(rt=R)&&!I)for(ue=u;ue!==null;)y=ue,R=y.child,y.tag===22&&y.memoizedState!==null?Cf(u):R!==null?(R.return=y,ue=R):Cf(u);for(;d!==null;)ue=d,xf(d),d=d.sibling;ue=u,ri=k,rt=I}wf(e)}else(u.subtreeFlags&8772)!==0&&d!==null?(d.return=u,ue=d):wf(e)}}function wf(e){for(;ue!==null;){var t=ue;if((t.flags&8772)!==0){var r=t.alternate;try{if((t.flags&8772)!==0)switch(t.tag){case 0:case 11:case 15:rt||oi(5,t);break;case 1:var i=t.stateNode;if(t.flags&4&&!rt)if(r===null)i.componentDidMount();else{var u=t.elementType===t.type?r.memoizedProps:$t(t.type,r.memoizedProps);i.componentDidUpdate(u,r.memoizedState,i.__reactInternalSnapshotBeforeUpdate)}var d=t.updateQueue;d!==null&&wd(t,d,i);break;case 3:var y=t.updateQueue;if(y!==null){if(r=null,t.child!==null)switch(t.child.tag){case 5:r=t.child.stateNode;break;case 1:r=t.child.stateNode}wd(t,y,r)}break;case 5:var k=t.stateNode;if(r===null&&t.flags&4){r=k;var R=t.memoizedProps;switch(t.type){case"button":case"input":case"select":case"textarea":R.autoFocus&&r.focus();break;case"img":R.src&&(r.src=R.src)}}break;case 6:break;case 4:break;case 12:break;case 13:if(t.memoizedState===null){var I=t.alternate;if(I!==null){var U=I.memoizedState;if(U!==null){var Q=U.dehydrated;Q!==null&&So(Q)}}}break;case 19:case 17:case 21:case 22:case 23:case 25:break;default:throw Error(l(163))}rt||t.flags&512&&ka(t)}catch(B){Oe(t,t.return,B)}}if(t===e){ue=null;break}if(r=t.sibling,r!==null){r.return=t.return,ue=r;break}ue=t.return}}function Sf(e){for(;ue!==null;){var t=ue;if(t===e){ue=null;break}var r=t.sibling;if(r!==null){r.return=t.return,ue=r;break}ue=t.return}}function Cf(e){for(;ue!==null;){var t=ue;try{switch(t.tag){case 0:case 11:case 15:var r=t.return;try{oi(4,t)}catch(R){Oe(t,r,R)}break;case 1:var i=t.stateNode;if(typeof i.componentDidMount=="function"){var u=t.return;try{i.componentDidMount()}catch(R){Oe(t,u,R)}}var d=t.return;try{ka(t)}catch(R){Oe(t,d,R)}break;case 5:var y=t.return;try{ka(t)}catch(R){Oe(t,y,R)}}}catch(R){Oe(t,t.return,R)}if(t===e){ue=null;break}var k=t.sibling;if(k!==null){k.return=t.return,ue=k;break}ue=t.return}}var Uv=Math.ceil,li=M.ReactCurrentDispatcher,Ra=M.ReactCurrentOwner,Tt=M.ReactCurrentBatchConfig,Pe=0,Ye=null,He=null,qe=0,St=0,$r=bn(0),Qe=0,Bo=null,tr=0,ii=0,_a=0,Uo=null,dt=null,Ta=0,Fr=1/0,fn=null,si=!1,ja=null,Tn=null,ai=!1,jn=null,ui=0,Ko=0,za=null,ci=-1,di=0;function it(){return(Pe&6)!==0?$e():ci!==-1?ci:ci=$e()}function zn(e){return(e.mode&1)===0?1:(Pe&2)!==0&&qe!==0?qe&-qe:Rv.transition!==null?(di===0&&(di=mc()),di):(e=Re,e!==0||(e=window.event,e=e===void 0?16:Ec(e.type)),e)}function Ht(e,t,r,i){if(50<Ko)throw Ko=0,za=null,Error(l(185));go(e,r,i),((Pe&2)===0||e!==Ye)&&(e===Ye&&((Pe&2)===0&&(ii|=r),Qe===4&&An(e,qe)),ft(e,i),r===1&&Pe===0&&(t.mode&1)===0&&(Fr=$e()+500,$l&&Pn()))}function ft(e,t){var r=e.callbackNode;Rg(e,t);var i=wl(e,e===Ye?qe:0);if(i===0)r!==null&&dc(r),e.callbackNode=null,e.callbackPriority=0;else if(t=i&-i,e.callbackPriority!==t){if(r!=null&&dc(r),t===1)e.tag===0?Nv(bf.bind(null,e)):ad(bf.bind(null,e)),Ev(function(){(Pe&6)===0&&Pn()}),r=null;else{switch(hc(i)){case 1:r=ds;break;case 4:r=fc;break;case 16:r=gl;break;case 536870912:r=pc;break;default:r=gl}r=zf(r,Ef.bind(null,e))}e.callbackPriority=t,e.callbackNode=r}}function Ef(e,t){if(ci=-1,di=0,(Pe&6)!==0)throw Error(l(327));var r=e.callbackNode;if(Wr()&&e.callbackNode!==r)return null;var i=wl(e,e===Ye?qe:0);if(i===0)return null;if((i&30)!==0||(i&e.expiredLanes)!==0||t)t=fi(e,i);else{t=i;var u=Pe;Pe|=2;var d=Pf();(Ye!==e||qe!==t)&&(fn=null,Fr=$e()+500,rr(e,t));do try{Gv();break}catch(k){kf(e,k)}while(!0);Xs(),li.current=d,Pe=u,He!==null?t=0:(Ye=null,qe=0,t=Qe)}if(t!==0){if(t===2&&(u=fs(e),u!==0&&(i=u,t=Aa(e,u))),t===1)throw r=Bo,rr(e,0),An(e,i),ft(e,$e()),r;if(t===6)An(e,i);else{if(u=e.current.alternate,(i&30)===0&&!Kv(u)&&(t=fi(e,i),t===2&&(d=fs(e),d!==0&&(i=d,t=Aa(e,d))),t===1))throw r=Bo,rr(e,0),An(e,i),ft(e,$e()),r;switch(e.finishedWork=u,e.finishedLanes=i,t){case 0:case 1:throw Error(l(345));case 2:or(e,dt,fn);break;case 3:if(An(e,i),(i&130023424)===i&&(t=Ta+500-$e(),10<t)){if(wl(e,0)!==0)break;if(u=e.suspendedLanes,(u&i)!==i){it(),e.pingedLanes|=e.suspendedLanes&u;break}e.timeoutHandle=$s(or.bind(null,e,dt,fn),t);break}or(e,dt,fn);break;case 4:if(An(e,i),(i&4194240)===i)break;for(t=e.eventTimes,u=-1;0<i;){var y=31-It(i);d=1<<y,y=t[y],y>u&&(u=y),i&=~d}if(i=u,i=$e()-i,i=(120>i?120:480>i?480:1080>i?1080:1920>i?1920:3e3>i?3e3:4320>i?4320:1960*Uv(i/1960))-i,10<i){e.timeoutHandle=$s(or.bind(null,e,dt,fn),i);break}or(e,dt,fn);break;case 5:or(e,dt,fn);break;default:throw Error(l(329))}}}return ft(e,$e()),e.callbackNode===r?Ef.bind(null,e):null}function Aa(e,t){var r=Uo;return e.current.memoizedState.isDehydrated&&(rr(e,t).flags|=256),e=fi(e,t),e!==2&&(t=dt,dt=r,t!==null&&La(t)),e}function La(e){dt===null?dt=e:dt.push.apply(dt,e)}function Kv(e){for(var t=e;;){if(t.flags&16384){var r=t.updateQueue;if(r!==null&&(r=r.stores,r!==null))for(var i=0;i<r.length;i++){var u=r[i],d=u.getSnapshot;u=u.value;try{if(!Ot(d(),u))return!1}catch{return!1}}}if(r=t.child,t.subtreeFlags&16384&&r!==null)r.return=t,t=r;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function An(e,t){for(t&=~_a,t&=~ii,e.suspendedLanes|=t,e.pingedLanes&=~t,e=e.expirationTimes;0<t;){var r=31-It(t),i=1<<r;e[r]=-1,t&=~i}}function bf(e){if((Pe&6)!==0)throw Error(l(327));Wr();var t=wl(e,0);if((t&1)===0)return ft(e,$e()),null;var r=fi(e,t);if(e.tag!==0&&r===2){var i=fs(e);i!==0&&(t=i,r=Aa(e,i))}if(r===1)throw r=Bo,rr(e,0),An(e,t),ft(e,$e()),r;if(r===6)throw Error(l(345));return e.finishedWork=e.current.alternate,e.finishedLanes=t,or(e,dt,fn),ft(e,$e()),null}function Ma(e,t){var r=Pe;Pe|=1;try{return e(t)}finally{Pe=r,Pe===0&&(Fr=$e()+500,$l&&Pn())}}function nr(e){jn!==null&&jn.tag===0&&(Pe&6)===0&&Wr();var t=Pe;Pe|=1;var r=Tt.transition,i=Re;try{if(Tt.transition=null,Re=1,e)return e()}finally{Re=i,Tt.transition=r,Pe=t,(Pe&6)===0&&Pn()}}function Ia(){St=$r.current,ze($r)}function rr(e,t){e.finishedWork=null,e.finishedLanes=0;var r=e.timeoutHandle;if(r!==-1&&(e.timeoutHandle=-1,Cv(r)),He!==null)for(r=He.return;r!==null;){var i=r;switch(Us(i),i.tag){case 1:i=i.type.childContextTypes,i!=null&&Ol();break;case 3:Ir(),ze(at),ze(et),oa();break;case 5:na(i);break;case 4:Ir();break;case 13:ze(Me);break;case 19:ze(Me);break;case 10:Zs(i.type._context);break;case 22:case 23:Ia()}r=r.return}if(Ye=e,He=e=Ln(e.current,null),qe=St=t,Qe=0,Bo=null,_a=ii=tr=0,dt=Uo=null,qn!==null){for(t=0;t<qn.length;t++)if(r=qn[t],i=r.interleaved,i!==null){r.interleaved=null;var u=i.next,d=r.pending;if(d!==null){var y=d.next;d.next=u,i.next=y}r.pending=i}qn=null}return e}function kf(e,t){do{var r=He;try{if(Xs(),Yl.current=Jl,Xl){for(var i=Ie.memoizedState;i!==null;){var u=i.queue;u!==null&&(u.pending=null),i=i.next}Xl=!1}if(er=0,Ge=Ke=Ie=null,Do=!1,$o=0,Ra.current=null,r===null||r.return===null){Qe=1,Bo=t,He=null;break}e:{var d=e,y=r.return,k=r,R=t;if(t=qe,k.flags|=32768,R!==null&&typeof R=="object"&&typeof R.then=="function"){var I=R,U=k,Q=U.tag;if((U.mode&1)===0&&(Q===0||Q===11||Q===15)){var B=U.alternate;B?(U.updateQueue=B.updateQueue,U.memoizedState=B.memoizedState,U.lanes=B.lanes):(U.updateQueue=null,U.memoizedState=null)}var ie=Yd(y);if(ie!==null){ie.flags&=-257,Xd(ie,y,k,d,t),ie.mode&1&&Gd(d,I,t),t=ie,R=I;var de=t.updateQueue;if(de===null){var fe=new Set;fe.add(R),t.updateQueue=fe}else de.add(R);break e}else{if((t&1)===0){Gd(d,I,t),Oa();break e}R=Error(l(426))}}else if(Le&&k.mode&1){var Fe=Yd(y);if(Fe!==null){(Fe.flags&65536)===0&&(Fe.flags|=256),Xd(Fe,y,k,d,t),Gs(Or(R,k));break e}}d=R=Or(R,k),Qe!==4&&(Qe=2),Uo===null?Uo=[d]:Uo.push(d),d=y;do{switch(d.tag){case 3:d.flags|=65536,t&=-t,d.lanes|=t;var A=Kd(d,R,t);xd(d,A);break e;case 1:k=R;var T=d.type,L=d.stateNode;if((d.flags&128)===0&&(typeof T.getDerivedStateFromError=="function"||L!==null&&typeof L.componentDidCatch=="function"&&(Tn===null||!Tn.has(L)))){d.flags|=65536,t&=-t,d.lanes|=t;var Z=Qd(d,k,t);xd(d,Z);break e}}d=d.return}while(d!==null)}Rf(r)}catch(he){t=he,He===r&&r!==null&&(He=r=r.return);continue}break}while(!0)}function Pf(){var e=li.current;return li.current=Jl,e===null?Jl:e}function Oa(){(Qe===0||Qe===3||Qe===2)&&(Qe=4),Ye===null||(tr&268435455)===0&&(ii&268435455)===0||An(Ye,qe)}function fi(e,t){var r=Pe;Pe|=2;var i=Pf();(Ye!==e||qe!==t)&&(fn=null,rr(e,t));do try{Qv();break}catch(u){kf(e,u)}while(!0);if(Xs(),Pe=r,li.current=i,He!==null)throw Error(l(261));return Ye=null,qe=0,Qe}function Qv(){for(;He!==null;)Nf(He)}function Gv(){for(;He!==null&&!xg();)Nf(He)}function Nf(e){var t=jf(e.alternate,e,St);e.memoizedProps=e.pendingProps,t===null?Rf(e):He=t,Ra.current=null}function Rf(e){var t=e;do{var r=t.alternate;if(e=t.return,(t.flags&32768)===0){if(r=Fv(r,t,St),r!==null){He=r;return}}else{if(r=Wv(r,t),r!==null){r.flags&=32767,He=r;return}if(e!==null)e.flags|=32768,e.subtreeFlags=0,e.deletions=null;else{Qe=6,He=null;return}}if(t=t.sibling,t!==null){He=t;return}He=t=e}while(t!==null);Qe===0&&(Qe=5)}function or(e,t,r){var i=Re,u=Tt.transition;try{Tt.transition=null,Re=1,Yv(e,t,r,i)}finally{Tt.transition=u,Re=i}return null}function Yv(e,t,r,i){do Wr();while(jn!==null);if((Pe&6)!==0)throw Error(l(327));r=e.finishedWork;var u=e.finishedLanes;if(r===null)return null;if(e.finishedWork=null,e.finishedLanes=0,r===e.current)throw Error(l(177));e.callbackNode=null,e.callbackPriority=0;var d=r.lanes|r.childLanes;if(_g(e,d),e===Ye&&(He=Ye=null,qe=0),(r.subtreeFlags&2064)===0&&(r.flags&2064)===0||ai||(ai=!0,zf(gl,function(){return Wr(),null})),d=(r.flags&15990)!==0,(r.subtreeFlags&15990)!==0||d){d=Tt.transition,Tt.transition=null;var y=Re;Re=1;var k=Pe;Pe|=4,Ra.current=null,Vv(e,r),yf(r,e),hv(Os),El=!!Is,Os=Is=null,e.current=r,Bv(r),wg(),Pe=k,Re=y,Tt.transition=d}else e.current=r;if(ai&&(ai=!1,jn=e,ui=u),d=e.pendingLanes,d===0&&(Tn=null),Eg(r.stateNode),ft(e,$e()),t!==null)for(i=e.onRecoverableError,r=0;r<t.length;r++)u=t[r],i(u.value,{componentStack:u.stack,digest:u.digest});if(si)throw si=!1,e=ja,ja=null,e;return(ui&1)!==0&&e.tag!==0&&Wr(),d=e.pendingLanes,(d&1)!==0?e===za?Ko++:(Ko=0,za=e):Ko=0,Pn(),null}function Wr(){if(jn!==null){var e=hc(ui),t=Tt.transition,r=Re;try{if(Tt.transition=null,Re=16>e?16:e,jn===null)var i=!1;else{if(e=jn,jn=null,ui=0,(Pe&6)!==0)throw Error(l(331));var u=Pe;for(Pe|=4,ue=e.current;ue!==null;){var d=ue,y=d.child;if((ue.flags&16)!==0){var k=d.deletions;if(k!==null){for(var R=0;R<k.length;R++){var I=k[R];for(ue=I;ue!==null;){var U=ue;switch(U.tag){case 0:case 11:case 15:Vo(8,U,d)}var Q=U.child;if(Q!==null)Q.return=U,ue=Q;else for(;ue!==null;){U=ue;var B=U.sibling,ie=U.return;if(pf(U),U===I){ue=null;break}if(B!==null){B.return=ie,ue=B;break}ue=ie}}}var de=d.alternate;if(de!==null){var fe=de.child;if(fe!==null){de.child=null;do{var Fe=fe.sibling;fe.sibling=null,fe=Fe}while(fe!==null)}}ue=d}}if((d.subtreeFlags&2064)!==0&&y!==null)y.return=d,ue=y;else e:for(;ue!==null;){if(d=ue,(d.flags&2048)!==0)switch(d.tag){case 0:case 11:case 15:Vo(9,d,d.return)}var A=d.sibling;if(A!==null){A.return=d.return,ue=A;break e}ue=d.return}}var T=e.current;for(ue=T;ue!==null;){y=ue;var L=y.child;if((y.subtreeFlags&2064)!==0&&L!==null)L.return=y,ue=L;else e:for(y=T;ue!==null;){if(k=ue,(k.flags&2048)!==0)try{switch(k.tag){case 0:case 11:case 15:oi(9,k)}}catch(he){Oe(k,k.return,he)}if(k===y){ue=null;break e}var Z=k.sibling;if(Z!==null){Z.return=k.return,ue=Z;break e}ue=k.return}}if(Pe=u,Pn(),Qt&&typeof Qt.onPostCommitFiberRoot=="function")try{Qt.onPostCommitFiberRoot(vl,e)}catch{}i=!0}return i}finally{Re=r,Tt.transition=t}}return!1}function _f(e,t,r){t=Or(r,t),t=Kd(e,t,1),e=Rn(e,t,1),t=it(),e!==null&&(go(e,1,t),ft(e,t))}function Oe(e,t,r){if(e.tag===3)_f(e,e,r);else for(;t!==null;){if(t.tag===3){_f(t,e,r);break}else if(t.tag===1){var i=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof i.componentDidCatch=="function"&&(Tn===null||!Tn.has(i))){e=Or(r,e),e=Qd(t,e,1),t=Rn(t,e,1),e=it(),t!==null&&(go(t,1,e),ft(t,e));break}}t=t.return}}function Xv(e,t,r){var i=e.pingCache;i!==null&&i.delete(t),t=it(),e.pingedLanes|=e.suspendedLanes&r,Ye===e&&(qe&r)===r&&(Qe===4||Qe===3&&(qe&130023424)===qe&&500>$e()-Ta?rr(e,0):_a|=r),ft(e,t)}function Tf(e,t){t===0&&((e.mode&1)===0?t=1:(t=xl,xl<<=1,(xl&130023424)===0&&(xl=4194304)));var r=it();e=un(e,t),e!==null&&(go(e,t,r),ft(e,r))}function Zv(e){var t=e.memoizedState,r=0;t!==null&&(r=t.retryLane),Tf(e,r)}function qv(e,t){var r=0;switch(e.tag){case 13:var i=e.stateNode,u=e.memoizedState;u!==null&&(r=u.retryLane);break;case 19:i=e.stateNode;break;default:throw Error(l(314))}i!==null&&i.delete(t),Tf(e,r)}var jf;jf=function(e,t,r){if(e!==null)if(e.memoizedProps!==t.pendingProps||at.current)ct=!0;else{if((e.lanes&r)===0&&(t.flags&128)===0)return ct=!1,$v(e,t,r);ct=(e.flags&131072)!==0}else ct=!1,Le&&(t.flags&1048576)!==0&&ud(t,Wl,t.index);switch(t.lanes=0,t.tag){case 2:var i=t.type;ni(e,t),e=t.pendingProps;var u=_r(t,et.current);Mr(t,r),u=sa(null,t,i,e,u,r);var d=aa();return t.flags|=1,typeof u=="object"&&u!==null&&typeof u.render=="function"&&u.$$typeof===void 0?(t.tag=1,t.memoizedState=null,t.updateQueue=null,ut(i)?(d=!0,Dl(t)):d=!1,t.memoizedState=u.state!==null&&u.state!==void 0?u.state:null,ea(t),u.updater=ei,t.stateNode=u,u._reactInternals=t,ma(t,i,e,r),t=ya(null,t,i,!0,d,r)):(t.tag=0,Le&&d&&Bs(t),lt(null,t,u,r),t=t.child),t;case 16:i=t.elementType;e:{switch(ni(e,t),e=t.pendingProps,u=i._init,i=u(i._payload),t.type=i,u=t.tag=ey(i),e=$t(i,e),u){case 0:t=va(null,t,i,e,r);break e;case 1:t=nf(null,t,i,e,r);break e;case 11:t=Zd(null,t,i,e,r);break e;case 14:t=qd(null,t,i,$t(i.type,e),r);break e}throw Error(l(306,i,""))}return t;case 0:return i=t.type,u=t.pendingProps,u=t.elementType===i?u:$t(i,u),va(e,t,i,u,r);case 1:return i=t.type,u=t.pendingProps,u=t.elementType===i?u:$t(i,u),nf(e,t,i,u,r);case 3:e:{if(rf(t),e===null)throw Error(l(387));i=t.pendingProps,d=t.memoizedState,u=d.element,yd(e,t),Ql(t,i,null,r);var y=t.memoizedState;if(i=y.element,d.isDehydrated)if(d={element:i,isDehydrated:!1,cache:y.cache,pendingSuspenseBoundaries:y.pendingSuspenseBoundaries,transitions:y.transitions},t.updateQueue.baseState=d,t.memoizedState=d,t.flags&256){u=Or(Error(l(423)),t),t=of(e,t,i,r,u);break e}else if(i!==u){u=Or(Error(l(424)),t),t=of(e,t,i,r,u);break e}else for(wt=En(t.stateNode.containerInfo.firstChild),xt=t,Le=!0,Dt=null,r=gd(t,null,i,r),t.child=r;r;)r.flags=r.flags&-3|4096,r=r.sibling;else{if(zr(),i===u){t=dn(e,t,r);break e}lt(e,t,i,r)}t=t.child}return t;case 5:return Sd(t),e===null&&Qs(t),i=t.type,u=t.pendingProps,d=e!==null?e.memoizedProps:null,y=u.children,Ds(i,u)?y=null:d!==null&&Ds(i,d)&&(t.flags|=32),tf(e,t),lt(e,t,y,r),t.child;case 6:return e===null&&Qs(t),null;case 13:return lf(e,t,r);case 4:return ta(t,t.stateNode.containerInfo),i=t.pendingProps,e===null?t.child=Ar(t,null,i,r):lt(e,t,i,r),t.child;case 11:return i=t.type,u=t.pendingProps,u=t.elementType===i?u:$t(i,u),Zd(e,t,i,u,r);case 7:return lt(e,t,t.pendingProps,r),t.child;case 8:return lt(e,t,t.pendingProps.children,r),t.child;case 12:return lt(e,t,t.pendingProps.children,r),t.child;case 10:e:{if(i=t.type._context,u=t.pendingProps,d=t.memoizedProps,y=u.value,Te(Bl,i._currentValue),i._currentValue=y,d!==null)if(Ot(d.value,y)){if(d.children===u.children&&!at.current){t=dn(e,t,r);break e}}else for(d=t.child,d!==null&&(d.return=t);d!==null;){var k=d.dependencies;if(k!==null){y=d.child;for(var R=k.firstContext;R!==null;){if(R.context===i){if(d.tag===1){R=cn(-1,r&-r),R.tag=2;var I=d.updateQueue;if(I!==null){I=I.shared;var U=I.pending;U===null?R.next=R:(R.next=U.next,U.next=R),I.pending=R}}d.lanes|=r,R=d.alternate,R!==null&&(R.lanes|=r),qs(d.return,r,t),k.lanes|=r;break}R=R.next}}else if(d.tag===10)y=d.type===t.type?null:d.child;else if(d.tag===18){if(y=d.return,y===null)throw Error(l(341));y.lanes|=r,k=y.alternate,k!==null&&(k.lanes|=r),qs(y,r,t),y=d.sibling}else y=d.child;if(y!==null)y.return=d;else for(y=d;y!==null;){if(y===t){y=null;break}if(d=y.sibling,d!==null){d.return=y.return,y=d;break}y=y.return}d=y}lt(e,t,u.children,r),t=t.child}return t;case 9:return u=t.type,i=t.pendingProps.children,Mr(t,r),u=Rt(u),i=i(u),t.flags|=1,lt(e,t,i,r),t.child;case 14:return i=t.type,u=$t(i,t.pendingProps),u=$t(i.type,u),qd(e,t,i,u,r);case 15:return Jd(e,t,t.type,t.pendingProps,r);case 17:return i=t.type,u=t.pendingProps,u=t.elementType===i?u:$t(i,u),ni(e,t),t.tag=1,ut(i)?(e=!0,Dl(t)):e=!1,Mr(t,r),Bd(t,i,u),ma(t,i,u,r),ya(null,t,i,!0,e,r);case 19:return af(e,t,r);case 22:return ef(e,t,r)}throw Error(l(156,t.tag))};function zf(e,t){return cc(e,t)}function Jv(e,t,r,i){this.tag=e,this.key=r,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=i,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function jt(e,t,r,i){return new Jv(e,t,r,i)}function Da(e){return e=e.prototype,!(!e||!e.isReactComponent)}function ey(e){if(typeof e=="function")return Da(e)?1:0;if(e!=null){if(e=e.$$typeof,e===q)return 11;if(e===ge)return 14}return 2}function Ln(e,t){var r=e.alternate;return r===null?(r=jt(e.tag,t,e.key,e.mode),r.elementType=e.elementType,r.type=e.type,r.stateNode=e.stateNode,r.alternate=e,e.alternate=r):(r.pendingProps=t,r.type=e.type,r.flags=0,r.subtreeFlags=0,r.deletions=null),r.flags=e.flags&14680064,r.childLanes=e.childLanes,r.lanes=e.lanes,r.child=e.child,r.memoizedProps=e.memoizedProps,r.memoizedState=e.memoizedState,r.updateQueue=e.updateQueue,t=e.dependencies,r.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},r.sibling=e.sibling,r.index=e.index,r.ref=e.ref,r}function pi(e,t,r,i,u,d){var y=2;if(i=e,typeof e=="function")Da(e)&&(y=1);else if(typeof e=="string")y=5;else e:switch(e){case O:return lr(r.children,u,d,t);case ee:y=8,u|=8;break;case re:return e=jt(12,r,t,u|2),e.elementType=re,e.lanes=d,e;case ve:return e=jt(13,r,t,u),e.elementType=ve,e.lanes=d,e;case se:return e=jt(19,r,t,u),e.elementType=se,e.lanes=d,e;case ce:return mi(r,u,d,t);default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case ae:y=10;break e;case pe:y=9;break e;case q:y=11;break e;case ge:y=14;break e;case le:y=16,i=null;break e}throw Error(l(130,e==null?e:typeof e,""))}return t=jt(y,r,t,u),t.elementType=e,t.type=i,t.lanes=d,t}function lr(e,t,r,i){return e=jt(7,e,i,t),e.lanes=r,e}function mi(e,t,r,i){return e=jt(22,e,i,t),e.elementType=ce,e.lanes=r,e.stateNode={isHidden:!1},e}function $a(e,t,r){return e=jt(6,e,null,t),e.lanes=r,e}function Fa(e,t,r){return t=jt(4,e.children!==null?e.children:[],e.key,t),t.lanes=r,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function ty(e,t,r,i,u){this.tag=t,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=ps(0),this.expirationTimes=ps(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=ps(0),this.identifierPrefix=i,this.onRecoverableError=u,this.mutableSourceEagerHydrationData=null}function Wa(e,t,r,i,u,d,y,k,R){return e=new ty(e,t,r,k,R),t===1?(t=1,d===!0&&(t|=8)):t=0,d=jt(3,null,null,t),e.current=d,d.stateNode=e,d.memoizedState={element:i,isDehydrated:r,cache:null,transitions:null,pendingSuspenseBoundaries:null},ea(d),e}function ny(e,t,r){var i=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:V,key:i==null?null:""+i,children:e,containerInfo:t,implementation:r}}function Af(e){if(!e)return kn;e=e._reactInternals;e:{if(Qn(e)!==e||e.tag!==1)throw Error(l(170));var t=e;do{switch(t.tag){case 3:t=t.stateNode.context;break e;case 1:if(ut(t.type)){t=t.stateNode.__reactInternalMemoizedMergedChildContext;break e}}t=t.return}while(t!==null);throw Error(l(171))}if(e.tag===1){var r=e.type;if(ut(r))return id(e,r,t)}return t}function Lf(e,t,r,i,u,d,y,k,R){return e=Wa(r,i,!0,e,u,d,y,k,R),e.context=Af(null),r=e.current,i=it(),u=zn(r),d=cn(i,u),d.callback=t??null,Rn(r,d,u),e.current.lanes=u,go(e,u,i),ft(e,i),e}function hi(e,t,r,i){var u=t.current,d=it(),y=zn(u);return r=Af(r),t.context===null?t.context=r:t.pendingContext=r,t=cn(d,y),t.payload={element:e},i=i===void 0?null:i,i!==null&&(t.callback=i),e=Rn(u,t,y),e!==null&&(Ht(e,u,y,d),Kl(e,u,y)),y}function gi(e){if(e=e.current,!e.child)return null;switch(e.child.tag){case 5:return e.child.stateNode;default:return e.child.stateNode}}function Mf(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var r=e.retryLane;e.retryLane=r!==0&&r<t?r:t}}function Ha(e,t){Mf(e,t),(e=e.alternate)&&Mf(e,t)}function ry(){return null}var If=typeof reportError=="function"?reportError:function(e){console.error(e)};function Va(e){this._internalRoot=e}vi.prototype.render=Va.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(l(409));hi(e,t,null,null)},vi.prototype.unmount=Va.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;nr(function(){hi(null,e,null,null)}),t[on]=null}};function vi(e){this._internalRoot=e}vi.prototype.unstable_scheduleHydration=function(e){if(e){var t=yc();e={blockedOn:null,target:e,priority:t};for(var r=0;r<wn.length&&t!==0&&t<wn[r].priority;r++);wn.splice(r,0,e),r===0&&Sc(e)}};function Ba(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function yi(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11&&(e.nodeType!==8||e.nodeValue!==" react-mount-point-unstable "))}function Of(){}function oy(e,t,r,i,u){if(u){if(typeof i=="function"){var d=i;i=function(){var I=gi(y);d.call(I)}}var y=Lf(t,i,e,0,null,!1,!1,"",Of);return e._reactRootContainer=y,e[on]=y.current,To(e.nodeType===8?e.parentNode:e),nr(),y}for(;u=e.lastChild;)e.removeChild(u);if(typeof i=="function"){var k=i;i=function(){var I=gi(R);k.call(I)}}var R=Wa(e,0,!1,null,null,!1,!1,"",Of);return e._reactRootContainer=R,e[on]=R.current,To(e.nodeType===8?e.parentNode:e),nr(function(){hi(t,R,r,i)}),R}function xi(e,t,r,i,u){var d=r._reactRootContainer;if(d){var y=d;if(typeof u=="function"){var k=u;u=function(){var R=gi(y);k.call(R)}}hi(t,y,e,u)}else y=oy(r,t,e,u,i);return gi(y)}gc=function(e){switch(e.tag){case 3:var t=e.stateNode;if(t.current.memoizedState.isDehydrated){var r=ho(t.pendingLanes);r!==0&&(ms(t,r|1),ft(t,$e()),(Pe&6)===0&&(Fr=$e()+500,Pn()))}break;case 13:nr(function(){var i=un(e,1);if(i!==null){var u=it();Ht(i,e,1,u)}}),Ha(e,1)}},hs=function(e){if(e.tag===13){var t=un(e,134217728);if(t!==null){var r=it();Ht(t,e,134217728,r)}Ha(e,134217728)}},vc=function(e){if(e.tag===13){var t=zn(e),r=un(e,t);if(r!==null){var i=it();Ht(r,e,t,i)}Ha(e,t)}},yc=function(){return Re},xc=function(e,t){var r=Re;try{return Re=e,t()}finally{Re=r}},ss=function(e,t,r){switch(t){case"input":if(vt(e,r),t=r.name,r.type==="radio"&&t!=null){for(r=e;r.parentNode;)r=r.parentNode;for(r=r.querySelectorAll("input[name="+JSON.stringify(""+t)+'][type="radio"]'),t=0;t<r.length;t++){var i=r[t];if(i!==e&&i.form===e.form){var u=Il(i);if(!u)throw Error(l(90));ye(i),vt(i,u)}}}break;case"textarea":Gu(e,r);break;case"select":t=r.value,t!=null&&kt(e,!!r.multiple,t,!1)}},rc=Ma,oc=nr;var ly={usingClientEntryPoint:!1,Events:[Ao,Nr,Il,tc,nc,Ma]},Qo={findFiberByHostInstance:Gn,bundleType:0,version:"18.3.1",rendererPackageName:"react-dom"},iy={bundleType:Qo.bundleType,version:Qo.version,rendererPackageName:Qo.rendererPackageName,rendererConfig:Qo.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:M.ReactCurrentDispatcher,findHostInstanceByFiber:function(e){return e=ac(e),e===null?null:e.stateNode},findFiberByHostInstance:Qo.findFiberByHostInstance||ry,findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.3.1-next-f1338f8080-20240426"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var wi=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!wi.isDisabled&&wi.supportsFiber)try{vl=wi.inject(iy),Qt=wi}catch{}}return pt.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=ly,pt.createPortal=function(e,t){var r=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!Ba(t))throw Error(l(200));return ny(e,t,null,r)},pt.createRoot=function(e,t){if(!Ba(e))throw Error(l(299));var r=!1,i="",u=If;return t!=null&&(t.unstable_strictMode===!0&&(r=!0),t.identifierPrefix!==void 0&&(i=t.identifierPrefix),t.onRecoverableError!==void 0&&(u=t.onRecoverableError)),t=Wa(e,1,!1,null,null,r,!1,i,u),e[on]=t.current,To(e.nodeType===8?e.parentNode:e),new Va(t)},pt.findDOMNode=function(e){if(e==null)return null;if(e.nodeType===1)return e;var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(l(188)):(e=Object.keys(e).join(","),Error(l(268,e)));return e=ac(t),e=e===null?null:e.stateNode,e},pt.flushSync=function(e){return nr(e)},pt.hydrate=function(e,t,r){if(!yi(t))throw Error(l(200));return xi(null,e,t,!0,r)},pt.hydrateRoot=function(e,t,r){if(!Ba(e))throw Error(l(405));var i=r!=null&&r.hydratedSources||null,u=!1,d="",y=If;if(r!=null&&(r.unstable_strictMode===!0&&(u=!0),r.identifierPrefix!==void 0&&(d=r.identifierPrefix),r.onRecoverableError!==void 0&&(y=r.onRecoverableError)),t=Lf(t,null,e,1,r??null,u,!1,d,y),e[on]=t.current,To(e),i)for(e=0;e<i.length;e++)r=i[e],u=r._getVersion,u=u(r._source),t.mutableSourceEagerHydrationData==null?t.mutableSourceEagerHydrationData=[r,u]:t.mutableSourceEagerHydrationData.push(r,u);return new vi(t)},pt.render=function(e,t,r){if(!yi(t))throw Error(l(200));return xi(null,e,t,!1,r)},pt.unmountComponentAtNode=function(e){if(!yi(e))throw Error(l(40));return e._reactRootContainer?(nr(function(){xi(null,null,e,!1,function(){e._reactRootContainer=null,e[on]=null})}),!0):!1},pt.unstable_batchedUpdates=Ma,pt.unstable_renderSubtreeIntoContainer=function(e,t,r,i){if(!yi(r))throw Error(l(200));if(e==null||e._reactInternals===void 0)throw Error(l(38));return xi(e,t,r,!1,i)},pt.version="18.3.1-next-f1338f8080-20240426",pt}var Uf;function Gp(){if(Uf)return Qa.exports;Uf=1;function n(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(n)}catch(o){console.error(o)}}return n(),Qa.exports=my(),Qa.exports}var Kf;function hy(){if(Kf)return Si;Kf=1;var n=Gp();return Si.createRoot=n.createRoot,Si.hydrateRoot=n.hydrateRoot,Si}var gy=hy(),sl=Gp();const vy=Qp(sl);function Qf(n,o){if(typeof n=="function")return n(o);n!=null&&(n.current=o)}function Yp(...n){return o=>{let l=!1;const s=n.map(a=>{const c=Qf(a,o);return!l&&typeof c=="function"&&(l=!0),c});if(l)return()=>{for(let a=0;a<s.length;a++){const c=s[a];typeof c=="function"?c():Qf(n[a],null)}}}}function oo(...n){return h.useCallback(Yp(...n),n)}function yy(n){const o=xy(n),l=h.forwardRef((s,a)=>{const{children:c,...p}=s,f=h.Children.toArray(c),m=f.find(Sy);if(m){const g=m.props.children,w=f.map(x=>x===m?h.Children.count(g)>1?h.Children.only(null):h.isValidElement(g)?g.props.children:null:x);return v.jsx(o,{...p,ref:a,children:h.isValidElement(g)?h.cloneElement(g,void 0,w):null})}return v.jsx(o,{...p,ref:a,children:c})});return l.displayName=`${n}.Slot`,l}function xy(n){const o=h.forwardRef((l,s)=>{const{children:a,...c}=l;if(h.isValidElement(a)){const p=Ey(a),f=Cy(c,a.props);return a.type!==h.Fragment&&(f.ref=s?Yp(s,p):p),h.cloneElement(a,f)}return h.Children.count(a)>1?h.Children.only(null):null});return o.displayName=`${n}.SlotClone`,o}var wy=Symbol("radix.slottable");function Sy(n){return h.isValidElement(n)&&typeof n.type=="function"&&"__radixId"in n.type&&n.type.__radixId===wy}function Cy(n,o){const l={...o};for(const s in o){const a=n[s],c=o[s];/^on[A-Z]/.test(s)?a&&c?l[s]=(...f)=>{const m=c(...f);return a(...f),m}:a&&(l[s]=a):s==="style"?l[s]={...a,...c}:s==="className"&&(l[s]=[a,c].filter(Boolean).join(" "))}return{...n,...l}}function Ey(n){var s,a;let o=(s=Object.getOwnPropertyDescriptor(n.props,"ref"))==null?void 0:s.get,l=o&&"isReactWarning"in o&&o.isReactWarning;return l?n.ref:(o=(a=Object.getOwnPropertyDescriptor(n,"ref"))==null?void 0:a.get,l=o&&"isReactWarning"in o&&o.isReactWarning,l?n.props.ref:n.props.ref||n.ref)}var by=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"],al=by.reduce((n,o)=>{const l=yy(`Primitive.${o}`),s=h.forwardRef((a,c)=>{const{asChild:p,...f}=a,m=p?l:o;return typeof window<"u"&&(window[Symbol.for("radix-ui")]=!0),v.jsx(m,{...f,ref:c})});return s.displayName=`Primitive.${o}`,{...n,[o]:s}},{});function Gf(n,o){if(typeof n=="function")return n(o);n!=null&&(n.current=o)}function ky(...n){return o=>{let l=!1;const s=n.map(a=>{const c=Gf(a,o);return!l&&typeof c=="function"&&(l=!0),c});if(l)return()=>{for(let a=0;a<s.length;a++){const c=s[a];typeof c=="function"?c():Gf(n[a],null)}}}}function Py(...n){return h.useCallback(ky(...n),n)}var nl=globalThis!=null&&globalThis.document?h.useLayoutEffect:()=>{};function Ny(n,o){return h.useReducer((l,s)=>o[l][s]??l,n)}var ul=n=>{const{present:o,children:l}=n,s=Ry(o),a=typeof l=="function"?l({present:s.isPresent}):h.Children.only(l),c=Py(s.ref,_y(a));return typeof l=="function"||s.isPresent?h.cloneElement(a,{ref:c}):null};ul.displayName="Presence";function Ry(n){const[o,l]=h.useState(),s=h.useRef(null),a=h.useRef(n),c=h.useRef("none"),p=n?"mounted":"unmounted",[f,m]=Ny(p,{mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}});return h.useEffect(()=>{const g=Ci(s.current);c.current=f==="mounted"?g:"none"},[f]),nl(()=>{const g=s.current,w=a.current;if(w!==n){const S=c.current,E=Ci(g);n?m("MOUNT"):E==="none"||(g==null?void 0:g.display)==="none"?m("UNMOUNT"):m(w&&S!==E?"ANIMATION_OUT":"UNMOUNT"),a.current=n}},[n,m]),nl(()=>{if(o){let g;const w=o.ownerDocument.defaultView??window,x=E=>{const C=Ci(s.current).includes(E.animationName);if(E.target===o&&C&&(m("ANIMATION_END"),!a.current)){const P=o.style.animationFillMode;o.style.animationFillMode="forwards",g=w.setTimeout(()=>{o.style.animationFillMode==="forwards"&&(o.style.animationFillMode=P)})}},S=E=>{E.target===o&&(c.current=Ci(s.current))};return o.addEventListener("animationstart",S),o.addEventListener("animationcancel",x),o.addEventListener("animationend",x),()=>{w.clearTimeout(g),o.removeEventListener("animationstart",S),o.removeEventListener("animationcancel",x),o.removeEventListener("animationend",x)}}else m("ANIMATION_END")},[o,m]),{isPresent:["mounted","unmountSuspended"].includes(f),ref:h.useCallback(g=>{s.current=g?getComputedStyle(g):null,l(g)},[])}}function Ci(n){return(n==null?void 0:n.animationName)||"none"}function _y(n){var s,a;let o=(s=Object.getOwnPropertyDescriptor(n.props,"ref"))==null?void 0:s.get,l=o&&"isReactWarning"in o&&o.isReactWarning;return l?n.ref:(o=(a=Object.getOwnPropertyDescriptor(n,"ref"))==null?void 0:a.get,l=o&&"isReactWarning"in o&&o.isReactWarning,l?n.props.ref:n.props.ref||n.ref)}function Ty(n,o=[]){let l=[];function s(c,p){const f=h.createContext(p),m=l.length;l=[...l,p];const g=x=>{var N;const{scope:S,children:E,...b}=x,C=((N=S==null?void 0:S[n])==null?void 0:N[m])||f,P=h.useMemo(()=>b,Object.values(b));return v.jsx(C.Provider,{value:P,children:E})};g.displayName=c+"Provider";function w(x,S){var C;const E=((C=S==null?void 0:S[n])==null?void 0:C[m])||f,b=h.useContext(E);if(b)return b;if(p!==void 0)return p;throw new Error(`\`${x}\` must be used within \`${c}\``)}return[g,w]}const a=()=>{const c=l.map(p=>h.createContext(p));return function(f){const m=(f==null?void 0:f[n])||c;return h.useMemo(()=>({[`__scope${n}`]:{...f,[n]:m}}),[f,m])}};return a.scopeName=n,[s,jy(a,...o)]}function jy(...n){const o=n[0];if(n.length===1)return o;const l=()=>{const s=n.map(a=>({useScope:a(),scopeName:a.scopeName}));return function(c){const p=s.reduce((f,{useScope:m,scopeName:g})=>{const x=m(c)[`__scope${g}`];return{...f,...x}},{});return h.useMemo(()=>({[`__scope${o.scopeName}`]:p}),[p])}};return l.scopeName=o.scopeName,l}function sr(n){const o=h.useRef(n);return h.useEffect(()=>{o.current=n}),h.useMemo(()=>(...l)=>{var s;return(s=o.current)==null?void 0:s.call(o,...l)},[])}var zy=h.createContext(void 0);function Ay(n){const o=h.useContext(zy);return n||o||"ltr"}function Ly(n,[o,l]){return Math.min(l,Math.max(o,n))}function ur(n,o,{checkForDefaultPrevented:l=!0}={}){return function(a){if(n==null||n(a),l===!1||!a.defaultPrevented)return o==null?void 0:o(a)}}function My(n,o){return h.useReducer((l,s)=>o[l][s]??l,n)}var bu="ScrollArea",[Xp,nE]=Ty(bu),[Iy,Lt]=Xp(bu),Zp=h.forwardRef((n,o)=>{const{__scopeScrollArea:l,type:s="hover",dir:a,scrollHideDelay:c=600,...p}=n,[f,m]=h.useState(null),[g,w]=h.useState(null),[x,S]=h.useState(null),[E,b]=h.useState(null),[C,P]=h.useState(null),[N,j]=h.useState(0),[z,M]=h.useState(0),[D,V]=h.useState(!1),[O,ee]=h.useState(!1),re=oo(o,pe=>m(pe)),ae=Ay(a);return v.jsx(Iy,{scope:l,type:s,dir:ae,scrollHideDelay:c,scrollArea:f,viewport:g,onViewportChange:w,content:x,onContentChange:S,scrollbarX:E,onScrollbarXChange:b,scrollbarXEnabled:D,onScrollbarXEnabledChange:V,scrollbarY:C,onScrollbarYChange:P,scrollbarYEnabled:O,onScrollbarYEnabledChange:ee,onCornerWidthChange:j,onCornerHeightChange:M,children:v.jsx(al.div,{dir:ae,...p,ref:re,style:{position:"relative","--radix-scroll-area-corner-width":N+"px","--radix-scroll-area-corner-height":z+"px",...n.style}})})});Zp.displayName=bu;var qp="ScrollAreaViewport",Jp=h.forwardRef((n,o)=>{const{__scopeScrollArea:l,children:s,nonce:a,...c}=n,p=Lt(qp,l),f=h.useRef(null),m=oo(o,f,p.onViewportChange);return v.jsxs(v.Fragment,{children:[v.jsx("style",{dangerouslySetInnerHTML:{__html:"[data-radix-scroll-area-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-scroll-area-viewport]::-webkit-scrollbar{display:none}"},nonce:a}),v.jsx(al.div,{"data-radix-scroll-area-viewport":"",...c,ref:m,style:{overflowX:p.scrollbarXEnabled?"scroll":"hidden",overflowY:p.scrollbarYEnabled?"scroll":"hidden",...n.style},children:v.jsx("div",{ref:p.onContentChange,style:{minWidth:"100%",display:"table"},children:s})})]})});Jp.displayName=qp;var nn="ScrollAreaScrollbar",em=h.forwardRef((n,o)=>{const{forceMount:l,...s}=n,a=Lt(nn,n.__scopeScrollArea),{onScrollbarXEnabledChange:c,onScrollbarYEnabledChange:p}=a,f=n.orientation==="horizontal";return h.useEffect(()=>(f?c(!0):p(!0),()=>{f?c(!1):p(!1)}),[f,c,p]),a.type==="hover"?v.jsx(Oy,{...s,ref:o,forceMount:l}):a.type==="scroll"?v.jsx(Dy,{...s,ref:o,forceMount:l}):a.type==="auto"?v.jsx(tm,{...s,ref:o,forceMount:l}):a.type==="always"?v.jsx(ku,{...s,ref:o}):null});em.displayName=nn;var Oy=h.forwardRef((n,o)=>{const{forceMount:l,...s}=n,a=Lt(nn,n.__scopeScrollArea),[c,p]=h.useState(!1);return h.useEffect(()=>{const f=a.scrollArea;let m=0;if(f){const g=()=>{window.clearTimeout(m),p(!0)},w=()=>{m=window.setTimeout(()=>p(!1),a.scrollHideDelay)};return f.addEventListener("pointerenter",g),f.addEventListener("pointerleave",w),()=>{window.clearTimeout(m),f.removeEventListener("pointerenter",g),f.removeEventListener("pointerleave",w)}}},[a.scrollArea,a.scrollHideDelay]),v.jsx(ul,{present:l||c,children:v.jsx(tm,{"data-state":c?"visible":"hidden",...s,ref:o})})}),Dy=h.forwardRef((n,o)=>{const{forceMount:l,...s}=n,a=Lt(nn,n.__scopeScrollArea),c=n.orientation==="horizontal",p=Vi(()=>m("SCROLL_END"),100),[f,m]=My("hidden",{hidden:{SCROLL:"scrolling"},scrolling:{SCROLL_END:"idle",POINTER_ENTER:"interacting"},interacting:{SCROLL:"interacting",POINTER_LEAVE:"idle"},idle:{HIDE:"hidden",SCROLL:"scrolling",POINTER_ENTER:"interacting"}});return h.useEffect(()=>{if(f==="idle"){const g=window.setTimeout(()=>m("HIDE"),a.scrollHideDelay);return()=>window.clearTimeout(g)}},[f,a.scrollHideDelay,m]),h.useEffect(()=>{const g=a.viewport,w=c?"scrollLeft":"scrollTop";if(g){let x=g[w];const S=()=>{const E=g[w];x!==E&&(m("SCROLL"),p()),x=E};return g.addEventListener("scroll",S),()=>g.removeEventListener("scroll",S)}},[a.viewport,c,m,p]),v.jsx(ul,{present:l||f!=="hidden",children:v.jsx(ku,{"data-state":f==="hidden"?"hidden":"visible",...s,ref:o,onPointerEnter:ur(n.onPointerEnter,()=>m("POINTER_ENTER")),onPointerLeave:ur(n.onPointerLeave,()=>m("POINTER_LEAVE"))})})}),tm=h.forwardRef((n,o)=>{const l=Lt(nn,n.__scopeScrollArea),{forceMount:s,...a}=n,[c,p]=h.useState(!1),f=n.orientation==="horizontal",m=Vi(()=>{if(l.viewport){const g=l.viewport.offsetWidth<l.viewport.scrollWidth,w=l.viewport.offsetHeight<l.viewport.scrollHeight;p(f?g:w)}},10);return to(l.viewport,m),to(l.content,m),v.jsx(ul,{present:s||c,children:v.jsx(ku,{"data-state":c?"visible":"hidden",...a,ref:o})})}),ku=h.forwardRef((n,o)=>{const{orientation:l="vertical",...s}=n,a=Lt(nn,n.__scopeScrollArea),c=h.useRef(null),p=h.useRef(0),[f,m]=h.useState({content:0,viewport:0,scrollbar:{size:0,paddingStart:0,paddingEnd:0}}),g=im(f.viewport,f.content),w={...s,sizes:f,onSizesChange:m,hasThumb:g>0&&g<1,onThumbChange:S=>c.current=S,onThumbPointerUp:()=>p.current=0,onThumbPointerDown:S=>p.current=S};function x(S,E){return By(S,p.current,f,E)}return l==="horizontal"?v.jsx($y,{...w,ref:o,onThumbPositionChange:()=>{if(a.viewport&&c.current){const S=a.viewport.scrollLeft,E=Yf(S,f,a.dir);c.current.style.transform=`translate3d(${E}px, 0, 0)`}},onWheelScroll:S=>{a.viewport&&(a.viewport.scrollLeft=S)},onDragScroll:S=>{a.viewport&&(a.viewport.scrollLeft=x(S,a.dir))}}):l==="vertical"?v.jsx(Fy,{...w,ref:o,onThumbPositionChange:()=>{if(a.viewport&&c.current){const S=a.viewport.scrollTop,E=Yf(S,f);c.current.style.transform=`translate3d(0, ${E}px, 0)`}},onWheelScroll:S=>{a.viewport&&(a.viewport.scrollTop=S)},onDragScroll:S=>{a.viewport&&(a.viewport.scrollTop=x(S))}}):null}),$y=h.forwardRef((n,o)=>{const{sizes:l,onSizesChange:s,...a}=n,c=Lt(nn,n.__scopeScrollArea),[p,f]=h.useState(),m=h.useRef(null),g=oo(o,m,c.onScrollbarXChange);return h.useEffect(()=>{m.current&&f(getComputedStyle(m.current))},[m]),v.jsx(rm,{"data-orientation":"horizontal",...a,ref:g,sizes:l,style:{bottom:0,left:c.dir==="rtl"?"var(--radix-scroll-area-corner-width)":0,right:c.dir==="ltr"?"var(--radix-scroll-area-corner-width)":0,"--radix-scroll-area-thumb-width":Hi(l)+"px",...n.style},onThumbPointerDown:w=>n.onThumbPointerDown(w.x),onDragScroll:w=>n.onDragScroll(w.x),onWheelScroll:(w,x)=>{if(c.viewport){const S=c.viewport.scrollLeft+w.deltaX;n.onWheelScroll(S),am(S,x)&&w.preventDefault()}},onResize:()=>{m.current&&c.viewport&&p&&s({content:c.viewport.scrollWidth,viewport:c.viewport.offsetWidth,scrollbar:{size:m.current.clientWidth,paddingStart:Ai(p.paddingLeft),paddingEnd:Ai(p.paddingRight)}})}})}),Fy=h.forwardRef((n,o)=>{const{sizes:l,onSizesChange:s,...a}=n,c=Lt(nn,n.__scopeScrollArea),[p,f]=h.useState(),m=h.useRef(null),g=oo(o,m,c.onScrollbarYChange);return h.useEffect(()=>{m.current&&f(getComputedStyle(m.current))},[m]),v.jsx(rm,{"data-orientation":"vertical",...a,ref:g,sizes:l,style:{top:0,right:c.dir==="ltr"?0:void 0,left:c.dir==="rtl"?0:void 0,bottom:"var(--radix-scroll-area-corner-height)","--radix-scroll-area-thumb-height":Hi(l)+"px",...n.style},onThumbPointerDown:w=>n.onThumbPointerDown(w.y),onDragScroll:w=>n.onDragScroll(w.y),onWheelScroll:(w,x)=>{if(c.viewport){const S=c.viewport.scrollTop+w.deltaY;n.onWheelScroll(S),am(S,x)&&w.preventDefault()}},onResize:()=>{m.current&&c.viewport&&p&&s({content:c.viewport.scrollHeight,viewport:c.viewport.offsetHeight,scrollbar:{size:m.current.clientHeight,paddingStart:Ai(p.paddingTop),paddingEnd:Ai(p.paddingBottom)}})}})}),[Wy,nm]=Xp(nn),rm=h.forwardRef((n,o)=>{const{__scopeScrollArea:l,sizes:s,hasThumb:a,onThumbChange:c,onThumbPointerUp:p,onThumbPointerDown:f,onThumbPositionChange:m,onDragScroll:g,onWheelScroll:w,onResize:x,...S}=n,E=Lt(nn,l),[b,C]=h.useState(null),P=oo(o,re=>C(re)),N=h.useRef(null),j=h.useRef(""),z=E.viewport,M=s.content-s.viewport,D=sr(w),V=sr(m),O=Vi(x,10);function ee(re){if(N.current){const ae=re.clientX-N.current.left,pe=re.clientY-N.current.top;g({x:ae,y:pe})}}return h.useEffect(()=>{const re=ae=>{const pe=ae.target;(b==null?void 0:b.contains(pe))&&D(ae,M)};return document.addEventListener("wheel",re,{passive:!1}),()=>document.removeEventListener("wheel",re,{passive:!1})},[z,b,M,D]),h.useEffect(V,[s,V]),to(b,O),to(E.content,O),v.jsx(Wy,{scope:l,scrollbar:b,hasThumb:a,onThumbChange:sr(c),onThumbPointerUp:sr(p),onThumbPositionChange:V,onThumbPointerDown:sr(f),children:v.jsx(al.div,{...S,ref:P,style:{position:"absolute",...S.style},onPointerDown:ur(n.onPointerDown,re=>{re.button===0&&(re.target.setPointerCapture(re.pointerId),N.current=b.getBoundingClientRect(),j.current=document.body.style.webkitUserSelect,document.body.style.webkitUserSelect="none",E.viewport&&(E.viewport.style.scrollBehavior="auto"),ee(re))}),onPointerMove:ur(n.onPointerMove,ee),onPointerUp:ur(n.onPointerUp,re=>{const ae=re.target;ae.hasPointerCapture(re.pointerId)&&ae.releasePointerCapture(re.pointerId),document.body.style.webkitUserSelect=j.current,E.viewport&&(E.viewport.style.scrollBehavior=""),N.current=null})})})}),zi="ScrollAreaThumb",om=h.forwardRef((n,o)=>{const{forceMount:l,...s}=n,a=nm(zi,n.__scopeScrollArea);return v.jsx(ul,{present:l||a.hasThumb,children:v.jsx(Hy,{ref:o,...s})})}),Hy=h.forwardRef((n,o)=>{const{__scopeScrollArea:l,style:s,...a}=n,c=Lt(zi,l),p=nm(zi,l),{onThumbPositionChange:f}=p,m=oo(o,x=>p.onThumbChange(x)),g=h.useRef(void 0),w=Vi(()=>{g.current&&(g.current(),g.current=void 0)},100);return h.useEffect(()=>{const x=c.viewport;if(x){const S=()=>{if(w(),!g.current){const E=Uy(x,f);g.current=E,f()}};return f(),x.addEventListener("scroll",S),()=>x.removeEventListener("scroll",S)}},[c.viewport,w,f]),v.jsx(al.div,{"data-state":p.hasThumb?"visible":"hidden",...a,ref:m,style:{width:"var(--radix-scroll-area-thumb-width)",height:"var(--radix-scroll-area-thumb-height)",...s},onPointerDownCapture:ur(n.onPointerDownCapture,x=>{const E=x.target.getBoundingClientRect(),b=x.clientX-E.left,C=x.clientY-E.top;p.onThumbPointerDown({x:b,y:C})}),onPointerUp:ur(n.onPointerUp,p.onThumbPointerUp)})});om.displayName=zi;var Pu="ScrollAreaCorner",lm=h.forwardRef((n,o)=>{const l=Lt(Pu,n.__scopeScrollArea),s=!!(l.scrollbarX&&l.scrollbarY);return l.type!=="scroll"&&s?v.jsx(Vy,{...n,ref:o}):null});lm.displayName=Pu;var Vy=h.forwardRef((n,o)=>{const{__scopeScrollArea:l,...s}=n,a=Lt(Pu,l),[c,p]=h.useState(0),[f,m]=h.useState(0),g=!!(c&&f);return to(a.scrollbarX,()=>{var x;const w=((x=a.scrollbarX)==null?void 0:x.offsetHeight)||0;a.onCornerHeightChange(w),m(w)}),to(a.scrollbarY,()=>{var x;const w=((x=a.scrollbarY)==null?void 0:x.offsetWidth)||0;a.onCornerWidthChange(w),p(w)}),g?v.jsx(al.div,{...s,ref:o,style:{width:c,height:f,position:"absolute",right:a.dir==="ltr"?0:void 0,left:a.dir==="rtl"?0:void 0,bottom:0,...n.style}}):null});function Ai(n){return n?parseInt(n,10):0}function im(n,o){const l=n/o;return isNaN(l)?0:l}function Hi(n){const o=im(n.viewport,n.content),l=n.scrollbar.paddingStart+n.scrollbar.paddingEnd,s=(n.scrollbar.size-l)*o;return Math.max(s,18)}function By(n,o,l,s="ltr"){const a=Hi(l),c=a/2,p=o||c,f=a-p,m=l.scrollbar.paddingStart+p,g=l.scrollbar.size-l.scrollbar.paddingEnd-f,w=l.content-l.viewport,x=s==="ltr"?[0,w]:[w*-1,0];return sm([m,g],x)(n)}function Yf(n,o,l="ltr"){const s=Hi(o),a=o.scrollbar.paddingStart+o.scrollbar.paddingEnd,c=o.scrollbar.size-a,p=o.content-o.viewport,f=c-s,m=l==="ltr"?[0,p]:[p*-1,0],g=Ly(n,m);return sm([0,p],[0,f])(g)}function sm(n,o){return l=>{if(n[0]===n[1]||o[0]===o[1])return o[0];const s=(o[1]-o[0])/(n[1]-n[0]);return o[0]+s*(l-n[0])}}function am(n,o){return n>0&&n<o}var Uy=(n,o=()=>{})=>{let l={left:n.scrollLeft,top:n.scrollTop},s=0;return function a(){const c={left:n.scrollLeft,top:n.scrollTop},p=l.left!==c.left,f=l.top!==c.top;(p||f)&&o(),l=c,s=window.requestAnimationFrame(a)}(),()=>window.cancelAnimationFrame(s)};function Vi(n,o){const l=sr(n),s=h.useRef(0);return h.useEffect(()=>()=>window.clearTimeout(s.current),[]),h.useCallback(()=>{window.clearTimeout(s.current),s.current=window.setTimeout(l,o)},[l,o])}function to(n,o){const l=sr(o);nl(()=>{let s=0;if(n){const a=new ResizeObserver(()=>{cancelAnimationFrame(s),s=window.requestAnimationFrame(l)});return a.observe(n),()=>{window.cancelAnimationFrame(s),a.unobserve(n)}}},[n,l])}var Ky=Zp,Qy=Jp,Gy=lm;function um(n){var o,l,s="";if(typeof n=="string"||typeof n=="number")s+=n;else if(typeof n=="object")if(Array.isArray(n)){var a=n.length;for(o=0;o<a;o++)n[o]&&(l=um(n[o]))&&(s&&(s+=" "),s+=l)}else for(l in n)n[l]&&(s&&(s+=" "),s+=l);return s}function cm(){for(var n,o,l=0,s="",a=arguments.length;l<a;l++)(n=arguments[l])&&(o=um(n))&&(s&&(s+=" "),s+=o);return s}const Nu="-",Yy=n=>{const o=Zy(n),{conflictingClassGroups:l,conflictingClassGroupModifiers:s}=n;return{getClassGroupId:p=>{const f=p.split(Nu);return f[0]===""&&f.length!==1&&f.shift(),dm(f,o)||Xy(p)},getConflictingClassGroupIds:(p,f)=>{const m=l[p]||[];return f&&s[p]?[...m,...s[p]]:m}}},dm=(n,o)=>{var p;if(n.length===0)return o.classGroupId;const l=n[0],s=o.nextPart.get(l),a=s?dm(n.slice(1),s):void 0;if(a)return a;if(o.validators.length===0)return;const c=n.join(Nu);return(p=o.validators.find(({validator:f})=>f(c)))==null?void 0:p.classGroupId},Xf=/^\[(.+)\]$/,Xy=n=>{if(Xf.test(n)){const o=Xf.exec(n)[1],l=o==null?void 0:o.substring(0,o.indexOf(":"));if(l)return"arbitrary.."+l}},Zy=n=>{const{theme:o,prefix:l}=n,s={nextPart:new Map,validators:[]};return Jy(Object.entries(n.classGroups),l).forEach(([c,p])=>{au(p,s,c,o)}),s},au=(n,o,l,s)=>{n.forEach(a=>{if(typeof a=="string"){const c=a===""?o:Zf(o,a);c.classGroupId=l;return}if(typeof a=="function"){if(qy(a)){au(a(s),o,l,s);return}o.validators.push({validator:a,classGroupId:l});return}Object.entries(a).forEach(([c,p])=>{au(p,Zf(o,c),l,s)})})},Zf=(n,o)=>{let l=n;return o.split(Nu).forEach(s=>{l.nextPart.has(s)||l.nextPart.set(s,{nextPart:new Map,validators:[]}),l=l.nextPart.get(s)}),l},qy=n=>n.isThemeGetter,Jy=(n,o)=>o?n.map(([l,s])=>{const a=s.map(c=>typeof c=="string"?o+c:typeof c=="object"?Object.fromEntries(Object.entries(c).map(([p,f])=>[o+p,f])):c);return[l,a]}):n,e0=n=>{if(n<1)return{get:()=>{},set:()=>{}};let o=0,l=new Map,s=new Map;const a=(c,p)=>{l.set(c,p),o++,o>n&&(o=0,s=l,l=new Map)};return{get(c){let p=l.get(c);if(p!==void 0)return p;if((p=s.get(c))!==void 0)return a(c,p),p},set(c,p){l.has(c)?l.set(c,p):a(c,p)}}},fm="!",t0=n=>{const{separator:o,experimentalParseClassName:l}=n,s=o.length===1,a=o[0],c=o.length,p=f=>{const m=[];let g=0,w=0,x;for(let P=0;P<f.length;P++){let N=f[P];if(g===0){if(N===a&&(s||f.slice(P,P+c)===o)){m.push(f.slice(w,P)),w=P+c;continue}if(N==="/"){x=P;continue}}N==="["?g++:N==="]"&&g--}const S=m.length===0?f:f.substring(w),E=S.startsWith(fm),b=E?S.substring(1):S,C=x&&x>w?x-w:void 0;return{modifiers:m,hasImportantModifier:E,baseClassName:b,maybePostfixModifierPosition:C}};return l?f=>l({className:f,parseClassName:p}):p},n0=n=>{if(n.length<=1)return n;const o=[];let l=[];return n.forEach(s=>{s[0]==="["?(o.push(...l.sort(),s),l=[]):l.push(s)}),o.push(...l.sort()),o},r0=n=>({cache:e0(n.cacheSize),parseClassName:t0(n),...Yy(n)}),o0=/\s+/,l0=(n,o)=>{const{parseClassName:l,getClassGroupId:s,getConflictingClassGroupIds:a}=o,c=[],p=n.trim().split(o0);let f="";for(let m=p.length-1;m>=0;m-=1){const g=p[m],{modifiers:w,hasImportantModifier:x,baseClassName:S,maybePostfixModifierPosition:E}=l(g);let b=!!E,C=s(b?S.substring(0,E):S);if(!C){if(!b){f=g+(f.length>0?" "+f:f);continue}if(C=s(S),!C){f=g+(f.length>0?" "+f:f);continue}b=!1}const P=n0(w).join(":"),N=x?P+fm:P,j=N+C;if(c.includes(j))continue;c.push(j);const z=a(C,b);for(let M=0;M<z.length;++M){const D=z[M];c.push(N+D)}f=g+(f.length>0?" "+f:f)}return f};function i0(){let n=0,o,l,s="";for(;n<arguments.length;)(o=arguments[n++])&&(l=pm(o))&&(s&&(s+=" "),s+=l);return s}const pm=n=>{if(typeof n=="string")return n;let o,l="";for(let s=0;s<n.length;s++)n[s]&&(o=pm(n[s]))&&(l&&(l+=" "),l+=o);return l};function s0(n,...o){let l,s,a,c=p;function p(m){const g=o.reduce((w,x)=>x(w),n());return l=r0(g),s=l.cache.get,a=l.cache.set,c=f,f(m)}function f(m){const g=s(m);if(g)return g;const w=l0(m,l);return a(m,w),w}return function(){return c(i0.apply(null,arguments))}}const Ae=n=>{const o=l=>l[n]||[];return o.isThemeGetter=!0,o},mm=/^\[(?:([a-z-]+):)?(.+)\]$/i,a0=/^\d+\/\d+$/,u0=new Set(["px","full","screen"]),c0=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,d0=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,f0=/^(rgba?|hsla?|hwb|(ok)?(lab|lch))\(.+\)$/,p0=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,m0=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,pn=n=>Yr(n)||u0.has(n)||a0.test(n),In=n=>lo(n,"length",C0),Yr=n=>!!n&&!Number.isNaN(Number(n)),Xa=n=>lo(n,"number",Yr),Yo=n=>!!n&&Number.isInteger(Number(n)),h0=n=>n.endsWith("%")&&Yr(n.slice(0,-1)),Ee=n=>mm.test(n),On=n=>c0.test(n),g0=new Set(["length","size","percentage"]),v0=n=>lo(n,g0,hm),y0=n=>lo(n,"position",hm),x0=new Set(["image","url"]),w0=n=>lo(n,x0,b0),S0=n=>lo(n,"",E0),Xo=()=>!0,lo=(n,o,l)=>{const s=mm.exec(n);return s?s[1]?typeof o=="string"?s[1]===o:o.has(s[1]):l(s[2]):!1},C0=n=>d0.test(n)&&!f0.test(n),hm=()=>!1,E0=n=>p0.test(n),b0=n=>m0.test(n),k0=()=>{const n=Ae("colors"),o=Ae("spacing"),l=Ae("blur"),s=Ae("brightness"),a=Ae("borderColor"),c=Ae("borderRadius"),p=Ae("borderSpacing"),f=Ae("borderWidth"),m=Ae("contrast"),g=Ae("grayscale"),w=Ae("hueRotate"),x=Ae("invert"),S=Ae("gap"),E=Ae("gradientColorStops"),b=Ae("gradientColorStopPositions"),C=Ae("inset"),P=Ae("margin"),N=Ae("opacity"),j=Ae("padding"),z=Ae("saturate"),M=Ae("scale"),D=Ae("sepia"),V=Ae("skew"),O=Ae("space"),ee=Ae("translate"),re=()=>["auto","contain","none"],ae=()=>["auto","hidden","clip","visible","scroll"],pe=()=>["auto",Ee,o],q=()=>[Ee,o],ve=()=>["",pn,In],se=()=>["auto",Yr,Ee],ge=()=>["bottom","center","left","left-bottom","left-top","right","right-bottom","right-top","top"],le=()=>["solid","dashed","dotted","double","none"],ce=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],$=()=>["start","end","center","between","around","evenly","stretch"],J=()=>["","0",Ee],Y=()=>["auto","avoid","all","avoid-page","page","left","right","column"],_=()=>[Yr,Ee];return{cacheSize:500,separator:":",theme:{colors:[Xo],spacing:[pn,In],blur:["none","",On,Ee],brightness:_(),borderColor:[n],borderRadius:["none","","full",On,Ee],borderSpacing:q(),borderWidth:ve(),contrast:_(),grayscale:J(),hueRotate:_(),invert:J(),gap:q(),gradientColorStops:[n],gradientColorStopPositions:[h0,In],inset:pe(),margin:pe(),opacity:_(),padding:q(),saturate:_(),scale:_(),sepia:J(),skew:_(),space:q(),translate:q()},classGroups:{aspect:[{aspect:["auto","square","video",Ee]}],container:["container"],columns:[{columns:[On]}],"break-after":[{"break-after":Y()}],"break-before":[{"break-before":Y()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:[...ge(),Ee]}],overflow:[{overflow:ae()}],"overflow-x":[{"overflow-x":ae()}],"overflow-y":[{"overflow-y":ae()}],overscroll:[{overscroll:re()}],"overscroll-x":[{"overscroll-x":re()}],"overscroll-y":[{"overscroll-y":re()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:[C]}],"inset-x":[{"inset-x":[C]}],"inset-y":[{"inset-y":[C]}],start:[{start:[C]}],end:[{end:[C]}],top:[{top:[C]}],right:[{right:[C]}],bottom:[{bottom:[C]}],left:[{left:[C]}],visibility:["visible","invisible","collapse"],z:[{z:["auto",Yo,Ee]}],basis:[{basis:pe()}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["wrap","wrap-reverse","nowrap"]}],flex:[{flex:["1","auto","initial","none",Ee]}],grow:[{grow:J()}],shrink:[{shrink:J()}],order:[{order:["first","last","none",Yo,Ee]}],"grid-cols":[{"grid-cols":[Xo]}],"col-start-end":[{col:["auto",{span:["full",Yo,Ee]},Ee]}],"col-start":[{"col-start":se()}],"col-end":[{"col-end":se()}],"grid-rows":[{"grid-rows":[Xo]}],"row-start-end":[{row:["auto",{span:[Yo,Ee]},Ee]}],"row-start":[{"row-start":se()}],"row-end":[{"row-end":se()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":["auto","min","max","fr",Ee]}],"auto-rows":[{"auto-rows":["auto","min","max","fr",Ee]}],gap:[{gap:[S]}],"gap-x":[{"gap-x":[S]}],"gap-y":[{"gap-y":[S]}],"justify-content":[{justify:["normal",...$()]}],"justify-items":[{"justify-items":["start","end","center","stretch"]}],"justify-self":[{"justify-self":["auto","start","end","center","stretch"]}],"align-content":[{content:["normal",...$(),"baseline"]}],"align-items":[{items:["start","end","center","baseline","stretch"]}],"align-self":[{self:["auto","start","end","center","stretch","baseline"]}],"place-content":[{"place-content":[...$(),"baseline"]}],"place-items":[{"place-items":["start","end","center","baseline","stretch"]}],"place-self":[{"place-self":["auto","start","end","center","stretch"]}],p:[{p:[j]}],px:[{px:[j]}],py:[{py:[j]}],ps:[{ps:[j]}],pe:[{pe:[j]}],pt:[{pt:[j]}],pr:[{pr:[j]}],pb:[{pb:[j]}],pl:[{pl:[j]}],m:[{m:[P]}],mx:[{mx:[P]}],my:[{my:[P]}],ms:[{ms:[P]}],me:[{me:[P]}],mt:[{mt:[P]}],mr:[{mr:[P]}],mb:[{mb:[P]}],ml:[{ml:[P]}],"space-x":[{"space-x":[O]}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":[O]}],"space-y-reverse":["space-y-reverse"],w:[{w:["auto","min","max","fit","svw","lvw","dvw",Ee,o]}],"min-w":[{"min-w":[Ee,o,"min","max","fit"]}],"max-w":[{"max-w":[Ee,o,"none","full","min","max","fit","prose",{screen:[On]},On]}],h:[{h:[Ee,o,"auto","min","max","fit","svh","lvh","dvh"]}],"min-h":[{"min-h":[Ee,o,"min","max","fit","svh","lvh","dvh"]}],"max-h":[{"max-h":[Ee,o,"min","max","fit","svh","lvh","dvh"]}],size:[{size:[Ee,o,"auto","min","max","fit"]}],"font-size":[{text:["base",On,In]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:["thin","extralight","light","normal","medium","semibold","bold","extrabold","black",Xa]}],"font-family":[{font:[Xo]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:["tighter","tight","normal","wide","wider","widest",Ee]}],"line-clamp":[{"line-clamp":["none",Yr,Xa]}],leading:[{leading:["none","tight","snug","normal","relaxed","loose",pn,Ee]}],"list-image":[{"list-image":["none",Ee]}],"list-style-type":[{list:["none","disc","decimal",Ee]}],"list-style-position":[{list:["inside","outside"]}],"placeholder-color":[{placeholder:[n]}],"placeholder-opacity":[{"placeholder-opacity":[N]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"text-color":[{text:[n]}],"text-opacity":[{"text-opacity":[N]}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...le(),"wavy"]}],"text-decoration-thickness":[{decoration:["auto","from-font",pn,In]}],"underline-offset":[{"underline-offset":["auto",pn,Ee]}],"text-decoration-color":[{decoration:[n]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:q()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",Ee]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",Ee]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-opacity":[{"bg-opacity":[N]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:[...ge(),y0]}],"bg-repeat":[{bg:["no-repeat",{repeat:["","x","y","round","space"]}]}],"bg-size":[{bg:["auto","cover","contain",v0]}],"bg-image":[{bg:["none",{"gradient-to":["t","tr","r","br","b","bl","l","tl"]},w0]}],"bg-color":[{bg:[n]}],"gradient-from-pos":[{from:[b]}],"gradient-via-pos":[{via:[b]}],"gradient-to-pos":[{to:[b]}],"gradient-from":[{from:[E]}],"gradient-via":[{via:[E]}],"gradient-to":[{to:[E]}],rounded:[{rounded:[c]}],"rounded-s":[{"rounded-s":[c]}],"rounded-e":[{"rounded-e":[c]}],"rounded-t":[{"rounded-t":[c]}],"rounded-r":[{"rounded-r":[c]}],"rounded-b":[{"rounded-b":[c]}],"rounded-l":[{"rounded-l":[c]}],"rounded-ss":[{"rounded-ss":[c]}],"rounded-se":[{"rounded-se":[c]}],"rounded-ee":[{"rounded-ee":[c]}],"rounded-es":[{"rounded-es":[c]}],"rounded-tl":[{"rounded-tl":[c]}],"rounded-tr":[{"rounded-tr":[c]}],"rounded-br":[{"rounded-br":[c]}],"rounded-bl":[{"rounded-bl":[c]}],"border-w":[{border:[f]}],"border-w-x":[{"border-x":[f]}],"border-w-y":[{"border-y":[f]}],"border-w-s":[{"border-s":[f]}],"border-w-e":[{"border-e":[f]}],"border-w-t":[{"border-t":[f]}],"border-w-r":[{"border-r":[f]}],"border-w-b":[{"border-b":[f]}],"border-w-l":[{"border-l":[f]}],"border-opacity":[{"border-opacity":[N]}],"border-style":[{border:[...le(),"hidden"]}],"divide-x":[{"divide-x":[f]}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":[f]}],"divide-y-reverse":["divide-y-reverse"],"divide-opacity":[{"divide-opacity":[N]}],"divide-style":[{divide:le()}],"border-color":[{border:[a]}],"border-color-x":[{"border-x":[a]}],"border-color-y":[{"border-y":[a]}],"border-color-s":[{"border-s":[a]}],"border-color-e":[{"border-e":[a]}],"border-color-t":[{"border-t":[a]}],"border-color-r":[{"border-r":[a]}],"border-color-b":[{"border-b":[a]}],"border-color-l":[{"border-l":[a]}],"divide-color":[{divide:[a]}],"outline-style":[{outline:["",...le()]}],"outline-offset":[{"outline-offset":[pn,Ee]}],"outline-w":[{outline:[pn,In]}],"outline-color":[{outline:[n]}],"ring-w":[{ring:ve()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:[n]}],"ring-opacity":[{"ring-opacity":[N]}],"ring-offset-w":[{"ring-offset":[pn,In]}],"ring-offset-color":[{"ring-offset":[n]}],shadow:[{shadow:["","inner","none",On,S0]}],"shadow-color":[{shadow:[Xo]}],opacity:[{opacity:[N]}],"mix-blend":[{"mix-blend":[...ce(),"plus-lighter","plus-darker"]}],"bg-blend":[{"bg-blend":ce()}],filter:[{filter:["","none"]}],blur:[{blur:[l]}],brightness:[{brightness:[s]}],contrast:[{contrast:[m]}],"drop-shadow":[{"drop-shadow":["","none",On,Ee]}],grayscale:[{grayscale:[g]}],"hue-rotate":[{"hue-rotate":[w]}],invert:[{invert:[x]}],saturate:[{saturate:[z]}],sepia:[{sepia:[D]}],"backdrop-filter":[{"backdrop-filter":["","none"]}],"backdrop-blur":[{"backdrop-blur":[l]}],"backdrop-brightness":[{"backdrop-brightness":[s]}],"backdrop-contrast":[{"backdrop-contrast":[m]}],"backdrop-grayscale":[{"backdrop-grayscale":[g]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[w]}],"backdrop-invert":[{"backdrop-invert":[x]}],"backdrop-opacity":[{"backdrop-opacity":[N]}],"backdrop-saturate":[{"backdrop-saturate":[z]}],"backdrop-sepia":[{"backdrop-sepia":[D]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":[p]}],"border-spacing-x":[{"border-spacing-x":[p]}],"border-spacing-y":[{"border-spacing-y":[p]}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["none","all","","colors","opacity","shadow","transform",Ee]}],duration:[{duration:_()}],ease:[{ease:["linear","in","out","in-out",Ee]}],delay:[{delay:_()}],animate:[{animate:["none","spin","ping","pulse","bounce",Ee]}],transform:[{transform:["","gpu","none"]}],scale:[{scale:[M]}],"scale-x":[{"scale-x":[M]}],"scale-y":[{"scale-y":[M]}],rotate:[{rotate:[Yo,Ee]}],"translate-x":[{"translate-x":[ee]}],"translate-y":[{"translate-y":[ee]}],"skew-x":[{"skew-x":[V]}],"skew-y":[{"skew-y":[V]}],"transform-origin":[{origin:["center","top","top-right","right","bottom-right","bottom","bottom-left","left","top-left",Ee]}],accent:[{accent:["auto",n]}],appearance:[{appearance:["none","auto"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",Ee]}],"caret-color":[{caret:[n]}],"pointer-events":[{"pointer-events":["none","auto"]}],resize:[{resize:["none","y","x",""]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":q()}],"scroll-mx":[{"scroll-mx":q()}],"scroll-my":[{"scroll-my":q()}],"scroll-ms":[{"scroll-ms":q()}],"scroll-me":[{"scroll-me":q()}],"scroll-mt":[{"scroll-mt":q()}],"scroll-mr":[{"scroll-mr":q()}],"scroll-mb":[{"scroll-mb":q()}],"scroll-ml":[{"scroll-ml":q()}],"scroll-p":[{"scroll-p":q()}],"scroll-px":[{"scroll-px":q()}],"scroll-py":[{"scroll-py":q()}],"scroll-ps":[{"scroll-ps":q()}],"scroll-pe":[{"scroll-pe":q()}],"scroll-pt":[{"scroll-pt":q()}],"scroll-pr":[{"scroll-pr":q()}],"scroll-pb":[{"scroll-pb":q()}],"scroll-pl":[{"scroll-pl":q()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",Ee]}],fill:[{fill:[n,"none"]}],"stroke-w":[{stroke:[pn,In,Xa]}],stroke:[{stroke:[n,"none"]}],sr:["sr-only","not-sr-only"],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]}}},P0=s0(k0);function De(...n){return P0(cm(n))}function gm({className:n,children:o,...l}){return v.jsxs(Ky,{"data-slot":"scroll-area",className:De("relative",n),...l,children:[v.jsx(Qy,{"data-slot":"scroll-area-viewport",className:"focus-visible:ring-ring/50 size-full rounded-[inherit] transition-[color,box-shadow] outline-none focus-visible:ring-[3px] focus-visible:outline-1",children:o}),v.jsx(N0,{}),v.jsx(Gy,{})]})}function N0({className:n,orientation:o="vertical",...l}){return v.jsx(em,{"data-slot":"scroll-area-scrollbar",orientation:o,className:De("flex touch-none p-px transition-colors select-none",o==="vertical"&&"h-full w-2.5 border-l border-l-transparent",o==="horizontal"&&"h-2.5 flex-col border-t border-t-transparent",n),...l,children:v.jsx(om,{"data-slot":"scroll-area-thumb",className:"bg-border relative flex-1 rounded-full"})})}function R0(n,o,{checkForDefaultPrevented:l=!0}={}){return function(a){if(n==null||n(a),l===!1||!a.defaultPrevented)return o==null?void 0:o(a)}}function qf(n,o){if(typeof n=="function")return n(o);n!=null&&(n.current=o)}function vm(...n){return o=>{let l=!1;const s=n.map(a=>{const c=qf(a,o);return!l&&typeof c=="function"&&(l=!0),c});if(l)return()=>{for(let a=0;a<s.length;a++){const c=s[a];typeof c=="function"?c():qf(n[a],null)}}}}function ym(...n){return h.useCallback(vm(...n),n)}function _0(n,o=[]){let l=[];function s(c,p){const f=h.createContext(p),m=l.length;l=[...l,p];const g=x=>{var N;const{scope:S,children:E,...b}=x,C=((N=S==null?void 0:S[n])==null?void 0:N[m])||f,P=h.useMemo(()=>b,Object.values(b));return v.jsx(C.Provider,{value:P,children:E})};g.displayName=c+"Provider";function w(x,S){var C;const E=((C=S==null?void 0:S[n])==null?void 0:C[m])||f,b=h.useContext(E);if(b)return b;if(p!==void 0)return p;throw new Error(`\`${x}\` must be used within \`${c}\``)}return[g,w]}const a=()=>{const c=l.map(p=>h.createContext(p));return function(f){const m=(f==null?void 0:f[n])||c;return h.useMemo(()=>({[`__scope${n}`]:{...f,[n]:m}}),[f,m])}};return a.scopeName=n,[s,T0(a,...o)]}function T0(...n){const o=n[0];if(n.length===1)return o;const l=()=>{const s=n.map(a=>({useScope:a(),scopeName:a.scopeName}));return function(c){const p=s.reduce((f,{useScope:m,scopeName:g})=>{const x=m(c)[`__scope${g}`];return{...f,...x}},{});return h.useMemo(()=>({[`__scope${o.scopeName}`]:p}),[p])}};return l.scopeName=o.scopeName,l}var j0=Wi[" useInsertionEffect ".trim().toString()]||nl;function z0({prop:n,defaultProp:o,onChange:l=()=>{},caller:s}){const[a,c,p]=A0({defaultProp:o,onChange:l}),f=n!==void 0,m=f?n:a;{const w=h.useRef(n!==void 0);h.useEffect(()=>{const x=w.current;x!==f&&console.warn(`${s} is changing from ${x?"controlled":"uncontrolled"} to ${f?"controlled":"uncontrolled"}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`),w.current=f},[f,s])}const g=h.useCallback(w=>{var x;if(f){const S=L0(w)?w(n):w;S!==n&&((x=p.current)==null||x.call(p,S))}else c(w)},[f,n,c,p]);return[m,g]}function A0({defaultProp:n,onChange:o}){const[l,s]=h.useState(n),a=h.useRef(l),c=h.useRef(o);return j0(()=>{c.current=o},[o]),h.useEffect(()=>{var p;a.current!==l&&((p=c.current)==null||p.call(c,l),a.current=l)},[l,a]),[l,s,c]}function L0(n){return typeof n=="function"}function M0(n){const o=h.useRef({value:n,previous:n});return h.useMemo(()=>(o.current.value!==n&&(o.current.previous=o.current.value,o.current.value=n),o.current.previous),[n])}function I0(n){const[o,l]=h.useState(void 0);return nl(()=>{if(n){l({width:n.offsetWidth,height:n.offsetHeight});const s=new ResizeObserver(a=>{if(!Array.isArray(a)||!a.length)return;const c=a[0];let p,f;if("borderBoxSize"in c){const m=c.borderBoxSize,g=Array.isArray(m)?m[0]:m;p=g.inlineSize,f=g.blockSize}else p=n.offsetWidth,f=n.offsetHeight;l({width:p,height:f})});return s.observe(n,{box:"border-box"}),()=>s.unobserve(n)}else l(void 0)},[n]),o}function O0(n){const o=D0(n),l=h.forwardRef((s,a)=>{const{children:c,...p}=s,f=h.Children.toArray(c),m=f.find(F0);if(m){const g=m.props.children,w=f.map(x=>x===m?h.Children.count(g)>1?h.Children.only(null):h.isValidElement(g)?g.props.children:null:x);return v.jsx(o,{...p,ref:a,children:h.isValidElement(g)?h.cloneElement(g,void 0,w):null})}return v.jsx(o,{...p,ref:a,children:c})});return l.displayName=`${n}.Slot`,l}function D0(n){const o=h.forwardRef((l,s)=>{const{children:a,...c}=l;if(h.isValidElement(a)){const p=H0(a),f=W0(c,a.props);return a.type!==h.Fragment&&(f.ref=s?vm(s,p):p),h.cloneElement(a,f)}return h.Children.count(a)>1?h.Children.only(null):null});return o.displayName=`${n}.SlotClone`,o}var $0=Symbol("radix.slottable");function F0(n){return h.isValidElement(n)&&typeof n.type=="function"&&"__radixId"in n.type&&n.type.__radixId===$0}function W0(n,o){const l={...o};for(const s in o){const a=n[s],c=o[s];/^on[A-Z]/.test(s)?a&&c?l[s]=(...f)=>{const m=c(...f);return a(...f),m}:a&&(l[s]=a):s==="style"?l[s]={...a,...c}:s==="className"&&(l[s]=[a,c].filter(Boolean).join(" "))}return{...n,...l}}function H0(n){var s,a;let o=(s=Object.getOwnPropertyDescriptor(n.props,"ref"))==null?void 0:s.get,l=o&&"isReactWarning"in o&&o.isReactWarning;return l?n.ref:(o=(a=Object.getOwnPropertyDescriptor(n,"ref"))==null?void 0:a.get,l=o&&"isReactWarning"in o&&o.isReactWarning,l?n.props.ref:n.props.ref||n.ref)}var V0=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"],xm=V0.reduce((n,o)=>{const l=O0(`Primitive.${o}`),s=h.forwardRef((a,c)=>{const{asChild:p,...f}=a,m=p?l:o;return typeof window<"u"&&(window[Symbol.for("radix-ui")]=!0),v.jsx(m,{...f,ref:c})});return s.displayName=`Primitive.${o}`,{...n,[o]:s}},{}),Bi="Switch",[B0,rE]=_0(Bi),[U0,K0]=B0(Bi),wm=h.forwardRef((n,o)=>{const{__scopeSwitch:l,name:s,checked:a,defaultChecked:c,required:p,disabled:f,value:m="on",onCheckedChange:g,form:w,...x}=n,[S,E]=h.useState(null),b=ym(o,z=>E(z)),C=h.useRef(!1),P=S?w||!!S.closest("form"):!0,[N,j]=z0({prop:a,defaultProp:c??!1,onChange:g,caller:Bi});return v.jsxs(U0,{scope:l,checked:N,disabled:f,children:[v.jsx(xm.button,{type:"button",role:"switch","aria-checked":N,"aria-required":p,"data-state":bm(N),"data-disabled":f?"":void 0,disabled:f,value:m,...x,ref:b,onClick:R0(n.onClick,z=>{j(M=>!M),P&&(C.current=z.isPropagationStopped(),C.current||z.stopPropagation())})}),P&&v.jsx(Em,{control:S,bubbles:!C.current,name:s,value:m,checked:N,required:p,disabled:f,form:w,style:{transform:"translateX(-100%)"}})]})});wm.displayName=Bi;var Sm="SwitchThumb",Cm=h.forwardRef((n,o)=>{const{__scopeSwitch:l,...s}=n,a=K0(Sm,l);return v.jsx(xm.span,{"data-state":bm(a.checked),"data-disabled":a.disabled?"":void 0,...s,ref:o})});Cm.displayName=Sm;var Q0="SwitchBubbleInput",Em=h.forwardRef(({__scopeSwitch:n,control:o,checked:l,bubbles:s=!0,...a},c)=>{const p=h.useRef(null),f=ym(p,c),m=M0(l),g=I0(o);return h.useEffect(()=>{const w=p.current;if(!w)return;const x=window.HTMLInputElement.prototype,E=Object.getOwnPropertyDescriptor(x,"checked").set;if(m!==l&&E){const b=new Event("click",{bubbles:s});E.call(w,l),w.dispatchEvent(b)}},[m,l,s]),v.jsx("input",{type:"checkbox","aria-hidden":!0,defaultChecked:l,...a,tabIndex:-1,ref:f,style:{...a.style,...g,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})});Em.displayName=Q0;function bm(n){return n?"checked":"unchecked"}var G0=wm,Y0=Cm;function tl({className:n,...o}){return v.jsx(G0,{"data-slot":"switch",className:De("peer inline-flex h-[1.15rem] w-8 shrink-0 items-center rounded-full transition-all outline-none disabled:cursor-not-allowed disabled:opacity-50","data-[state=unchecked]:bg-gray-200 data-[state=unchecked]:border-2 data-[state=unchecked]:border-gray-300 data-[state=unchecked]:shadow-inner","dark:data-[state=unchecked]:bg-gray-700 dark:data-[state=unchecked]:border-gray-600","data-[state=checked]:bg-primary data-[state=checked]:border data-[state=checked]:border-primary-foreground/20","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]",n),...o,children:v.jsx(Y0,{"data-slot":"switch-thumb",className:De("pointer-events-none block size-4 rounded-full ring-0 transition-transform shadow-sm border border-gray-300","data-[state=unchecked]:translate-x-0 data-[state=unchecked]:bg-white data-[state=unchecked]:shadow-md","dark:data-[state=unchecked]:bg-gray-200 dark:data-[state=unchecked]:border-gray-500","data-[state=checked]:translate-x-4 data-[state=checked]:bg-white data-[state=checked]:border-gray-400","dark:data-[state=checked]:bg-primary-foreground dark:data-[state=checked]:border-gray-300")})})}function Jf(n,o){if(typeof n=="function")return n(o);n!=null&&(n.current=o)}function X0(...n){return o=>{let l=!1;const s=n.map(a=>{const c=Jf(a,o);return!l&&typeof c=="function"&&(l=!0),c});if(l)return()=>{for(let a=0;a<s.length;a++){const c=s[a];typeof c=="function"?c():Jf(n[a],null)}}}}function Z0(n){const o=q0(n),l=h.forwardRef((s,a)=>{const{children:c,...p}=s,f=h.Children.toArray(c),m=f.find(ex);if(m){const g=m.props.children,w=f.map(x=>x===m?h.Children.count(g)>1?h.Children.only(null):h.isValidElement(g)?g.props.children:null:x);return v.jsx(o,{...p,ref:a,children:h.isValidElement(g)?h.cloneElement(g,void 0,w):null})}return v.jsx(o,{...p,ref:a,children:c})});return l.displayName=`${n}.Slot`,l}function q0(n){const o=h.forwardRef((l,s)=>{const{children:a,...c}=l;if(h.isValidElement(a)){const p=nx(a),f=tx(c,a.props);return a.type!==h.Fragment&&(f.ref=s?X0(s,p):p),h.cloneElement(a,f)}return h.Children.count(a)>1?h.Children.only(null):null});return o.displayName=`${n}.SlotClone`,o}var J0=Symbol("radix.slottable");function ex(n){return h.isValidElement(n)&&typeof n.type=="function"&&"__radixId"in n.type&&n.type.__radixId===J0}function tx(n,o){const l={...o};for(const s in o){const a=n[s],c=o[s];/^on[A-Z]/.test(s)?a&&c?l[s]=(...f)=>{const m=c(...f);return a(...f),m}:a&&(l[s]=a):s==="style"?l[s]={...a,...c}:s==="className"&&(l[s]=[a,c].filter(Boolean).join(" "))}return{...n,...l}}function nx(n){var s,a;let o=(s=Object.getOwnPropertyDescriptor(n.props,"ref"))==null?void 0:s.get,l=o&&"isReactWarning"in o&&o.isReactWarning;return l?n.ref:(o=(a=Object.getOwnPropertyDescriptor(n,"ref"))==null?void 0:a.get,l=o&&"isReactWarning"in o&&o.isReactWarning,l?n.props.ref:n.props.ref||n.ref)}var rx=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"],ox=rx.reduce((n,o)=>{const l=Z0(`Primitive.${o}`),s=h.forwardRef((a,c)=>{const{asChild:p,...f}=a,m=p?l:o;return typeof window<"u"&&(window[Symbol.for("radix-ui")]=!0),v.jsx(m,{...f,ref:c})});return s.displayName=`Primitive.${o}`,{...n,[o]:s}},{}),lx="Label",km=h.forwardRef((n,o)=>v.jsx(ox.label,{...n,ref:o,onMouseDown:l=>{var a;l.target.closest("button, input, select, textarea")||((a=n.onMouseDown)==null||a.call(n,l),!l.defaultPrevented&&l.detail>1&&l.preventDefault())}}));km.displayName=lx;var Pm=km;const ep=n=>typeof n=="boolean"?`${n}`:n===0?"0":n,tp=cm,Ru=(n,o)=>l=>{var s;if((o==null?void 0:o.variants)==null)return tp(n,l==null?void 0:l.class,l==null?void 0:l.className);const{variants:a,defaultVariants:c}=o,p=Object.keys(a).map(g=>{const w=l==null?void 0:l[g],x=c==null?void 0:c[g];if(w===null)return null;const S=ep(w)||ep(x);return a[g][S]}),f=l&&Object.entries(l).reduce((g,w)=>{let[x,S]=w;return S===void 0||(g[x]=S),g},{}),m=o==null||(s=o.compoundVariants)===null||s===void 0?void 0:s.reduce((g,w)=>{let{class:x,className:S,...E}=w;return Object.entries(E).every(b=>{let[C,P]=b;return Array.isArray(P)?P.includes({...c,...f}[C]):{...c,...f}[C]===P})?[...g,x,S]:g},[]);return tp(n,p,m,l==null?void 0:l.class,l==null?void 0:l.className)},ix=Ru("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),We=h.forwardRef(({className:n,...o},l)=>v.jsx(Pm,{"data-slot":"label",ref:l,className:De(ix(),n),...o}));We.displayName=Pm.displayName;function np(n,o){if(typeof n=="function")return n(o);n!=null&&(n.current=o)}function Nm(...n){return o=>{let l=!1;const s=n.map(a=>{const c=np(a,o);return!l&&typeof c=="function"&&(l=!0),c});if(l)return()=>{for(let a=0;a<s.length;a++){const c=s[a];typeof c=="function"?c():np(n[a],null)}}}}function Je(...n){return h.useCallback(Nm(...n),n)}var fr=h.forwardRef((n,o)=>{const{children:l,...s}=n,a=h.Children.toArray(l),c=a.find(ax);if(c){const p=c.props.children,f=a.map(m=>m===c?h.Children.count(p)>1?h.Children.only(null):h.isValidElement(p)?p.props.children:null:m);return v.jsx(uu,{...s,ref:o,children:h.isValidElement(p)?h.cloneElement(p,void 0,f):null})}return v.jsx(uu,{...s,ref:o,children:l})});fr.displayName="Slot";var uu=h.forwardRef((n,o)=>{const{children:l,...s}=n;if(h.isValidElement(l)){const a=cx(l),c=ux(s,l.props);return l.type!==h.Fragment&&(c.ref=o?Nm(o,a):a),h.cloneElement(l,c)}return h.Children.count(l)>1?h.Children.only(null):null});uu.displayName="SlotClone";var sx=({children:n})=>v.jsx(v.Fragment,{children:n});function ax(n){return h.isValidElement(n)&&n.type===sx}function ux(n,o){const l={...o};for(const s in o){const a=n[s],c=o[s];/^on[A-Z]/.test(s)?a&&c?l[s]=(...f)=>{c(...f),a(...f)}:a&&(l[s]=a):s==="style"?l[s]={...a,...c}:s==="className"&&(l[s]=[a,c].filter(Boolean).join(" "))}return{...n,...l}}function cx(n){var s,a;let o=(s=Object.getOwnPropertyDescriptor(n.props,"ref"))==null?void 0:s.get,l=o&&"isReactWarning"in o&&o.isReactWarning;return l?n.ref:(o=(a=Object.getOwnPropertyDescriptor(n,"ref"))==null?void 0:a.get,l=o&&"isReactWarning"in o&&o.isReactWarning,l?n.props.ref:n.props.ref||n.ref)}const dx=Ru("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-white hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9 rounded-md"}},defaultVariants:{variant:"default",size:"default"}});function zt({className:n,variant:o,size:l,asChild:s=!1,...a}){const c=s?fr:"button";return v.jsx(c,{"data-slot":"button",className:De(dx({variant:o,size:l,className:n})),...a})}/**
 * @license lucide-react v0.487.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const fx=n=>n.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),px=n=>n.replace(/^([A-Z])|[\s-_]+(\w)/g,(o,l,s)=>s?s.toUpperCase():l.toLowerCase()),rp=n=>{const o=px(n);return o.charAt(0).toUpperCase()+o.slice(1)},Rm=(...n)=>n.filter((o,l,s)=>!!o&&o.trim()!==""&&s.indexOf(o)===l).join(" ").trim();/**
 * @license lucide-react v0.487.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var mx={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.487.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const hx=h.forwardRef(({color:n="currentColor",size:o=24,strokeWidth:l=2,absoluteStrokeWidth:s,className:a="",children:c,iconNode:p,...f},m)=>h.createElement("svg",{ref:m,...mx,width:o,height:o,stroke:n,strokeWidth:s?Number(l)*24/Number(o):l,className:Rm("lucide",a),...f},[...p.map(([g,w])=>h.createElement(g,w)),...Array.isArray(c)?c:[c]]));/**
 * @license lucide-react v0.487.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ot=(n,o)=>{const l=h.forwardRef(({className:s,...a},c)=>h.createElement(hx,{ref:c,iconNode:o,className:Rm(`lucide-${fx(rp(n))}`,`lucide-${n}`,s),...a}));return l.displayName=rp(n),l};/**
 * @license lucide-react v0.487.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const gx=[["path",{d:"m8 2 1.88 1.88",key:"fmnt4t"}],["path",{d:"M14.12 3.88 16 2",key:"qol33r"}],["path",{d:"M9 7.13v-1a3.003 3.003 0 1 1 6 0v1",key:"d7y7pr"}],["path",{d:"M12 20c-3.3 0-6-2.7-6-6v-3a4 4 0 0 1 4-4h4a4 4 0 0 1 4 4v3c0 3.3-2.7 6-6 6",key:"xs1cw7"}],["path",{d:"M12 20v-9",key:"1qisl0"}],["path",{d:"M6.53 9C4.6 8.8 3 7.1 3 5",key:"32zzws"}],["path",{d:"M6 13H2",key:"82j7cp"}],["path",{d:"M3 21c0-2.1 1.7-3.9 3.8-4",key:"4p0ekp"}],["path",{d:"M20.97 5c0 2.1-1.6 3.8-3.5 4",key:"18gb23"}],["path",{d:"M22 13h-4",key:"1jl80f"}],["path",{d:"M17.2 17c2.1.1 3.8 1.9 3.8 4",key:"k3fwyw"}]],vx=ot("bug",gx);/**
 * @license lucide-react v0.487.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const yx=[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]],xx=ot("check",yx);/**
 * @license lucide-react v0.487.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const wx=[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]],_u=ot("chevron-down",wx);/**
 * @license lucide-react v0.487.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Sx=[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]],Cx=ot("chevron-up",Sx);/**
 * @license lucide-react v0.487.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ex=[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]],_m=ot("clock",Ex);/**
 * @license lucide-react v0.487.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const bx=[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]],kx=ot("file-text",bx);/**
 * @license lucide-react v0.487.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Px=[["circle",{cx:"9",cy:"12",r:"1",key:"1vctgf"}],["circle",{cx:"9",cy:"5",r:"1",key:"hp0tcf"}],["circle",{cx:"9",cy:"19",r:"1",key:"fkjjf6"}],["circle",{cx:"15",cy:"12",r:"1",key:"1tmaij"}],["circle",{cx:"15",cy:"5",r:"1",key:"19l28e"}],["circle",{cx:"15",cy:"19",r:"1",key:"f4zoj3"}]],Nx=ot("grip-vertical",Px);/**
 * @license lucide-react v0.487.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Rx=[["path",{d:"m5 8 6 6",key:"1wu5hv"}],["path",{d:"m4 14 6-6 2-3",key:"1k1g8d"}],["path",{d:"M2 5h12",key:"or177f"}],["path",{d:"M7 2h1",key:"1t2jsx"}],["path",{d:"m22 22-5-10-5 10",key:"don7ne"}],["path",{d:"M14 18h6",key:"1m8k6r"}]],cu=ot("languages",Rx);/**
 * @license lucide-react v0.487.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const _x=[["path",{d:"M15 14c.2-1 .7-1.7 1.5-2.5 1-.9 1.5-2.2 1.5-3.5A6 6 0 0 0 6 8c0 1 .2 2.2 1.5 ******* 1.3 1.5 1.5 2.5",key:"1gvzjb"}],["path",{d:"M9 18h6",key:"x1upvd"}],["path",{d:"M10 22h4",key:"ceow96"}]],Tx=ot("lightbulb",_x);/**
 * @license lucide-react v0.487.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const jx=[["path",{d:"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z",key:"1lielz"}]],zx=ot("message-square",jx);/**
 * @license lucide-react v0.487.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ax=[["line",{x1:"2",x2:"22",y1:"2",y2:"22",key:"a6p6uj"}],["path",{d:"M18.89 13.23A7.12 7.12 0 0 0 19 12v-2",key:"80xlxr"}],["path",{d:"M5 10v2a7 7 0 0 0 12 5",key:"p2k8kg"}],["path",{d:"M15 9.34V5a3 3 0 0 0-5.68-1.33",key:"1gzdoj"}],["path",{d:"M9 9v3a3 3 0 0 0 5.12 2.12",key:"r2i35w"}],["line",{x1:"12",x2:"12",y1:"19",y2:"22",key:"x3vr5v"}]],Lx=ot("mic-off",Ax);/**
 * @license lucide-react v0.487.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Mx=[["path",{d:"M12 2a3 3 0 0 0-3 3v7a3 3 0 0 0 6 0V5a3 3 0 0 0-3-3Z",key:"131961"}],["path",{d:"M19 10v2a7 7 0 0 1-14 0v-2",key:"1vc78b"}],["line",{x1:"12",x2:"12",y1:"19",y2:"22",key:"x3vr5v"}]],Ix=ot("mic",Mx);/**
 * @license lucide-react v0.487.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ox=[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]],Tm=ot("plus",Ox);/**
 * @license lucide-react v0.487.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Dx=[["path",{d:"M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8",key:"1357e3"}],["path",{d:"M3 3v5h5",key:"1xhq8a"}]],$x=ot("rotate-ccw",Dx);/**
 * @license lucide-react v0.487.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Fx=[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]],jm=ot("trash-2",Fx);/**
 * @license lucide-react v0.487.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Wx=[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]],Hx=ot("x",Wx);function Vx({id:n,timestamp:o,originalText:l,translatedText:s,originalLang:a,targetLang:c,confidence:p,isDebugLatest:f=!1,onPlay:m}){const g={high:"border-l-green-500 bg-green-50",medium:"border-l-yellow-500 bg-yellow-50",low:"border-l-orange-500 bg-orange-50"},w=!l.trim()&&!s.trim(),x=n.startsWith("debug-"),S=a.toLowerCase()===c.toLowerCase(),E=()=>x?f?"border-l-yellow-500 bg-yellow-100":"border-l-green-500 bg-green-100":g[p];return v.jsxs("div",{className:`p-4 mb-4 border-l-4 rounded-lg ${E()} hover:shadow-sm transition-shadow cursor-pointer ${w?"opacity-60":""}`,onClick:()=>m==null?void 0:m(n),children:[v.jsx("div",{className:"flex items-center justify-between mb-3",children:v.jsxs("div",{className:"flex items-center gap-2",children:[v.jsx(_m,{size:14,className:"text-muted-foreground"}),v.jsx("span",{className:"text-xs text-muted-foreground",children:o}),x&&v.jsx("span",{className:`text-xs px-1.5 py-0.5 rounded text-center ${f?"bg-yellow-200 text-yellow-800":"bg-green-200 text-green-800"}`,children:f?"DEBUG (최신)":"DEBUG"})]})}),v.jsx("div",{className:"space-y-2",children:S?v.jsxs("div",{children:[v.jsxs("div",{className:"flex items-center gap-2 mb-1.5",children:[v.jsx("span",{className:"text-xs px-2 py-1 bg-blue-100 text-blue-800 rounded uppercase",children:a}),v.jsx("span",{className:"text-xs text-muted-foreground",children:"원문"})]}),v.jsx("p",{className:"leading-relaxed",children:l||v.jsx("span",{className:"text-muted-foreground italic",children:"음성 인식 대기 중..."})})]}):v.jsxs(v.Fragment,{children:[v.jsxs("div",{children:[v.jsxs("div",{className:"flex items-center gap-2 mb-1.5",children:[v.jsx("span",{className:"text-xs px-2 py-1 bg-blue-100 text-blue-800 rounded uppercase",children:a}),v.jsx("span",{className:"text-xs text-muted-foreground",children:"원문"})]}),v.jsx("p",{className:"text-sm text-gray-700 leading-relaxed mb-2",children:l||v.jsx("span",{className:"text-muted-foreground italic",children:"음성 인식 대기 중..."})})]}),v.jsxs("div",{children:[v.jsxs("div",{className:"flex items-center gap-2 mb-1.5",children:[v.jsx("span",{className:"text-xs px-2 py-1 bg-green-100 text-green-800 rounded uppercase",children:c}),v.jsx("span",{className:"text-xs text-muted-foreground",children:"번역"})]}),v.jsx("p",{className:"leading-relaxed",children:s||v.jsx("span",{className:"text-muted-foreground italic",children:"번역 대기 중..."})})]})]})})]})}function op({translations:n,isSummaryPanelVisible:o,onSummaryPanelToggle:l,isQuestionHelperVisible:s,onQuestionHelperToggle:a,scrollToBottom:c,onScrolledToBottom:p}){const f=h.useRef(null),m=h.useRef(null),[g,w]=h.useState(!1),x=b=>{const C=n.find(P=>P.id===b);C&&console.log(`Playing text: ${C.originalText} in language: ${C.originalLang}`)},S=h.useCallback(()=>{if(!m.current)return;let b=null,C=m.current.parentElement,P=0;for(;C&&P<5;){if(C.scrollHeight>C.clientHeight){b=C;break}C=C.parentElement,P++}if(b){const{scrollTop:N,scrollHeight:j,clientHeight:z}=b,M=N+z>=j-10,V=j>z&&!M;w(V)}else w(!1)},[]),E=h.useCallback(()=>{f.current&&f.current.scrollIntoView({behavior:"smooth",block:"end"})},[]);return h.useEffect(()=>{c&&f.current&&(f.current.scrollIntoView({behavior:"smooth",block:"end"}),setTimeout(()=>{p==null||p()},500))},[c,p]),h.useEffect(()=>{const b=setInterval(()=>{S()},1e3);return S(),()=>{clearInterval(b)}},[S]),h.useEffect(()=>{const b=setTimeout(S,100);return()=>clearTimeout(b)},[n,S]),v.jsxs("div",{className:"flex-1 pt-4 px-4 flex flex-col h-full relative",children:[v.jsx("div",{className:"mb-4 shrink-0",children:v.jsxs("div",{className:"flex items-start justify-between",children:[v.jsxs("div",{children:[v.jsx("h2",{className:"text-lg mb-1",children:"실시간 통역"}),v.jsx("p",{className:"text-sm text-muted-foreground",children:"음성이 실시간으로 문단 단위로 번역되어 표시됩니다"})]}),v.jsx("div",{className:"bg-gray-50 rounded-lg p-3 border border-gray-200 shadow-sm",children:v.jsxs("div",{className:"flex flex-col gap-2.5",children:[v.jsxs("div",{className:"flex items-center gap-3",children:[v.jsx(We,{className:"text-xs text-muted-foreground w-20 text-right",children:"요약 패널:"}),v.jsxs("div",{className:"flex items-center gap-1",children:[v.jsx(We,{className:"text-xs text-muted-foreground",children:"숨김"}),v.jsx(tl,{checked:o,onCheckedChange:l,className:"data-[state=checked]:bg-blue-600 scale-75"}),v.jsx(We,{className:"text-xs text-muted-foreground",children:"표시"})]})]}),v.jsxs("div",{className:"flex items-center gap-3",children:[v.jsx(We,{className:"text-xs text-muted-foreground w-20 text-right",children:"질문 도우미:"}),v.jsxs("div",{className:"flex items-center gap-1",children:[v.jsx(We,{className:"text-xs text-muted-foreground",children:"숨김"}),v.jsx(tl,{checked:s,onCheckedChange:a,className:"data-[state=checked]:bg-purple-600 scale-75"}),v.jsx(We,{className:"text-xs text-muted-foreground",children:"표시"})]})]})]})})]})}),v.jsx(gm,{className:"flex-1 min-h-0",children:v.jsx("div",{ref:m,className:"space-y-4 pr-2",children:n.length===0?v.jsxs("div",{className:"text-center py-12 text-muted-foreground",children:[v.jsx("p",{children:"마이크를 켜고 대화를 시작해주세요"}),v.jsx("p",{className:"text-xs mt-1",children:"문단 단위로 통역 결과가 표시됩니다"})]}):v.jsxs(v.Fragment,{children:[n.map(b=>v.jsx(Vx,{...b,onPlay:x},b.id)),v.jsx("div",{ref:f})]})})}),g&&v.jsx(zt,{onClick:E,className:"absolute bottom-4 right-4 h-10 w-10 rounded-full shadow-lg bg-blue-600/80 hover:bg-blue-700/90 text-white border-0 transition-all duration-200 hover:scale-105 z-50 backdrop-blur-sm",size:"icon",title:"맨 아래로 스크롤",children:v.jsx(_u,{className:"h-4 w-4"})})]})}function Tu({className:n,...o}){return v.jsx("div",{"data-slot":"card",className:De("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border",n),...o})}function Bx({className:n,...o}){return v.jsx("div",{"data-slot":"card-header",className:De("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 pt-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",n),...o})}function Ux({className:n,...o}){return v.jsx("h4",{"data-slot":"card-title",className:De("leading-none",n),...o})}function ju({className:n,...o}){return v.jsx("div",{"data-slot":"card-content",className:De("px-6 [&:last-child]:pb-6",n),...o})}const Kx=Ru("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function du({className:n,variant:o,asChild:l=!1,...s}){const a=l?fr:"span";return v.jsx(a,{"data-slot":"badge",className:De(Kx({variant:o}),n),...s})}const Xr=h.forwardRef(({className:n,type:o,...l},s)=>v.jsx("input",{type:o,"data-slot":"input",className:De("border-input bg-input-background focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive flex h-9 w-full rounded-md border px-3 py-1 text-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50",n),ref:s,...l}));Xr.displayName="Input";function Qx({className:n,...o}){return v.jsx("textarea",{"data-slot":"textarea",className:De("resize-none border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-input-background px-3 py-2 text-base transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",n),...o})}function lp(n,o){if(typeof n=="function")return n(o);n!=null&&(n.current=o)}function Gx(...n){return o=>{let l=!1;const s=n.map(a=>{const c=lp(a,o);return!l&&typeof c=="function"&&(l=!0),c});if(l)return()=>{for(let a=0;a<s.length;a++){const c=s[a];typeof c=="function"?c():lp(n[a],null)}}}}function Yx(n){const o=Xx(n),l=h.forwardRef((s,a)=>{const{children:c,...p}=s,f=h.Children.toArray(c),m=f.find(qx);if(m){const g=m.props.children,w=f.map(x=>x===m?h.Children.count(g)>1?h.Children.only(null):h.isValidElement(g)?g.props.children:null:x);return v.jsx(o,{...p,ref:a,children:h.isValidElement(g)?h.cloneElement(g,void 0,w):null})}return v.jsx(o,{...p,ref:a,children:c})});return l.displayName=`${n}.Slot`,l}function Xx(n){const o=h.forwardRef((l,s)=>{const{children:a,...c}=l;if(h.isValidElement(a)){const p=ew(a),f=Jx(c,a.props);return a.type!==h.Fragment&&(f.ref=s?Gx(s,p):p),h.cloneElement(a,f)}return h.Children.count(a)>1?h.Children.only(null):null});return o.displayName=`${n}.SlotClone`,o}var Zx=Symbol("radix.slottable");function qx(n){return h.isValidElement(n)&&typeof n.type=="function"&&"__radixId"in n.type&&n.type.__radixId===Zx}function Jx(n,o){const l={...o};for(const s in o){const a=n[s],c=o[s];/^on[A-Z]/.test(s)?a&&c?l[s]=(...f)=>{const m=c(...f);return a(...f),m}:a&&(l[s]=a):s==="style"?l[s]={...a,...c}:s==="className"&&(l[s]=[a,c].filter(Boolean).join(" "))}return{...n,...l}}function ew(n){var s,a;let o=(s=Object.getOwnPropertyDescriptor(n.props,"ref"))==null?void 0:s.get,l=o&&"isReactWarning"in o&&o.isReactWarning;return l?n.ref:(o=(a=Object.getOwnPropertyDescriptor(n,"ref"))==null?void 0:a.get,l=o&&"isReactWarning"in o&&o.isReactWarning,l?n.props.ref:n.props.ref||n.ref)}var tw=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"],nw=tw.reduce((n,o)=>{const l=Yx(`Primitive.${o}`),s=h.forwardRef((a,c)=>{const{asChild:p,...f}=a,m=p?l:o;return typeof window<"u"&&(window[Symbol.for("radix-ui")]=!0),v.jsx(m,{...f,ref:c})});return s.displayName=`Primitive.${o}`,{...n,[o]:s}},{}),rw="Separator",ip="horizontal",ow=["horizontal","vertical"],zm=h.forwardRef((n,o)=>{const{decorative:l,orientation:s=ip,...a}=n,c=lw(s)?s:ip,f=l?{role:"none"}:{"aria-orientation":c==="vertical"?c:void 0,role:"separator"};return v.jsx(nw.div,{"data-orientation":c,...f,...a,ref:o})});zm.displayName=rw;function lw(n){return ow.includes(n)}var iw=zm;function sw({className:n,orientation:o="horizontal",decorative:l=!0,...s}){return v.jsx(iw,{"data-slot":"separator-root",decorative:l,orientation:o,className:De("bg-border shrink-0 data-[orientation=horizontal]:h-px data-[orientation=horizontal]:w-full data-[orientation=vertical]:h-full data-[orientation=vertical]:w-px",n),...s})}function aw({currentTopic:n,onTopicChange:o,onClearAllSummaries:l,onAddSummary:s}){const[a,c]=h.useState(n),[p,f]=h.useState(""),[m,g]=h.useState(""),[w,x]=h.useState(""),S=()=>{a.trim()?(o(a.trim()),console.log("현재 주제가 적용되었습니다:",a.trim())):console.log("주제 적용 실패: 빈 텍스트입니다.")},E=N=>{N.key==="Enter"&&S()},b=()=>{l&&(l(),console.log("모든 구간별 요약이 삭제되었습니다."))},C=()=>{if(!p.trim()||!m.trim()||!w.trim()){console.log("요약 추가 실패: 모든 필드를 입력해주세요.");return}const N=w.split(`
`).map(j=>j.trim()).filter(j=>j!=="");s&&(s(p.trim(),m.trim(),N),f(""),g(""),x(""),console.log("새 구간별 요약이 추가되었습니다."))},P=p.trim()&&m.trim()&&w.trim();return v.jsx("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-3 shadow-sm",children:v.jsxs("div",{className:"space-y-4",children:[v.jsx("div",{className:"text-center",children:v.jsx(We,{className:"text-sm text-blue-700",children:"요약패널 디버그"})}),v.jsxs("div",{className:"space-y-2",children:[v.jsx(We,{className:"text-xs text-blue-600",children:"현재 주제 편집:"}),v.jsx(Xr,{value:a,onChange:N=>c(N.target.value),onKeyPress:E,placeholder:"새로운 주제를 입력하세요...",className:"text-sm bg-white border-blue-200 focus:border-blue-400"}),v.jsx("div",{className:"flex justify-center",children:v.jsx(zt,{onClick:S,size:"sm",className:"bg-blue-600 hover:bg-blue-700 text-white px-4 py-1",children:"현재 주제 적용"})})]}),v.jsx(sw,{className:"bg-blue-200"}),v.jsxs("div",{className:"space-y-3",children:[v.jsx(We,{className:"text-xs text-blue-600",children:"구간별 요약 관리:"}),v.jsx("div",{className:"flex justify-center",children:v.jsxs(zt,{onClick:b,variant:"destructive",size:"sm",className:"px-3 py-1",children:[v.jsx(jm,{size:14,className:"mr-1"}),"구간별 요약 전체 지우기"]})}),v.jsxs("div",{className:"space-y-2",children:[v.jsx(We,{className:"text-xs text-blue-600",children:"새 구간별 요약 추가:"}),v.jsxs("div",{className:"space-y-1",children:[v.jsx(We,{className:"text-xs text-gray-600",children:"시간 범위:"}),v.jsx(Xr,{value:p,onChange:N=>f(N.target.value),placeholder:"예: 10:50-10:55",className:"text-sm bg-white border-blue-200 focus:border-blue-400"})]}),v.jsxs("div",{className:"space-y-1",children:[v.jsx(We,{className:"text-xs text-gray-600",children:"요약 주제:"}),v.jsx(Xr,{value:m,onChange:N=>g(N.target.value),placeholder:"요약 내용을 입력하세요...",className:"text-sm bg-white border-blue-200 focus:border-blue-400"})]}),v.jsxs("div",{className:"space-y-1",children:[v.jsx(We,{className:"text-xs text-gray-600",children:"주요 포인트 (각 줄마다 하나씩):"}),v.jsx(Qx,{value:w,onChange:N=>x(N.target.value),placeholder:`주요 포인트를 입력하세요...
각 줄마다 하나의 포인트를 입력하면 됩니다`,className:"text-sm bg-white border-blue-200 focus:border-blue-400 h-16 resize-none"})]}),v.jsx("div",{className:"flex justify-center pt-2",children:v.jsxs(zt,{onClick:C,disabled:!P,size:"sm",className:"bg-green-600 hover:bg-green-700 disabled:bg-gray-400 text-white px-4 py-1",children:[v.jsx(Tm,{size:14,className:"mr-1"}),"구간별 요약 추가"]})})]})]})]})})}function uw({currentTopic:n,summaries:o,isSummaryDebugMode:l=!1,onTopicChange:s,onClearAllSummaries:a,onAddSummary:c,summaryScrollToBottom:p=!1,onSummaryScrolledToBottom:f}){const m=h.useRef(null),g=h.useRef(null);return h.useEffect(()=>{p&&setTimeout(()=>{let w=!1;if(g.current){const x=g.current.querySelector("[data-radix-scroll-area-viewport]");x&&(x.scrollTop=x.scrollHeight,w=!0,console.log("스크롤 실행됨 (방법 1): viewport.scrollHeight =",x.scrollHeight))}if(!w&&m.current){const x=m.current.closest("[data-radix-scroll-area-viewport]");x&&(x.scrollTop=x.scrollHeight,w=!0,console.log("스크롤 실행됨 (방법 2): scrollContainer.scrollHeight =",x.scrollHeight))}if(!w&&m.current){const x=m.current.parentElement;x&&x.hasAttribute("data-radix-scroll-area-viewport")&&(x.scrollTop=x.scrollHeight,w=!0,console.log("스크롤 실행됨 (방법 3): parent.scrollHeight =",x.scrollHeight))}w||console.log("스크롤 실행 실패: viewport를 찾을 수 없음"),f==null||f()},50)},[p,o.length,f]),v.jsxs("div",{className:"h-full flex flex-col bg-white border-l",children:[v.jsx("div",{className:"p-4 border-b bg-blue-50 shrink-0",children:v.jsxs("div",{className:"flex items-start gap-2",children:[v.jsx(zx,{size:16,className:"text-blue-600 mt-1 shrink-0"}),v.jsxs("div",{children:[v.jsx("h3",{className:"text-sm mb-1",children:"현재 주제"}),v.jsx("p",{className:"text-sm text-blue-800",children:n})]})]})}),v.jsx("div",{className:"p-4 border-b shrink-0",children:v.jsxs("div",{className:"flex items-center gap-2",children:[v.jsx(_m,{size:16,className:"text-gray-600"}),v.jsx("h3",{className:"text-sm",children:"구간별 요약"})]})}),v.jsx("div",{className:`flex-1 min-h-0 ${l?"overflow-hidden":""}`,children:v.jsx("div",{ref:g,className:"h-full",children:v.jsx(gm,{className:"h-full",children:v.jsx("div",{ref:m,className:"p-4 space-y-3",children:o.map(w=>v.jsxs(Tu,{className:"shadow-sm",children:[v.jsx(Bx,{className:"pb-1",children:v.jsx(Ux,{className:"text-sm flex items-center gap-2",children:v.jsx(du,{variant:"outline",className:"text-xs",children:w.timeRange})})}),v.jsxs(ju,{className:"pt-1",children:[v.jsx("p",{className:"text-sm text-gray-800 mb-2",children:w.summary}),v.jsxs("div",{className:"space-y-1",children:[v.jsx("p",{className:"text-xs text-gray-600",children:"주요 포인트:"}),v.jsx("ul",{className:"space-y-0.5",children:w.keyPoints.map((x,S)=>v.jsxs("li",{className:"text-xs text-gray-700 flex items-start gap-1",children:[v.jsx("span",{className:"text-blue-500 mt-1",children:"•"}),v.jsx("span",{children:x})]},S))})]})]})]},w.id))})})})}),l&&s&&v.jsx("div",{className:"shrink-0 border-t bg-white animate-in slide-in-from-bottom-2 duration-300 max-h-[50vh] overflow-y-auto",children:v.jsx("div",{className:"p-4",children:v.jsx(aw,{currentTopic:n,onTopicChange:s,onClearAllSummaries:a,onAddSummary:c})})})]})}function sp(n,[o,l]){return Math.min(l,Math.max(o,n))}function Ve(n,o,{checkForDefaultPrevented:l=!0}={}){return function(a){if(n==null||n(a),l===!1||!a.defaultPrevented)return o==null?void 0:o(a)}}function zu(n,o=[]){let l=[];function s(c,p){const f=h.createContext(p),m=l.length;l=[...l,p];const g=x=>{var N;const{scope:S,children:E,...b}=x,C=((N=S==null?void 0:S[n])==null?void 0:N[m])||f,P=h.useMemo(()=>b,Object.values(b));return v.jsx(C.Provider,{value:P,children:E})};g.displayName=c+"Provider";function w(x,S){var C;const E=((C=S==null?void 0:S[n])==null?void 0:C[m])||f,b=h.useContext(E);if(b)return b;if(p!==void 0)return p;throw new Error(`\`${x}\` must be used within \`${c}\``)}return[g,w]}const a=()=>{const c=l.map(p=>h.createContext(p));return function(f){const m=(f==null?void 0:f[n])||c;return h.useMemo(()=>({[`__scope${n}`]:{...f,[n]:m}}),[f,m])}};return a.scopeName=n,[s,cw(a,...o)]}function cw(...n){const o=n[0];if(n.length===1)return o;const l=()=>{const s=n.map(a=>({useScope:a(),scopeName:a.scopeName}));return function(c){const p=s.reduce((f,{useScope:m,scopeName:g})=>{const x=m(c)[`__scope${g}`];return{...f,...x}},{});return h.useMemo(()=>({[`__scope${o.scopeName}`]:p}),[p])}};return l.scopeName=o.scopeName,l}function dw(n){const o=n+"CollectionProvider",[l,s]=zu(o),[a,c]=l(o,{collectionRef:{current:null},itemMap:new Map}),p=E=>{const{scope:b,children:C}=E,P=$n.useRef(null),N=$n.useRef(new Map).current;return v.jsx(a,{scope:b,itemMap:N,collectionRef:P,children:C})};p.displayName=o;const f=n+"CollectionSlot",m=$n.forwardRef((E,b)=>{const{scope:C,children:P}=E,N=c(f,C),j=Je(b,N.collectionRef);return v.jsx(fr,{ref:j,children:P})});m.displayName=f;const g=n+"CollectionItemSlot",w="data-radix-collection-item",x=$n.forwardRef((E,b)=>{const{scope:C,children:P,...N}=E,j=$n.useRef(null),z=Je(b,j),M=c(g,C);return $n.useEffect(()=>(M.itemMap.set(j,{ref:j,...N}),()=>void M.itemMap.delete(j))),v.jsx(fr,{[w]:"",ref:z,children:P})});x.displayName=g;function S(E){const b=c(n+"CollectionConsumer",E);return $n.useCallback(()=>{const P=b.collectionRef.current;if(!P)return[];const N=Array.from(P.querySelectorAll(`[${w}]`));return Array.from(b.itemMap.values()).sort((M,D)=>N.indexOf(M.ref.current)-N.indexOf(D.ref.current))},[b.collectionRef,b.itemMap])}return[{Provider:p,Slot:m,ItemSlot:x},S,s]}var fw=h.createContext(void 0);function pw(n){const o=h.useContext(fw);return n||o||"ltr"}var mw=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","span","svg","ul"],Be=mw.reduce((n,o)=>{const l=h.forwardRef((s,a)=>{const{asChild:c,...p}=s,f=c?fr:o;return typeof window<"u"&&(window[Symbol.for("radix-ui")]=!0),v.jsx(f,{...p,ref:a})});return l.displayName=`Primitive.${o}`,{...n,[o]:l}},{});function hw(n,o){n&&sl.flushSync(()=>n.dispatchEvent(o))}function pr(n){const o=h.useRef(n);return h.useEffect(()=>{o.current=n}),h.useMemo(()=>(...l)=>{var s;return(s=o.current)==null?void 0:s.call(o,...l)},[])}function gw(n,o=globalThis==null?void 0:globalThis.document){const l=pr(n);h.useEffect(()=>{const s=a=>{a.key==="Escape"&&l(a)};return o.addEventListener("keydown",s,{capture:!0}),()=>o.removeEventListener("keydown",s,{capture:!0})},[l,o])}var vw="DismissableLayer",fu="dismissableLayer.update",yw="dismissableLayer.pointerDownOutside",xw="dismissableLayer.focusOutside",ap,Am=h.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),Lm=h.forwardRef((n,o)=>{const{disableOutsidePointerEvents:l=!1,onEscapeKeyDown:s,onPointerDownOutside:a,onFocusOutside:c,onInteractOutside:p,onDismiss:f,...m}=n,g=h.useContext(Am),[w,x]=h.useState(null),S=(w==null?void 0:w.ownerDocument)??(globalThis==null?void 0:globalThis.document),[,E]=h.useState({}),b=Je(o,O=>x(O)),C=Array.from(g.layers),[P]=[...g.layersWithOutsidePointerEventsDisabled].slice(-1),N=C.indexOf(P),j=w?C.indexOf(w):-1,z=g.layersWithOutsidePointerEventsDisabled.size>0,M=j>=N,D=Cw(O=>{const ee=O.target,re=[...g.branches].some(ae=>ae.contains(ee));!M||re||(a==null||a(O),p==null||p(O),O.defaultPrevented||f==null||f())},S),V=Ew(O=>{const ee=O.target;[...g.branches].some(ae=>ae.contains(ee))||(c==null||c(O),p==null||p(O),O.defaultPrevented||f==null||f())},S);return gw(O=>{j===g.layers.size-1&&(s==null||s(O),!O.defaultPrevented&&f&&(O.preventDefault(),f()))},S),h.useEffect(()=>{if(w)return l&&(g.layersWithOutsidePointerEventsDisabled.size===0&&(ap=S.body.style.pointerEvents,S.body.style.pointerEvents="none"),g.layersWithOutsidePointerEventsDisabled.add(w)),g.layers.add(w),up(),()=>{l&&g.layersWithOutsidePointerEventsDisabled.size===1&&(S.body.style.pointerEvents=ap)}},[w,S,l,g]),h.useEffect(()=>()=>{w&&(g.layers.delete(w),g.layersWithOutsidePointerEventsDisabled.delete(w),up())},[w,g]),h.useEffect(()=>{const O=()=>E({});return document.addEventListener(fu,O),()=>document.removeEventListener(fu,O)},[]),v.jsx(Be.div,{...m,ref:b,style:{pointerEvents:z?M?"auto":"none":void 0,...n.style},onFocusCapture:Ve(n.onFocusCapture,V.onFocusCapture),onBlurCapture:Ve(n.onBlurCapture,V.onBlurCapture),onPointerDownCapture:Ve(n.onPointerDownCapture,D.onPointerDownCapture)})});Lm.displayName=vw;var ww="DismissableLayerBranch",Sw=h.forwardRef((n,o)=>{const l=h.useContext(Am),s=h.useRef(null),a=Je(o,s);return h.useEffect(()=>{const c=s.current;if(c)return l.branches.add(c),()=>{l.branches.delete(c)}},[l.branches]),v.jsx(Be.div,{...n,ref:a})});Sw.displayName=ww;function Cw(n,o=globalThis==null?void 0:globalThis.document){const l=pr(n),s=h.useRef(!1),a=h.useRef(()=>{});return h.useEffect(()=>{const c=f=>{if(f.target&&!s.current){let m=function(){Mm(yw,l,g,{discrete:!0})};const g={originalEvent:f};f.pointerType==="touch"?(o.removeEventListener("click",a.current),a.current=m,o.addEventListener("click",a.current,{once:!0})):m()}else o.removeEventListener("click",a.current);s.current=!1},p=window.setTimeout(()=>{o.addEventListener("pointerdown",c)},0);return()=>{window.clearTimeout(p),o.removeEventListener("pointerdown",c),o.removeEventListener("click",a.current)}},[o,l]),{onPointerDownCapture:()=>s.current=!0}}function Ew(n,o=globalThis==null?void 0:globalThis.document){const l=pr(n),s=h.useRef(!1);return h.useEffect(()=>{const a=c=>{c.target&&!s.current&&Mm(xw,l,{originalEvent:c},{discrete:!1})};return o.addEventListener("focusin",a),()=>o.removeEventListener("focusin",a)},[o,l]),{onFocusCapture:()=>s.current=!0,onBlurCapture:()=>s.current=!1}}function up(){const n=new CustomEvent(fu);document.dispatchEvent(n)}function Mm(n,o,l,{discrete:s}){const a=l.originalEvent.target,c=new CustomEvent(n,{bubbles:!1,cancelable:!0,detail:l});o&&a.addEventListener(n,o,{once:!0}),s?hw(a,c):a.dispatchEvent(c)}var Za=0;function bw(){h.useEffect(()=>{const n=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",n[0]??cp()),document.body.insertAdjacentElement("beforeend",n[1]??cp()),Za++,()=>{Za===1&&document.querySelectorAll("[data-radix-focus-guard]").forEach(o=>o.remove()),Za--}},[])}function cp(){const n=document.createElement("span");return n.setAttribute("data-radix-focus-guard",""),n.tabIndex=0,n.style.outline="none",n.style.opacity="0",n.style.position="fixed",n.style.pointerEvents="none",n}var qa="focusScope.autoFocusOnMount",Ja="focusScope.autoFocusOnUnmount",dp={bubbles:!1,cancelable:!0},kw="FocusScope",Im=h.forwardRef((n,o)=>{const{loop:l=!1,trapped:s=!1,onMountAutoFocus:a,onUnmountAutoFocus:c,...p}=n,[f,m]=h.useState(null),g=pr(a),w=pr(c),x=h.useRef(null),S=Je(o,C=>m(C)),E=h.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;h.useEffect(()=>{if(s){let C=function(z){if(E.paused||!f)return;const M=z.target;f.contains(M)?x.current=M:Fn(x.current,{select:!0})},P=function(z){if(E.paused||!f)return;const M=z.relatedTarget;M!==null&&(f.contains(M)||Fn(x.current,{select:!0}))},N=function(z){if(document.activeElement===document.body)for(const D of z)D.removedNodes.length>0&&Fn(f)};document.addEventListener("focusin",C),document.addEventListener("focusout",P);const j=new MutationObserver(N);return f&&j.observe(f,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",C),document.removeEventListener("focusout",P),j.disconnect()}}},[s,f,E.paused]),h.useEffect(()=>{if(f){pp.add(E);const C=document.activeElement;if(!f.contains(C)){const N=new CustomEvent(qa,dp);f.addEventListener(qa,g),f.dispatchEvent(N),N.defaultPrevented||(Pw(jw(Om(f)),{select:!0}),document.activeElement===C&&Fn(f))}return()=>{f.removeEventListener(qa,g),setTimeout(()=>{const N=new CustomEvent(Ja,dp);f.addEventListener(Ja,w),f.dispatchEvent(N),N.defaultPrevented||Fn(C??document.body,{select:!0}),f.removeEventListener(Ja,w),pp.remove(E)},0)}}},[f,g,w,E]);const b=h.useCallback(C=>{if(!l&&!s||E.paused)return;const P=C.key==="Tab"&&!C.altKey&&!C.ctrlKey&&!C.metaKey,N=document.activeElement;if(P&&N){const j=C.currentTarget,[z,M]=Nw(j);z&&M?!C.shiftKey&&N===M?(C.preventDefault(),l&&Fn(z,{select:!0})):C.shiftKey&&N===z&&(C.preventDefault(),l&&Fn(M,{select:!0})):N===j&&C.preventDefault()}},[l,s,E.paused]);return v.jsx(Be.div,{tabIndex:-1,...p,ref:S,onKeyDown:b})});Im.displayName=kw;function Pw(n,{select:o=!1}={}){const l=document.activeElement;for(const s of n)if(Fn(s,{select:o}),document.activeElement!==l)return}function Nw(n){const o=Om(n),l=fp(o,n),s=fp(o.reverse(),n);return[l,s]}function Om(n){const o=[],l=document.createTreeWalker(n,NodeFilter.SHOW_ELEMENT,{acceptNode:s=>{const a=s.tagName==="INPUT"&&s.type==="hidden";return s.disabled||s.hidden||a?NodeFilter.FILTER_SKIP:s.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;l.nextNode();)o.push(l.currentNode);return o}function fp(n,o){for(const l of n)if(!Rw(l,{upTo:o}))return l}function Rw(n,{upTo:o}){if(getComputedStyle(n).visibility==="hidden")return!0;for(;n;){if(o!==void 0&&n===o)return!1;if(getComputedStyle(n).display==="none")return!0;n=n.parentElement}return!1}function _w(n){return n instanceof HTMLInputElement&&"select"in n}function Fn(n,{select:o=!1}={}){if(n&&n.focus){const l=document.activeElement;n.focus({preventScroll:!0}),n!==l&&_w(n)&&o&&n.select()}}var pp=Tw();function Tw(){let n=[];return{add(o){const l=n[0];o!==l&&(l==null||l.pause()),n=mp(n,o),n.unshift(o)},remove(o){var l;n=mp(n,o),(l=n[0])==null||l.resume()}}}function mp(n,o){const l=[...n],s=l.indexOf(o);return s!==-1&&l.splice(s,1),l}function jw(n){return n.filter(o=>o.tagName!=="A")}var zw=globalThis!=null&&globalThis.document?h.useLayoutEffect:()=>{},Aw=Wi.useId||(()=>{}),Lw=0;function Au(n){const[o,l]=h.useState(Aw());return zw(()=>{l(s=>s??String(Lw++))},[n]),n||(o?`radix-${o}`:"")}const Mw=["top","right","bottom","left"],Hn=Math.min,Et=Math.max,Li=Math.round,Ei=Math.floor,en=n=>({x:n,y:n}),Iw={left:"right",right:"left",bottom:"top",top:"bottom"},Ow={start:"end",end:"start"};function pu(n,o,l){return Et(n,Hn(o,l))}function hn(n,o){return typeof n=="function"?n(o):n}function gn(n){return n.split("-")[0]}function io(n){return n.split("-")[1]}function Lu(n){return n==="x"?"y":"x"}function Mu(n){return n==="y"?"height":"width"}function Jt(n){return["top","bottom"].includes(gn(n))?"y":"x"}function Iu(n){return Lu(Jt(n))}function Dw(n,o,l){l===void 0&&(l=!1);const s=io(n),a=Iu(n),c=Mu(a);let p=a==="x"?s===(l?"end":"start")?"right":"left":s==="start"?"bottom":"top";return o.reference[c]>o.floating[c]&&(p=Mi(p)),[p,Mi(p)]}function $w(n){const o=Mi(n);return[mu(n),o,mu(o)]}function mu(n){return n.replace(/start|end/g,o=>Ow[o])}function Fw(n,o,l){const s=["left","right"],a=["right","left"],c=["top","bottom"],p=["bottom","top"];switch(n){case"top":case"bottom":return l?o?a:s:o?s:a;case"left":case"right":return o?c:p;default:return[]}}function Ww(n,o,l,s){const a=io(n);let c=Fw(gn(n),l==="start",s);return a&&(c=c.map(p=>p+"-"+a),o&&(c=c.concat(c.map(mu)))),c}function Mi(n){return n.replace(/left|right|bottom|top/g,o=>Iw[o])}function Hw(n){return{top:0,right:0,bottom:0,left:0,...n}}function Dm(n){return typeof n!="number"?Hw(n):{top:n,right:n,bottom:n,left:n}}function Ii(n){const{x:o,y:l,width:s,height:a}=n;return{width:s,height:a,top:l,left:o,right:o+s,bottom:l+a,x:o,y:l}}function hp(n,o,l){let{reference:s,floating:a}=n;const c=Jt(o),p=Iu(o),f=Mu(p),m=gn(o),g=c==="y",w=s.x+s.width/2-a.width/2,x=s.y+s.height/2-a.height/2,S=s[f]/2-a[f]/2;let E;switch(m){case"top":E={x:w,y:s.y-a.height};break;case"bottom":E={x:w,y:s.y+s.height};break;case"right":E={x:s.x+s.width,y:x};break;case"left":E={x:s.x-a.width,y:x};break;default:E={x:s.x,y:s.y}}switch(io(o)){case"start":E[p]-=S*(l&&g?-1:1);break;case"end":E[p]+=S*(l&&g?-1:1);break}return E}const Vw=async(n,o,l)=>{const{placement:s="bottom",strategy:a="absolute",middleware:c=[],platform:p}=l,f=c.filter(Boolean),m=await(p.isRTL==null?void 0:p.isRTL(o));let g=await p.getElementRects({reference:n,floating:o,strategy:a}),{x:w,y:x}=hp(g,s,m),S=s,E={},b=0;for(let C=0;C<f.length;C++){const{name:P,fn:N}=f[C],{x:j,y:z,data:M,reset:D}=await N({x:w,y:x,initialPlacement:s,placement:S,strategy:a,middlewareData:E,rects:g,platform:p,elements:{reference:n,floating:o}});w=j??w,x=z??x,E={...E,[P]:{...E[P],...M}},D&&b<=50&&(b++,typeof D=="object"&&(D.placement&&(S=D.placement),D.rects&&(g=D.rects===!0?await p.getElementRects({reference:n,floating:o,strategy:a}):D.rects),{x:w,y:x}=hp(g,S,m)),C=-1)}return{x:w,y:x,placement:S,strategy:a,middlewareData:E}};async function rl(n,o){var l;o===void 0&&(o={});const{x:s,y:a,platform:c,rects:p,elements:f,strategy:m}=n,{boundary:g="clippingAncestors",rootBoundary:w="viewport",elementContext:x="floating",altBoundary:S=!1,padding:E=0}=hn(o,n),b=Dm(E),P=f[S?x==="floating"?"reference":"floating":x],N=Ii(await c.getClippingRect({element:(l=await(c.isElement==null?void 0:c.isElement(P)))==null||l?P:P.contextElement||await(c.getDocumentElement==null?void 0:c.getDocumentElement(f.floating)),boundary:g,rootBoundary:w,strategy:m})),j=x==="floating"?{x:s,y:a,width:p.floating.width,height:p.floating.height}:p.reference,z=await(c.getOffsetParent==null?void 0:c.getOffsetParent(f.floating)),M=await(c.isElement==null?void 0:c.isElement(z))?await(c.getScale==null?void 0:c.getScale(z))||{x:1,y:1}:{x:1,y:1},D=Ii(c.convertOffsetParentRelativeRectToViewportRelativeRect?await c.convertOffsetParentRelativeRectToViewportRelativeRect({elements:f,rect:j,offsetParent:z,strategy:m}):j);return{top:(N.top-D.top+b.top)/M.y,bottom:(D.bottom-N.bottom+b.bottom)/M.y,left:(N.left-D.left+b.left)/M.x,right:(D.right-N.right+b.right)/M.x}}const Bw=n=>({name:"arrow",options:n,async fn(o){const{x:l,y:s,placement:a,rects:c,platform:p,elements:f,middlewareData:m}=o,{element:g,padding:w=0}=hn(n,o)||{};if(g==null)return{};const x=Dm(w),S={x:l,y:s},E=Iu(a),b=Mu(E),C=await p.getDimensions(g),P=E==="y",N=P?"top":"left",j=P?"bottom":"right",z=P?"clientHeight":"clientWidth",M=c.reference[b]+c.reference[E]-S[E]-c.floating[b],D=S[E]-c.reference[E],V=await(p.getOffsetParent==null?void 0:p.getOffsetParent(g));let O=V?V[z]:0;(!O||!await(p.isElement==null?void 0:p.isElement(V)))&&(O=f.floating[z]||c.floating[b]);const ee=M/2-D/2,re=O/2-C[b]/2-1,ae=Hn(x[N],re),pe=Hn(x[j],re),q=ae,ve=O-C[b]-pe,se=O/2-C[b]/2+ee,ge=pu(q,se,ve),le=!m.arrow&&io(a)!=null&&se!==ge&&c.reference[b]/2-(se<q?ae:pe)-C[b]/2<0,ce=le?se<q?se-q:se-ve:0;return{[E]:S[E]+ce,data:{[E]:ge,centerOffset:se-ge-ce,...le&&{alignmentOffset:ce}},reset:le}}}),Uw=function(n){return n===void 0&&(n={}),{name:"flip",options:n,async fn(o){var l,s;const{placement:a,middlewareData:c,rects:p,initialPlacement:f,platform:m,elements:g}=o,{mainAxis:w=!0,crossAxis:x=!0,fallbackPlacements:S,fallbackStrategy:E="bestFit",fallbackAxisSideDirection:b="none",flipAlignment:C=!0,...P}=hn(n,o);if((l=c.arrow)!=null&&l.alignmentOffset)return{};const N=gn(a),j=Jt(f),z=gn(f)===f,M=await(m.isRTL==null?void 0:m.isRTL(g.floating)),D=S||(z||!C?[Mi(f)]:$w(f)),V=b!=="none";!S&&V&&D.push(...Ww(f,C,b,M));const O=[f,...D],ee=await rl(o,P),re=[];let ae=((s=c.flip)==null?void 0:s.overflows)||[];if(w&&re.push(ee[N]),x){const se=Dw(a,p,M);re.push(ee[se[0]],ee[se[1]])}if(ae=[...ae,{placement:a,overflows:re}],!re.every(se=>se<=0)){var pe,q;const se=(((pe=c.flip)==null?void 0:pe.index)||0)+1,ge=O[se];if(ge&&(!(x==="alignment"?j!==Jt(ge):!1)||ae.every($=>$.overflows[0]>0&&Jt($.placement)===j)))return{data:{index:se,overflows:ae},reset:{placement:ge}};let le=(q=ae.filter(ce=>ce.overflows[0]<=0).sort((ce,$)=>ce.overflows[1]-$.overflows[1])[0])==null?void 0:q.placement;if(!le)switch(E){case"bestFit":{var ve;const ce=(ve=ae.filter($=>{if(V){const J=Jt($.placement);return J===j||J==="y"}return!0}).map($=>[$.placement,$.overflows.filter(J=>J>0).reduce((J,Y)=>J+Y,0)]).sort(($,J)=>$[1]-J[1])[0])==null?void 0:ve[0];ce&&(le=ce);break}case"initialPlacement":le=f;break}if(a!==le)return{reset:{placement:le}}}return{}}}};function gp(n,o){return{top:n.top-o.height,right:n.right-o.width,bottom:n.bottom-o.height,left:n.left-o.width}}function vp(n){return Mw.some(o=>n[o]>=0)}const Kw=function(n){return n===void 0&&(n={}),{name:"hide",options:n,async fn(o){const{rects:l}=o,{strategy:s="referenceHidden",...a}=hn(n,o);switch(s){case"referenceHidden":{const c=await rl(o,{...a,elementContext:"reference"}),p=gp(c,l.reference);return{data:{referenceHiddenOffsets:p,referenceHidden:vp(p)}}}case"escaped":{const c=await rl(o,{...a,altBoundary:!0}),p=gp(c,l.floating);return{data:{escapedOffsets:p,escaped:vp(p)}}}default:return{}}}}};async function Qw(n,o){const{placement:l,platform:s,elements:a}=n,c=await(s.isRTL==null?void 0:s.isRTL(a.floating)),p=gn(l),f=io(l),m=Jt(l)==="y",g=["left","top"].includes(p)?-1:1,w=c&&m?-1:1,x=hn(o,n);let{mainAxis:S,crossAxis:E,alignmentAxis:b}=typeof x=="number"?{mainAxis:x,crossAxis:0,alignmentAxis:null}:{mainAxis:x.mainAxis||0,crossAxis:x.crossAxis||0,alignmentAxis:x.alignmentAxis};return f&&typeof b=="number"&&(E=f==="end"?b*-1:b),m?{x:E*w,y:S*g}:{x:S*g,y:E*w}}const Gw=function(n){return n===void 0&&(n=0),{name:"offset",options:n,async fn(o){var l,s;const{x:a,y:c,placement:p,middlewareData:f}=o,m=await Qw(o,n);return p===((l=f.offset)==null?void 0:l.placement)&&(s=f.arrow)!=null&&s.alignmentOffset?{}:{x:a+m.x,y:c+m.y,data:{...m,placement:p}}}}},Yw=function(n){return n===void 0&&(n={}),{name:"shift",options:n,async fn(o){const{x:l,y:s,placement:a}=o,{mainAxis:c=!0,crossAxis:p=!1,limiter:f={fn:P=>{let{x:N,y:j}=P;return{x:N,y:j}}},...m}=hn(n,o),g={x:l,y:s},w=await rl(o,m),x=Jt(gn(a)),S=Lu(x);let E=g[S],b=g[x];if(c){const P=S==="y"?"top":"left",N=S==="y"?"bottom":"right",j=E+w[P],z=E-w[N];E=pu(j,E,z)}if(p){const P=x==="y"?"top":"left",N=x==="y"?"bottom":"right",j=b+w[P],z=b-w[N];b=pu(j,b,z)}const C=f.fn({...o,[S]:E,[x]:b});return{...C,data:{x:C.x-l,y:C.y-s,enabled:{[S]:c,[x]:p}}}}}},Xw=function(n){return n===void 0&&(n={}),{options:n,fn(o){const{x:l,y:s,placement:a,rects:c,middlewareData:p}=o,{offset:f=0,mainAxis:m=!0,crossAxis:g=!0}=hn(n,o),w={x:l,y:s},x=Jt(a),S=Lu(x);let E=w[S],b=w[x];const C=hn(f,o),P=typeof C=="number"?{mainAxis:C,crossAxis:0}:{mainAxis:0,crossAxis:0,...C};if(m){const z=S==="y"?"height":"width",M=c.reference[S]-c.floating[z]+P.mainAxis,D=c.reference[S]+c.reference[z]-P.mainAxis;E<M?E=M:E>D&&(E=D)}if(g){var N,j;const z=S==="y"?"width":"height",M=["top","left"].includes(gn(a)),D=c.reference[x]-c.floating[z]+(M&&((N=p.offset)==null?void 0:N[x])||0)+(M?0:P.crossAxis),V=c.reference[x]+c.reference[z]+(M?0:((j=p.offset)==null?void 0:j[x])||0)-(M?P.crossAxis:0);b<D?b=D:b>V&&(b=V)}return{[S]:E,[x]:b}}}},Zw=function(n){return n===void 0&&(n={}),{name:"size",options:n,async fn(o){var l,s;const{placement:a,rects:c,platform:p,elements:f}=o,{apply:m=()=>{},...g}=hn(n,o),w=await rl(o,g),x=gn(a),S=io(a),E=Jt(a)==="y",{width:b,height:C}=c.floating;let P,N;x==="top"||x==="bottom"?(P=x,N=S===(await(p.isRTL==null?void 0:p.isRTL(f.floating))?"start":"end")?"left":"right"):(N=x,P=S==="end"?"top":"bottom");const j=C-w.top-w.bottom,z=b-w.left-w.right,M=Hn(C-w[P],j),D=Hn(b-w[N],z),V=!o.middlewareData.shift;let O=M,ee=D;if((l=o.middlewareData.shift)!=null&&l.enabled.x&&(ee=z),(s=o.middlewareData.shift)!=null&&s.enabled.y&&(O=j),V&&!S){const ae=Et(w.left,0),pe=Et(w.right,0),q=Et(w.top,0),ve=Et(w.bottom,0);E?ee=b-2*(ae!==0||pe!==0?ae+pe:Et(w.left,w.right)):O=C-2*(q!==0||ve!==0?q+ve:Et(w.top,w.bottom))}await m({...o,availableWidth:ee,availableHeight:O});const re=await p.getDimensions(f.floating);return b!==re.width||C!==re.height?{reset:{rects:!0}}:{}}}};function Ui(){return typeof window<"u"}function so(n){return $m(n)?(n.nodeName||"").toLowerCase():"#document"}function bt(n){var o;return(n==null||(o=n.ownerDocument)==null?void 0:o.defaultView)||window}function rn(n){var o;return(o=($m(n)?n.ownerDocument:n.document)||window.document)==null?void 0:o.documentElement}function $m(n){return Ui()?n instanceof Node||n instanceof bt(n).Node:!1}function Ut(n){return Ui()?n instanceof Element||n instanceof bt(n).Element:!1}function tn(n){return Ui()?n instanceof HTMLElement||n instanceof bt(n).HTMLElement:!1}function yp(n){return!Ui()||typeof ShadowRoot>"u"?!1:n instanceof ShadowRoot||n instanceof bt(n).ShadowRoot}function cl(n){const{overflow:o,overflowX:l,overflowY:s,display:a}=Kt(n);return/auto|scroll|overlay|hidden|clip/.test(o+s+l)&&!["inline","contents"].includes(a)}function qw(n){return["table","td","th"].includes(so(n))}function Ki(n){return[":popover-open",":modal"].some(o=>{try{return n.matches(o)}catch{return!1}})}function Ou(n){const o=Du(),l=Ut(n)?Kt(n):n;return["transform","translate","scale","rotate","perspective"].some(s=>l[s]?l[s]!=="none":!1)||(l.containerType?l.containerType!=="normal":!1)||!o&&(l.backdropFilter?l.backdropFilter!=="none":!1)||!o&&(l.filter?l.filter!=="none":!1)||["transform","translate","scale","rotate","perspective","filter"].some(s=>(l.willChange||"").includes(s))||["paint","layout","strict","content"].some(s=>(l.contain||"").includes(s))}function Jw(n){let o=Vn(n);for(;tn(o)&&!no(o);){if(Ou(o))return o;if(Ki(o))return null;o=Vn(o)}return null}function Du(){return typeof CSS>"u"||!CSS.supports?!1:CSS.supports("-webkit-backdrop-filter","none")}function no(n){return["html","body","#document"].includes(so(n))}function Kt(n){return bt(n).getComputedStyle(n)}function Qi(n){return Ut(n)?{scrollLeft:n.scrollLeft,scrollTop:n.scrollTop}:{scrollLeft:n.scrollX,scrollTop:n.scrollY}}function Vn(n){if(so(n)==="html")return n;const o=n.assignedSlot||n.parentNode||yp(n)&&n.host||rn(n);return yp(o)?o.host:o}function Fm(n){const o=Vn(n);return no(o)?n.ownerDocument?n.ownerDocument.body:n.body:tn(o)&&cl(o)?o:Fm(o)}function ol(n,o,l){var s;o===void 0&&(o=[]),l===void 0&&(l=!0);const a=Fm(n),c=a===((s=n.ownerDocument)==null?void 0:s.body),p=bt(a);if(c){const f=hu(p);return o.concat(p,p.visualViewport||[],cl(a)?a:[],f&&l?ol(f):[])}return o.concat(a,ol(a,[],l))}function hu(n){return n.parent&&Object.getPrototypeOf(n.parent)?n.frameElement:null}function Wm(n){const o=Kt(n);let l=parseFloat(o.width)||0,s=parseFloat(o.height)||0;const a=tn(n),c=a?n.offsetWidth:l,p=a?n.offsetHeight:s,f=Li(l)!==c||Li(s)!==p;return f&&(l=c,s=p),{width:l,height:s,$:f}}function $u(n){return Ut(n)?n:n.contextElement}function Zr(n){const o=$u(n);if(!tn(o))return en(1);const l=o.getBoundingClientRect(),{width:s,height:a,$:c}=Wm(o);let p=(c?Li(l.width):l.width)/s,f=(c?Li(l.height):l.height)/a;return(!p||!Number.isFinite(p))&&(p=1),(!f||!Number.isFinite(f))&&(f=1),{x:p,y:f}}const eS=en(0);function Hm(n){const o=bt(n);return!Du()||!o.visualViewport?eS:{x:o.visualViewport.offsetLeft,y:o.visualViewport.offsetTop}}function tS(n,o,l){return o===void 0&&(o=!1),!l||o&&l!==bt(n)?!1:o}function mr(n,o,l,s){o===void 0&&(o=!1),l===void 0&&(l=!1);const a=n.getBoundingClientRect(),c=$u(n);let p=en(1);o&&(s?Ut(s)&&(p=Zr(s)):p=Zr(n));const f=tS(c,l,s)?Hm(c):en(0);let m=(a.left+f.x)/p.x,g=(a.top+f.y)/p.y,w=a.width/p.x,x=a.height/p.y;if(c){const S=bt(c),E=s&&Ut(s)?bt(s):s;let b=S,C=hu(b);for(;C&&s&&E!==b;){const P=Zr(C),N=C.getBoundingClientRect(),j=Kt(C),z=N.left+(C.clientLeft+parseFloat(j.paddingLeft))*P.x,M=N.top+(C.clientTop+parseFloat(j.paddingTop))*P.y;m*=P.x,g*=P.y,w*=P.x,x*=P.y,m+=z,g+=M,b=bt(C),C=hu(b)}}return Ii({width:w,height:x,x:m,y:g})}function Fu(n,o){const l=Qi(n).scrollLeft;return o?o.left+l:mr(rn(n)).left+l}function Vm(n,o,l){l===void 0&&(l=!1);const s=n.getBoundingClientRect(),a=s.left+o.scrollLeft-(l?0:Fu(n,s)),c=s.top+o.scrollTop;return{x:a,y:c}}function nS(n){let{elements:o,rect:l,offsetParent:s,strategy:a}=n;const c=a==="fixed",p=rn(s),f=o?Ki(o.floating):!1;if(s===p||f&&c)return l;let m={scrollLeft:0,scrollTop:0},g=en(1);const w=en(0),x=tn(s);if((x||!x&&!c)&&((so(s)!=="body"||cl(p))&&(m=Qi(s)),tn(s))){const E=mr(s);g=Zr(s),w.x=E.x+s.clientLeft,w.y=E.y+s.clientTop}const S=p&&!x&&!c?Vm(p,m,!0):en(0);return{width:l.width*g.x,height:l.height*g.y,x:l.x*g.x-m.scrollLeft*g.x+w.x+S.x,y:l.y*g.y-m.scrollTop*g.y+w.y+S.y}}function rS(n){return Array.from(n.getClientRects())}function oS(n){const o=rn(n),l=Qi(n),s=n.ownerDocument.body,a=Et(o.scrollWidth,o.clientWidth,s.scrollWidth,s.clientWidth),c=Et(o.scrollHeight,o.clientHeight,s.scrollHeight,s.clientHeight);let p=-l.scrollLeft+Fu(n);const f=-l.scrollTop;return Kt(s).direction==="rtl"&&(p+=Et(o.clientWidth,s.clientWidth)-a),{width:a,height:c,x:p,y:f}}function lS(n,o){const l=bt(n),s=rn(n),a=l.visualViewport;let c=s.clientWidth,p=s.clientHeight,f=0,m=0;if(a){c=a.width,p=a.height;const g=Du();(!g||g&&o==="fixed")&&(f=a.offsetLeft,m=a.offsetTop)}return{width:c,height:p,x:f,y:m}}function iS(n,o){const l=mr(n,!0,o==="fixed"),s=l.top+n.clientTop,a=l.left+n.clientLeft,c=tn(n)?Zr(n):en(1),p=n.clientWidth*c.x,f=n.clientHeight*c.y,m=a*c.x,g=s*c.y;return{width:p,height:f,x:m,y:g}}function xp(n,o,l){let s;if(o==="viewport")s=lS(n,l);else if(o==="document")s=oS(rn(n));else if(Ut(o))s=iS(o,l);else{const a=Hm(n);s={x:o.x-a.x,y:o.y-a.y,width:o.width,height:o.height}}return Ii(s)}function Bm(n,o){const l=Vn(n);return l===o||!Ut(l)||no(l)?!1:Kt(l).position==="fixed"||Bm(l,o)}function sS(n,o){const l=o.get(n);if(l)return l;let s=ol(n,[],!1).filter(f=>Ut(f)&&so(f)!=="body"),a=null;const c=Kt(n).position==="fixed";let p=c?Vn(n):n;for(;Ut(p)&&!no(p);){const f=Kt(p),m=Ou(p);!m&&f.position==="fixed"&&(a=null),(c?!m&&!a:!m&&f.position==="static"&&!!a&&["absolute","fixed"].includes(a.position)||cl(p)&&!m&&Bm(n,p))?s=s.filter(w=>w!==p):a=f,p=Vn(p)}return o.set(n,s),s}function aS(n){let{element:o,boundary:l,rootBoundary:s,strategy:a}=n;const p=[...l==="clippingAncestors"?Ki(o)?[]:sS(o,this._c):[].concat(l),s],f=p[0],m=p.reduce((g,w)=>{const x=xp(o,w,a);return g.top=Et(x.top,g.top),g.right=Hn(x.right,g.right),g.bottom=Hn(x.bottom,g.bottom),g.left=Et(x.left,g.left),g},xp(o,f,a));return{width:m.right-m.left,height:m.bottom-m.top,x:m.left,y:m.top}}function uS(n){const{width:o,height:l}=Wm(n);return{width:o,height:l}}function cS(n,o,l){const s=tn(o),a=rn(o),c=l==="fixed",p=mr(n,!0,c,o);let f={scrollLeft:0,scrollTop:0};const m=en(0);function g(){m.x=Fu(a)}if(s||!s&&!c)if((so(o)!=="body"||cl(a))&&(f=Qi(o)),s){const E=mr(o,!0,c,o);m.x=E.x+o.clientLeft,m.y=E.y+o.clientTop}else a&&g();c&&!s&&a&&g();const w=a&&!s&&!c?Vm(a,f):en(0),x=p.left+f.scrollLeft-m.x-w.x,S=p.top+f.scrollTop-m.y-w.y;return{x,y:S,width:p.width,height:p.height}}function eu(n){return Kt(n).position==="static"}function wp(n,o){if(!tn(n)||Kt(n).position==="fixed")return null;if(o)return o(n);let l=n.offsetParent;return rn(n)===l&&(l=l.ownerDocument.body),l}function Um(n,o){const l=bt(n);if(Ki(n))return l;if(!tn(n)){let a=Vn(n);for(;a&&!no(a);){if(Ut(a)&&!eu(a))return a;a=Vn(a)}return l}let s=wp(n,o);for(;s&&qw(s)&&eu(s);)s=wp(s,o);return s&&no(s)&&eu(s)&&!Ou(s)?l:s||Jw(n)||l}const dS=async function(n){const o=this.getOffsetParent||Um,l=this.getDimensions,s=await l(n.floating);return{reference:cS(n.reference,await o(n.floating),n.strategy),floating:{x:0,y:0,width:s.width,height:s.height}}};function fS(n){return Kt(n).direction==="rtl"}const pS={convertOffsetParentRelativeRectToViewportRelativeRect:nS,getDocumentElement:rn,getClippingRect:aS,getOffsetParent:Um,getElementRects:dS,getClientRects:rS,getDimensions:uS,getScale:Zr,isElement:Ut,isRTL:fS};function Km(n,o){return n.x===o.x&&n.y===o.y&&n.width===o.width&&n.height===o.height}function mS(n,o){let l=null,s;const a=rn(n);function c(){var f;clearTimeout(s),(f=l)==null||f.disconnect(),l=null}function p(f,m){f===void 0&&(f=!1),m===void 0&&(m=1),c();const g=n.getBoundingClientRect(),{left:w,top:x,width:S,height:E}=g;if(f||o(),!S||!E)return;const b=Ei(x),C=Ei(a.clientWidth-(w+S)),P=Ei(a.clientHeight-(x+E)),N=Ei(w),z={rootMargin:-b+"px "+-C+"px "+-P+"px "+-N+"px",threshold:Et(0,Hn(1,m))||1};let M=!0;function D(V){const O=V[0].intersectionRatio;if(O!==m){if(!M)return p();O?p(!1,O):s=setTimeout(()=>{p(!1,1e-7)},1e3)}O===1&&!Km(g,n.getBoundingClientRect())&&p(),M=!1}try{l=new IntersectionObserver(D,{...z,root:a.ownerDocument})}catch{l=new IntersectionObserver(D,z)}l.observe(n)}return p(!0),c}function hS(n,o,l,s){s===void 0&&(s={});const{ancestorScroll:a=!0,ancestorResize:c=!0,elementResize:p=typeof ResizeObserver=="function",layoutShift:f=typeof IntersectionObserver=="function",animationFrame:m=!1}=s,g=$u(n),w=a||c?[...g?ol(g):[],...ol(o)]:[];w.forEach(N=>{a&&N.addEventListener("scroll",l,{passive:!0}),c&&N.addEventListener("resize",l)});const x=g&&f?mS(g,l):null;let S=-1,E=null;p&&(E=new ResizeObserver(N=>{let[j]=N;j&&j.target===g&&E&&(E.unobserve(o),cancelAnimationFrame(S),S=requestAnimationFrame(()=>{var z;(z=E)==null||z.observe(o)})),l()}),g&&!m&&E.observe(g),E.observe(o));let b,C=m?mr(n):null;m&&P();function P(){const N=mr(n);C&&!Km(C,N)&&l(),C=N,b=requestAnimationFrame(P)}return l(),()=>{var N;w.forEach(j=>{a&&j.removeEventListener("scroll",l),c&&j.removeEventListener("resize",l)}),x==null||x(),(N=E)==null||N.disconnect(),E=null,m&&cancelAnimationFrame(b)}}const gS=Gw,vS=Yw,yS=Uw,xS=Zw,wS=Kw,Sp=Bw,SS=Xw,CS=(n,o,l)=>{const s=new Map,a={platform:pS,...l},c={...a.platform,_c:s};return Vw(n,o,{...a,platform:c})};var ES=typeof document<"u",bS=function(){},_i=ES?h.useLayoutEffect:bS;function Oi(n,o){if(n===o)return!0;if(typeof n!=typeof o)return!1;if(typeof n=="function"&&n.toString()===o.toString())return!0;let l,s,a;if(n&&o&&typeof n=="object"){if(Array.isArray(n)){if(l=n.length,l!==o.length)return!1;for(s=l;s--!==0;)if(!Oi(n[s],o[s]))return!1;return!0}if(a=Object.keys(n),l=a.length,l!==Object.keys(o).length)return!1;for(s=l;s--!==0;)if(!{}.hasOwnProperty.call(o,a[s]))return!1;for(s=l;s--!==0;){const c=a[s];if(!(c==="_owner"&&n.$$typeof)&&!Oi(n[c],o[c]))return!1}return!0}return n!==n&&o!==o}function Qm(n){return typeof window>"u"?1:(n.ownerDocument.defaultView||window).devicePixelRatio||1}function Cp(n,o){const l=Qm(n);return Math.round(o*l)/l}function tu(n){const o=h.useRef(n);return _i(()=>{o.current=n}),o}function kS(n){n===void 0&&(n={});const{placement:o="bottom",strategy:l="absolute",middleware:s=[],platform:a,elements:{reference:c,floating:p}={},transform:f=!0,whileElementsMounted:m,open:g}=n,[w,x]=h.useState({x:0,y:0,strategy:l,placement:o,middlewareData:{},isPositioned:!1}),[S,E]=h.useState(s);Oi(S,s)||E(s);const[b,C]=h.useState(null),[P,N]=h.useState(null),j=h.useCallback($=>{$!==V.current&&(V.current=$,C($))},[]),z=h.useCallback($=>{$!==O.current&&(O.current=$,N($))},[]),M=c||b,D=p||P,V=h.useRef(null),O=h.useRef(null),ee=h.useRef(w),re=m!=null,ae=tu(m),pe=tu(a),q=tu(g),ve=h.useCallback(()=>{if(!V.current||!O.current)return;const $={placement:o,strategy:l,middleware:S};pe.current&&($.platform=pe.current),CS(V.current,O.current,$).then(J=>{const Y={...J,isPositioned:q.current!==!1};se.current&&!Oi(ee.current,Y)&&(ee.current=Y,sl.flushSync(()=>{x(Y)}))})},[S,o,l,pe,q]);_i(()=>{g===!1&&ee.current.isPositioned&&(ee.current.isPositioned=!1,x($=>({...$,isPositioned:!1})))},[g]);const se=h.useRef(!1);_i(()=>(se.current=!0,()=>{se.current=!1}),[]),_i(()=>{if(M&&(V.current=M),D&&(O.current=D),M&&D){if(ae.current)return ae.current(M,D,ve);ve()}},[M,D,ve,ae,re]);const ge=h.useMemo(()=>({reference:V,floating:O,setReference:j,setFloating:z}),[j,z]),le=h.useMemo(()=>({reference:M,floating:D}),[M,D]),ce=h.useMemo(()=>{const $={position:l,left:0,top:0};if(!le.floating)return $;const J=Cp(le.floating,w.x),Y=Cp(le.floating,w.y);return f?{...$,transform:"translate("+J+"px, "+Y+"px)",...Qm(le.floating)>=1.5&&{willChange:"transform"}}:{position:l,left:J,top:Y}},[l,f,le.floating,w.x,w.y]);return h.useMemo(()=>({...w,update:ve,refs:ge,elements:le,floatingStyles:ce}),[w,ve,ge,le,ce])}const PS=n=>{function o(l){return{}.hasOwnProperty.call(l,"current")}return{name:"arrow",options:n,fn(l){const{element:s,padding:a}=typeof n=="function"?n(l):n;return s&&o(s)?s.current!=null?Sp({element:s.current,padding:a}).fn(l):{}:s?Sp({element:s,padding:a}).fn(l):{}}}},NS=(n,o)=>({...gS(n),options:[n,o]}),RS=(n,o)=>({...vS(n),options:[n,o]}),_S=(n,o)=>({...SS(n),options:[n,o]}),TS=(n,o)=>({...yS(n),options:[n,o]}),jS=(n,o)=>({...xS(n),options:[n,o]}),zS=(n,o)=>({...wS(n),options:[n,o]}),AS=(n,o)=>({...PS(n),options:[n,o]});var LS="Arrow",Gm=h.forwardRef((n,o)=>{const{children:l,width:s=10,height:a=5,...c}=n;return v.jsx(Be.svg,{...c,ref:o,width:s,height:a,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:n.asChild?l:v.jsx("polygon",{points:"0,0 30,0 15,10"})})});Gm.displayName=LS;var MS=Gm,At=globalThis!=null&&globalThis.document?h.useLayoutEffect:()=>{};function IS(n){const[o,l]=h.useState(void 0);return At(()=>{if(n){l({width:n.offsetWidth,height:n.offsetHeight});const s=new ResizeObserver(a=>{if(!Array.isArray(a)||!a.length)return;const c=a[0];let p,f;if("borderBoxSize"in c){const m=c.borderBoxSize,g=Array.isArray(m)?m[0]:m;p=g.inlineSize,f=g.blockSize}else p=n.offsetWidth,f=n.offsetHeight;l({width:p,height:f})});return s.observe(n,{box:"border-box"}),()=>s.unobserve(n)}else l(void 0)},[n]),o}var Wu="Popper",[Ym,Xm]=zu(Wu),[OS,Zm]=Ym(Wu),qm=n=>{const{__scopePopper:o,children:l}=n,[s,a]=h.useState(null);return v.jsx(OS,{scope:o,anchor:s,onAnchorChange:a,children:l})};qm.displayName=Wu;var Jm="PopperAnchor",eh=h.forwardRef((n,o)=>{const{__scopePopper:l,virtualRef:s,...a}=n,c=Zm(Jm,l),p=h.useRef(null),f=Je(o,p);return h.useEffect(()=>{c.onAnchorChange((s==null?void 0:s.current)||p.current)}),s?null:v.jsx(Be.div,{...a,ref:f})});eh.displayName=Jm;var Hu="PopperContent",[DS,$S]=Ym(Hu),th=h.forwardRef((n,o)=>{var F,X,K,oe,me,ye;const{__scopePopper:l,side:s="bottom",sideOffset:a=0,align:c="center",alignOffset:p=0,arrowPadding:f=0,avoidCollisions:m=!0,collisionBoundary:g=[],collisionPadding:w=0,sticky:x="partial",hideWhenDetached:S=!1,updatePositionStrategy:E="optimized",onPlaced:b,...C}=n,P=Zm(Hu,l),[N,j]=h.useState(null),z=Je(o,Se=>j(Se)),[M,D]=h.useState(null),V=IS(M),O=(V==null?void 0:V.width)??0,ee=(V==null?void 0:V.height)??0,re=s+(c!=="center"?"-"+c:""),ae=typeof w=="number"?w:{top:0,right:0,bottom:0,left:0,...w},pe=Array.isArray(g)?g:[g],q=pe.length>0,ve={padding:ae,boundary:pe.filter(WS),altBoundary:q},{refs:se,floatingStyles:ge,placement:le,isPositioned:ce,middlewareData:$}=kS({strategy:"fixed",placement:re,whileElementsMounted:(...Se)=>hS(...Se,{animationFrame:E==="always"}),elements:{reference:P.anchor},middleware:[NS({mainAxis:a+ee,alignmentAxis:p}),m&&RS({mainAxis:!0,crossAxis:!1,limiter:x==="partial"?_S():void 0,...ve}),m&&TS({...ve}),jS({...ve,apply:({elements:Se,rects:_e,availableWidth:st,availableHeight:gt})=>{const{width:vt,height:Ue}=_e.reference,Mt=Se.floating.style;Mt.setProperty("--radix-popper-available-width",`${st}px`),Mt.setProperty("--radix-popper-available-height",`${gt}px`),Mt.setProperty("--radix-popper-anchor-width",`${vt}px`),Mt.setProperty("--radix-popper-anchor-height",`${Ue}px`)}}),M&&AS({element:M,padding:f}),HS({arrowWidth:O,arrowHeight:ee}),S&&zS({strategy:"referenceHidden",...ve})]}),[J,Y]=oh(le),_=pr(b);At(()=>{ce&&(_==null||_())},[ce,_]);const W=(F=$.arrow)==null?void 0:F.x,H=(X=$.arrow)==null?void 0:X.y,G=((K=$.arrow)==null?void 0:K.centerOffset)!==0,[ne,te]=h.useState();return At(()=>{N&&te(window.getComputedStyle(N).zIndex)},[N]),v.jsx("div",{ref:se.setFloating,"data-radix-popper-content-wrapper":"",style:{...ge,transform:ce?ge.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:ne,"--radix-popper-transform-origin":[(oe=$.transformOrigin)==null?void 0:oe.x,(me=$.transformOrigin)==null?void 0:me.y].join(" "),...((ye=$.hide)==null?void 0:ye.referenceHidden)&&{visibility:"hidden",pointerEvents:"none"}},dir:n.dir,children:v.jsx(DS,{scope:l,placedSide:J,onArrowChange:D,arrowX:W,arrowY:H,shouldHideArrow:G,children:v.jsx(Be.div,{"data-side":J,"data-align":Y,...C,ref:z,style:{...C.style,animation:ce?void 0:"none"}})})})});th.displayName=Hu;var nh="PopperArrow",FS={top:"bottom",right:"left",bottom:"top",left:"right"},rh=h.forwardRef(function(o,l){const{__scopePopper:s,...a}=o,c=$S(nh,s),p=FS[c.placedSide];return v.jsx("span",{ref:c.onArrowChange,style:{position:"absolute",left:c.arrowX,top:c.arrowY,[p]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[c.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[c.placedSide],visibility:c.shouldHideArrow?"hidden":void 0},children:v.jsx(MS,{...a,ref:l,style:{...a.style,display:"block"}})})});rh.displayName=nh;function WS(n){return n!==null}var HS=n=>({name:"transformOrigin",options:n,fn(o){var P,N,j;const{placement:l,rects:s,middlewareData:a}=o,p=((P=a.arrow)==null?void 0:P.centerOffset)!==0,f=p?0:n.arrowWidth,m=p?0:n.arrowHeight,[g,w]=oh(l),x={start:"0%",center:"50%",end:"100%"}[w],S=(((N=a.arrow)==null?void 0:N.x)??0)+f/2,E=(((j=a.arrow)==null?void 0:j.y)??0)+m/2;let b="",C="";return g==="bottom"?(b=p?x:`${S}px`,C=`${-m}px`):g==="top"?(b=p?x:`${S}px`,C=`${s.floating.height+m}px`):g==="right"?(b=`${-m}px`,C=p?x:`${E}px`):g==="left"&&(b=`${s.floating.width+m}px`,C=p?x:`${E}px`),{data:{x:b,y:C}}}});function oh(n){const[o,l="center"]=n.split("-");return[o,l]}var VS=qm,BS=eh,US=th,KS=rh,QS="Portal",lh=h.forwardRef((n,o)=>{var f;const{container:l,...s}=n,[a,c]=h.useState(!1);At(()=>c(!0),[]);const p=l||a&&((f=globalThis==null?void 0:globalThis.document)==null?void 0:f.body);return p?vy.createPortal(v.jsx(Be.div,{...s,ref:o}),p):null});lh.displayName=QS;function ih(n){const o=h.useRef(n);return h.useEffect(()=>{o.current=n}),h.useMemo(()=>(...l)=>{var s;return(s=o.current)==null?void 0:s.call(o,...l)},[])}function Ep({prop:n,defaultProp:o,onChange:l=()=>{}}){const[s,a]=GS({defaultProp:o,onChange:l}),c=n!==void 0,p=c?n:s,f=ih(l),m=h.useCallback(g=>{if(c){const x=typeof g=="function"?g(n):g;x!==n&&f(x)}else a(g)},[c,n,a,f]);return[p,m]}function GS({defaultProp:n,onChange:o}){const l=h.useState(n),[s]=l,a=h.useRef(s),c=ih(o);return h.useEffect(()=>{a.current!==s&&(c(s),a.current=s)},[s,a,c]),l}function YS(n){const o=h.useRef({value:n,previous:n});return h.useMemo(()=>(o.current.value!==n&&(o.current.previous=o.current.value,o.current.value=n),o.current.previous),[n])}var XS="VisuallyHidden",sh=h.forwardRef((n,o)=>v.jsx(Be.span,{...n,ref:o,style:{position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal",...n.style}}));sh.displayName=XS;var ZS=function(n){if(typeof document>"u")return null;var o=Array.isArray(n)?n[0]:n;return o.ownerDocument.body},Hr=new WeakMap,bi=new WeakMap,ki={},nu=0,ah=function(n){return n&&(n.host||ah(n.parentNode))},qS=function(n,o){return o.map(function(l){if(n.contains(l))return l;var s=ah(l);return s&&n.contains(s)?s:(console.error("aria-hidden",l,"in not contained inside",n,". Doing nothing"),null)}).filter(function(l){return!!l})},JS=function(n,o,l,s){var a=qS(o,Array.isArray(n)?n:[n]);ki[l]||(ki[l]=new WeakMap);var c=ki[l],p=[],f=new Set,m=new Set(a),g=function(x){!x||f.has(x)||(f.add(x),g(x.parentNode))};a.forEach(g);var w=function(x){!x||m.has(x)||Array.prototype.forEach.call(x.children,function(S){if(f.has(S))w(S);else try{var E=S.getAttribute(s),b=E!==null&&E!=="false",C=(Hr.get(S)||0)+1,P=(c.get(S)||0)+1;Hr.set(S,C),c.set(S,P),p.push(S),C===1&&b&&bi.set(S,!0),P===1&&S.setAttribute(l,"true"),b||S.setAttribute(s,"true")}catch(N){console.error("aria-hidden: cannot operate on ",S,N)}})};return w(o),f.clear(),nu++,function(){p.forEach(function(x){var S=Hr.get(x)-1,E=c.get(x)-1;Hr.set(x,S),c.set(x,E),S||(bi.has(x)||x.removeAttribute(s),bi.delete(x)),E||x.removeAttribute(l)}),nu--,nu||(Hr=new WeakMap,Hr=new WeakMap,bi=new WeakMap,ki={})}},e1=function(n,o,l){l===void 0&&(l="data-aria-hidden");var s=Array.from(Array.isArray(n)?n:[n]),a=ZS(n);return a?(s.push.apply(s,Array.from(a.querySelectorAll("[aria-live], script"))),JS(s,a,l,"aria-hidden")):function(){return null}},qt=function(){return qt=Object.assign||function(o){for(var l,s=1,a=arguments.length;s<a;s++){l=arguments[s];for(var c in l)Object.prototype.hasOwnProperty.call(l,c)&&(o[c]=l[c])}return o},qt.apply(this,arguments)};function uh(n,o){var l={};for(var s in n)Object.prototype.hasOwnProperty.call(n,s)&&o.indexOf(s)<0&&(l[s]=n[s]);if(n!=null&&typeof Object.getOwnPropertySymbols=="function")for(var a=0,s=Object.getOwnPropertySymbols(n);a<s.length;a++)o.indexOf(s[a])<0&&Object.prototype.propertyIsEnumerable.call(n,s[a])&&(l[s[a]]=n[s[a]]);return l}function t1(n,o,l){if(l||arguments.length===2)for(var s=0,a=o.length,c;s<a;s++)(c||!(s in o))&&(c||(c=Array.prototype.slice.call(o,0,s)),c[s]=o[s]);return n.concat(c||Array.prototype.slice.call(o))}var Ti="right-scroll-bar-position",ji="width-before-scroll-bar",n1="with-scroll-bars-hidden",r1="--removed-body-scroll-bar-size";function ru(n,o){return typeof n=="function"?n(o):n&&(n.current=o),n}function o1(n,o){var l=h.useState(function(){return{value:n,callback:o,facade:{get current(){return l.value},set current(s){var a=l.value;a!==s&&(l.value=s,l.callback(s,a))}}}})[0];return l.callback=o,l.facade}var l1=typeof window<"u"?h.useLayoutEffect:h.useEffect,bp=new WeakMap;function i1(n,o){var l=o1(null,function(s){return n.forEach(function(a){return ru(a,s)})});return l1(function(){var s=bp.get(l);if(s){var a=new Set(s),c=new Set(n),p=l.current;a.forEach(function(f){c.has(f)||ru(f,null)}),c.forEach(function(f){a.has(f)||ru(f,p)})}bp.set(l,n)},[n]),l}function s1(n){return n}function a1(n,o){o===void 0&&(o=s1);var l=[],s=!1,a={read:function(){if(s)throw new Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return l.length?l[l.length-1]:n},useMedium:function(c){var p=o(c,s);return l.push(p),function(){l=l.filter(function(f){return f!==p})}},assignSyncMedium:function(c){for(s=!0;l.length;){var p=l;l=[],p.forEach(c)}l={push:function(f){return c(f)},filter:function(){return l}}},assignMedium:function(c){s=!0;var p=[];if(l.length){var f=l;l=[],f.forEach(c),p=l}var m=function(){var w=p;p=[],w.forEach(c)},g=function(){return Promise.resolve().then(m)};g(),l={push:function(w){p.push(w),g()},filter:function(w){return p=p.filter(w),l}}}};return a}function u1(n){n===void 0&&(n={});var o=a1(null);return o.options=qt({async:!0,ssr:!1},n),o}var ch=function(n){var o=n.sideCar,l=uh(n,["sideCar"]);if(!o)throw new Error("Sidecar: please provide `sideCar` property to import the right car");var s=o.read();if(!s)throw new Error("Sidecar medium not found");return h.createElement(s,qt({},l))};ch.isSideCarExport=!0;function c1(n,o){return n.useMedium(o),ch}var dh=u1(),ou=function(){},Gi=h.forwardRef(function(n,o){var l=h.useRef(null),s=h.useState({onScrollCapture:ou,onWheelCapture:ou,onTouchMoveCapture:ou}),a=s[0],c=s[1],p=n.forwardProps,f=n.children,m=n.className,g=n.removeScrollBar,w=n.enabled,x=n.shards,S=n.sideCar,E=n.noRelative,b=n.noIsolation,C=n.inert,P=n.allowPinchZoom,N=n.as,j=N===void 0?"div":N,z=n.gapMode,M=uh(n,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noRelative","noIsolation","inert","allowPinchZoom","as","gapMode"]),D=S,V=i1([l,o]),O=qt(qt({},M),a);return h.createElement(h.Fragment,null,w&&h.createElement(D,{sideCar:dh,removeScrollBar:g,shards:x,noRelative:E,noIsolation:b,inert:C,setCallbacks:c,allowPinchZoom:!!P,lockRef:l,gapMode:z}),p?h.cloneElement(h.Children.only(f),qt(qt({},O),{ref:V})):h.createElement(j,qt({},O,{className:m,ref:V}),f))});Gi.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1};Gi.classNames={fullWidth:ji,zeroRight:Ti};var d1=function(){if(typeof __webpack_nonce__<"u")return __webpack_nonce__};function f1(){if(!document)return null;var n=document.createElement("style");n.type="text/css";var o=d1();return o&&n.setAttribute("nonce",o),n}function p1(n,o){n.styleSheet?n.styleSheet.cssText=o:n.appendChild(document.createTextNode(o))}function m1(n){var o=document.head||document.getElementsByTagName("head")[0];o.appendChild(n)}var h1=function(){var n=0,o=null;return{add:function(l){n==0&&(o=f1())&&(p1(o,l),m1(o)),n++},remove:function(){n--,!n&&o&&(o.parentNode&&o.parentNode.removeChild(o),o=null)}}},g1=function(){var n=h1();return function(o,l){h.useEffect(function(){return n.add(o),function(){n.remove()}},[o&&l])}},fh=function(){var n=g1(),o=function(l){var s=l.styles,a=l.dynamic;return n(s,a),null};return o},v1={left:0,top:0,right:0,gap:0},lu=function(n){return parseInt(n||"",10)||0},y1=function(n){var o=window.getComputedStyle(document.body),l=o[n==="padding"?"paddingLeft":"marginLeft"],s=o[n==="padding"?"paddingTop":"marginTop"],a=o[n==="padding"?"paddingRight":"marginRight"];return[lu(l),lu(s),lu(a)]},x1=function(n){if(n===void 0&&(n="margin"),typeof window>"u")return v1;var o=y1(n),l=document.documentElement.clientWidth,s=window.innerWidth;return{left:o[0],top:o[1],right:o[2],gap:Math.max(0,s-l+o[2]-o[0])}},w1=fh(),qr="data-scroll-locked",S1=function(n,o,l,s){var a=n.left,c=n.top,p=n.right,f=n.gap;return l===void 0&&(l="margin"),`
  .`.concat(n1,` {
   overflow: hidden `).concat(s,`;
   padding-right: `).concat(f,"px ").concat(s,`;
  }
  body[`).concat(qr,`] {
    overflow: hidden `).concat(s,`;
    overscroll-behavior: contain;
    `).concat([o&&"position: relative ".concat(s,";"),l==="margin"&&`
    padding-left: `.concat(a,`px;
    padding-top: `).concat(c,`px;
    padding-right: `).concat(p,`px;
    margin-left:0;
    margin-top:0;
    margin-right: `).concat(f,"px ").concat(s,`;
    `),l==="padding"&&"padding-right: ".concat(f,"px ").concat(s,";")].filter(Boolean).join(""),`
  }
  
  .`).concat(Ti,` {
    right: `).concat(f,"px ").concat(s,`;
  }
  
  .`).concat(ji,` {
    margin-right: `).concat(f,"px ").concat(s,`;
  }
  
  .`).concat(Ti," .").concat(Ti,` {
    right: 0 `).concat(s,`;
  }
  
  .`).concat(ji," .").concat(ji,` {
    margin-right: 0 `).concat(s,`;
  }
  
  body[`).concat(qr,`] {
    `).concat(r1,": ").concat(f,`px;
  }
`)},kp=function(){var n=parseInt(document.body.getAttribute(qr)||"0",10);return isFinite(n)?n:0},C1=function(){h.useEffect(function(){return document.body.setAttribute(qr,(kp()+1).toString()),function(){var n=kp()-1;n<=0?document.body.removeAttribute(qr):document.body.setAttribute(qr,n.toString())}},[])},E1=function(n){var o=n.noRelative,l=n.noImportant,s=n.gapMode,a=s===void 0?"margin":s;C1();var c=h.useMemo(function(){return x1(a)},[a]);return h.createElement(w1,{styles:S1(c,!o,a,l?"":"!important")})},gu=!1;if(typeof window<"u")try{var Pi=Object.defineProperty({},"passive",{get:function(){return gu=!0,!0}});window.addEventListener("test",Pi,Pi),window.removeEventListener("test",Pi,Pi)}catch{gu=!1}var Vr=gu?{passive:!1}:!1,b1=function(n){return n.tagName==="TEXTAREA"},ph=function(n,o){if(!(n instanceof Element))return!1;var l=window.getComputedStyle(n);return l[o]!=="hidden"&&!(l.overflowY===l.overflowX&&!b1(n)&&l[o]==="visible")},k1=function(n){return ph(n,"overflowY")},P1=function(n){return ph(n,"overflowX")},Pp=function(n,o){var l=o.ownerDocument,s=o;do{typeof ShadowRoot<"u"&&s instanceof ShadowRoot&&(s=s.host);var a=mh(n,s);if(a){var c=hh(n,s),p=c[1],f=c[2];if(p>f)return!0}s=s.parentNode}while(s&&s!==l.body);return!1},N1=function(n){var o=n.scrollTop,l=n.scrollHeight,s=n.clientHeight;return[o,l,s]},R1=function(n){var o=n.scrollLeft,l=n.scrollWidth,s=n.clientWidth;return[o,l,s]},mh=function(n,o){return n==="v"?k1(o):P1(o)},hh=function(n,o){return n==="v"?N1(o):R1(o)},_1=function(n,o){return n==="h"&&o==="rtl"?-1:1},T1=function(n,o,l,s,a){var c=_1(n,window.getComputedStyle(o).direction),p=c*s,f=l.target,m=o.contains(f),g=!1,w=p>0,x=0,S=0;do{if(!f)break;var E=hh(n,f),b=E[0],C=E[1],P=E[2],N=C-P-c*b;(b||N)&&mh(n,f)&&(x+=N,S+=b);var j=f.parentNode;f=j&&j.nodeType===Node.DOCUMENT_FRAGMENT_NODE?j.host:j}while(!m&&f!==document.body||m&&(o.contains(f)||o===f));return(w&&Math.abs(x)<1||!w&&Math.abs(S)<1)&&(g=!0),g},Ni=function(n){return"changedTouches"in n?[n.changedTouches[0].clientX,n.changedTouches[0].clientY]:[0,0]},Np=function(n){return[n.deltaX,n.deltaY]},Rp=function(n){return n&&"current"in n?n.current:n},j1=function(n,o){return n[0]===o[0]&&n[1]===o[1]},z1=function(n){return`
  .block-interactivity-`.concat(n,` {pointer-events: none;}
  .allow-interactivity-`).concat(n,` {pointer-events: all;}
`)},A1=0,Br=[];function L1(n){var o=h.useRef([]),l=h.useRef([0,0]),s=h.useRef(),a=h.useState(A1++)[0],c=h.useState(fh)[0],p=h.useRef(n);h.useEffect(function(){p.current=n},[n]),h.useEffect(function(){if(n.inert){document.body.classList.add("block-interactivity-".concat(a));var C=t1([n.lockRef.current],(n.shards||[]).map(Rp),!0).filter(Boolean);return C.forEach(function(P){return P.classList.add("allow-interactivity-".concat(a))}),function(){document.body.classList.remove("block-interactivity-".concat(a)),C.forEach(function(P){return P.classList.remove("allow-interactivity-".concat(a))})}}},[n.inert,n.lockRef.current,n.shards]);var f=h.useCallback(function(C,P){if("touches"in C&&C.touches.length===2||C.type==="wheel"&&C.ctrlKey)return!p.current.allowPinchZoom;var N=Ni(C),j=l.current,z="deltaX"in C?C.deltaX:j[0]-N[0],M="deltaY"in C?C.deltaY:j[1]-N[1],D,V=C.target,O=Math.abs(z)>Math.abs(M)?"h":"v";if("touches"in C&&O==="h"&&V.type==="range")return!1;var ee=Pp(O,V);if(!ee)return!0;if(ee?D=O:(D=O==="v"?"h":"v",ee=Pp(O,V)),!ee)return!1;if(!s.current&&"changedTouches"in C&&(z||M)&&(s.current=D),!D)return!0;var re=s.current||D;return T1(re,P,C,re==="h"?z:M)},[]),m=h.useCallback(function(C){var P=C;if(!(!Br.length||Br[Br.length-1]!==c)){var N="deltaY"in P?Np(P):Ni(P),j=o.current.filter(function(D){return D.name===P.type&&(D.target===P.target||P.target===D.shadowParent)&&j1(D.delta,N)})[0];if(j&&j.should){P.cancelable&&P.preventDefault();return}if(!j){var z=(p.current.shards||[]).map(Rp).filter(Boolean).filter(function(D){return D.contains(P.target)}),M=z.length>0?f(P,z[0]):!p.current.noIsolation;M&&P.cancelable&&P.preventDefault()}}},[]),g=h.useCallback(function(C,P,N,j){var z={name:C,delta:P,target:N,should:j,shadowParent:M1(N)};o.current.push(z),setTimeout(function(){o.current=o.current.filter(function(M){return M!==z})},1)},[]),w=h.useCallback(function(C){l.current=Ni(C),s.current=void 0},[]),x=h.useCallback(function(C){g(C.type,Np(C),C.target,f(C,n.lockRef.current))},[]),S=h.useCallback(function(C){g(C.type,Ni(C),C.target,f(C,n.lockRef.current))},[]);h.useEffect(function(){return Br.push(c),n.setCallbacks({onScrollCapture:x,onWheelCapture:x,onTouchMoveCapture:S}),document.addEventListener("wheel",m,Vr),document.addEventListener("touchmove",m,Vr),document.addEventListener("touchstart",w,Vr),function(){Br=Br.filter(function(C){return C!==c}),document.removeEventListener("wheel",m,Vr),document.removeEventListener("touchmove",m,Vr),document.removeEventListener("touchstart",w,Vr)}},[]);var E=n.removeScrollBar,b=n.inert;return h.createElement(h.Fragment,null,b?h.createElement(c,{styles:z1(a)}):null,E?h.createElement(E1,{noRelative:n.noRelative,gapMode:n.gapMode}):null)}function M1(n){for(var o=null;n!==null;)n instanceof ShadowRoot&&(o=n.host,n=n.host),n=n.parentNode;return o}const I1=c1(dh,L1);var gh=h.forwardRef(function(n,o){return h.createElement(Gi,qt({},n,{ref:o,sideCar:I1}))});gh.classNames=Gi.classNames;var O1=[" ","Enter","ArrowUp","ArrowDown"],D1=[" ","Enter"],dl="Select",[Yi,Xi,$1]=dw(dl),[ao,oE]=zu(dl,[$1,Xm]),Zi=Xm(),[F1,Bn]=ao(dl),[W1,H1]=ao(dl),vh=n=>{const{__scopeSelect:o,children:l,open:s,defaultOpen:a,onOpenChange:c,value:p,defaultValue:f,onValueChange:m,dir:g,name:w,autoComplete:x,disabled:S,required:E,form:b}=n,C=Zi(o),[P,N]=h.useState(null),[j,z]=h.useState(null),[M,D]=h.useState(!1),V=pw(g),[O=!1,ee]=Ep({prop:s,defaultProp:a,onChange:c}),[re,ae]=Ep({prop:p,defaultProp:f,onChange:m}),pe=h.useRef(null),q=P?b||!!P.closest("form"):!0,[ve,se]=h.useState(new Set),ge=Array.from(ve).map(le=>le.props.value).join(";");return v.jsx(VS,{...C,children:v.jsxs(F1,{required:E,scope:o,trigger:P,onTriggerChange:N,valueNode:j,onValueNodeChange:z,valueNodeHasChildren:M,onValueNodeHasChildrenChange:D,contentId:Au(),value:re,onValueChange:ae,open:O,onOpenChange:ee,dir:V,triggerPointerDownPosRef:pe,disabled:S,children:[v.jsx(Yi.Provider,{scope:o,children:v.jsx(W1,{scope:n.__scopeSelect,onNativeOptionAdd:h.useCallback(le=>{se(ce=>new Set(ce).add(le))},[]),onNativeOptionRemove:h.useCallback(le=>{se(ce=>{const $=new Set(ce);return $.delete(le),$})},[]),children:l})}),q?v.jsxs(Fh,{"aria-hidden":!0,required:E,tabIndex:-1,name:w,autoComplete:x,value:re,onChange:le=>ae(le.target.value),disabled:S,form:b,children:[re===void 0?v.jsx("option",{value:""}):null,Array.from(ve)]},ge):null]})})};vh.displayName=dl;var yh="SelectTrigger",xh=h.forwardRef((n,o)=>{const{__scopeSelect:l,disabled:s=!1,...a}=n,c=Zi(l),p=Bn(yh,l),f=p.disabled||s,m=Je(o,p.onTriggerChange),g=Xi(l),w=h.useRef("touch"),[x,S,E]=Wh(C=>{const P=g().filter(z=>!z.disabled),N=P.find(z=>z.value===p.value),j=Hh(P,C,N);j!==void 0&&p.onValueChange(j.value)}),b=C=>{f||(p.onOpenChange(!0),E()),C&&(p.triggerPointerDownPosRef.current={x:Math.round(C.pageX),y:Math.round(C.pageY)})};return v.jsx(BS,{asChild:!0,...c,children:v.jsx(Be.button,{type:"button",role:"combobox","aria-controls":p.contentId,"aria-expanded":p.open,"aria-required":p.required,"aria-autocomplete":"none",dir:p.dir,"data-state":p.open?"open":"closed",disabled:f,"data-disabled":f?"":void 0,"data-placeholder":$h(p.value)?"":void 0,...a,ref:m,onClick:Ve(a.onClick,C=>{C.currentTarget.focus(),w.current!=="mouse"&&b(C)}),onPointerDown:Ve(a.onPointerDown,C=>{w.current=C.pointerType;const P=C.target;P.hasPointerCapture(C.pointerId)&&P.releasePointerCapture(C.pointerId),C.button===0&&C.ctrlKey===!1&&C.pointerType==="mouse"&&(b(C),C.preventDefault())}),onKeyDown:Ve(a.onKeyDown,C=>{const P=x.current!=="";!(C.ctrlKey||C.altKey||C.metaKey)&&C.key.length===1&&S(C.key),!(P&&C.key===" ")&&O1.includes(C.key)&&(b(),C.preventDefault())})})})});xh.displayName=yh;var wh="SelectValue",Sh=h.forwardRef((n,o)=>{const{__scopeSelect:l,className:s,style:a,children:c,placeholder:p="",...f}=n,m=Bn(wh,l),{onValueNodeHasChildrenChange:g}=m,w=c!==void 0,x=Je(o,m.onValueNodeChange);return At(()=>{g(w)},[g,w]),v.jsx(Be.span,{...f,ref:x,style:{pointerEvents:"none"},children:$h(m.value)?v.jsx(v.Fragment,{children:p}):c})});Sh.displayName=wh;var V1="SelectIcon",Ch=h.forwardRef((n,o)=>{const{__scopeSelect:l,children:s,...a}=n;return v.jsx(Be.span,{"aria-hidden":!0,...a,ref:o,children:s||"▼"})});Ch.displayName=V1;var B1="SelectPortal",Eh=n=>v.jsx(lh,{asChild:!0,...n});Eh.displayName=B1;var hr="SelectContent",bh=h.forwardRef((n,o)=>{const l=Bn(hr,n.__scopeSelect),[s,a]=h.useState();if(At(()=>{a(new DocumentFragment)},[]),!l.open){const c=s;return c?sl.createPortal(v.jsx(kh,{scope:n.__scopeSelect,children:v.jsx(Yi.Slot,{scope:n.__scopeSelect,children:v.jsx("div",{children:n.children})})}),c):null}return v.jsx(Ph,{...n,ref:o})});bh.displayName=hr;var Vt=10,[kh,Un]=ao(hr),U1="SelectContentImpl",Ph=h.forwardRef((n,o)=>{const{__scopeSelect:l,position:s="item-aligned",onCloseAutoFocus:a,onEscapeKeyDown:c,onPointerDownOutside:p,side:f,sideOffset:m,align:g,alignOffset:w,arrowPadding:x,collisionBoundary:S,collisionPadding:E,sticky:b,hideWhenDetached:C,avoidCollisions:P,...N}=n,j=Bn(hr,l),[z,M]=h.useState(null),[D,V]=h.useState(null),O=Je(o,F=>M(F)),[ee,re]=h.useState(null),[ae,pe]=h.useState(null),q=Xi(l),[ve,se]=h.useState(!1),ge=h.useRef(!1);h.useEffect(()=>{if(z)return e1(z)},[z]),bw();const le=h.useCallback(F=>{const[X,...K]=q().map(ye=>ye.ref.current),[oe]=K.slice(-1),me=document.activeElement;for(const ye of F)if(ye===me||(ye==null||ye.scrollIntoView({block:"nearest"}),ye===X&&D&&(D.scrollTop=0),ye===oe&&D&&(D.scrollTop=D.scrollHeight),ye==null||ye.focus(),document.activeElement!==me))return},[q,D]),ce=h.useCallback(()=>le([ee,z]),[le,ee,z]);h.useEffect(()=>{ve&&ce()},[ve,ce]);const{onOpenChange:$,triggerPointerDownPosRef:J}=j;h.useEffect(()=>{if(z){let F={x:0,y:0};const X=oe=>{var me,ye;F={x:Math.abs(Math.round(oe.pageX)-(((me=J.current)==null?void 0:me.x)??0)),y:Math.abs(Math.round(oe.pageY)-(((ye=J.current)==null?void 0:ye.y)??0))}},K=oe=>{F.x<=10&&F.y<=10?oe.preventDefault():z.contains(oe.target)||$(!1),document.removeEventListener("pointermove",X),J.current=null};return J.current!==null&&(document.addEventListener("pointermove",X),document.addEventListener("pointerup",K,{capture:!0,once:!0})),()=>{document.removeEventListener("pointermove",X),document.removeEventListener("pointerup",K,{capture:!0})}}},[z,$,J]),h.useEffect(()=>{const F=()=>$(!1);return window.addEventListener("blur",F),window.addEventListener("resize",F),()=>{window.removeEventListener("blur",F),window.removeEventListener("resize",F)}},[$]);const[Y,_]=Wh(F=>{const X=q().filter(me=>!me.disabled),K=X.find(me=>me.ref.current===document.activeElement),oe=Hh(X,F,K);oe&&setTimeout(()=>oe.ref.current.focus())}),W=h.useCallback((F,X,K)=>{const oe=!ge.current&&!K;(j.value!==void 0&&j.value===X||oe)&&(re(F),oe&&(ge.current=!0))},[j.value]),H=h.useCallback(()=>z==null?void 0:z.focus(),[z]),G=h.useCallback((F,X,K)=>{const oe=!ge.current&&!K;(j.value!==void 0&&j.value===X||oe)&&pe(F)},[j.value]),ne=s==="popper"?vu:Nh,te=ne===vu?{side:f,sideOffset:m,align:g,alignOffset:w,arrowPadding:x,collisionBoundary:S,collisionPadding:E,sticky:b,hideWhenDetached:C,avoidCollisions:P}:{};return v.jsx(kh,{scope:l,content:z,viewport:D,onViewportChange:V,itemRefCallback:W,selectedItem:ee,onItemLeave:H,itemTextRefCallback:G,focusSelectedItem:ce,selectedItemText:ae,position:s,isPositioned:ve,searchRef:Y,children:v.jsx(gh,{as:fr,allowPinchZoom:!0,children:v.jsx(Im,{asChild:!0,trapped:j.open,onMountAutoFocus:F=>{F.preventDefault()},onUnmountAutoFocus:Ve(a,F=>{var X;(X=j.trigger)==null||X.focus({preventScroll:!0}),F.preventDefault()}),children:v.jsx(Lm,{asChild:!0,disableOutsidePointerEvents:!0,onEscapeKeyDown:c,onPointerDownOutside:p,onFocusOutside:F=>F.preventDefault(),onDismiss:()=>j.onOpenChange(!1),children:v.jsx(ne,{role:"listbox",id:j.contentId,"data-state":j.open?"open":"closed",dir:j.dir,onContextMenu:F=>F.preventDefault(),...N,...te,onPlaced:()=>se(!0),ref:O,style:{display:"flex",flexDirection:"column",outline:"none",...N.style},onKeyDown:Ve(N.onKeyDown,F=>{const X=F.ctrlKey||F.altKey||F.metaKey;if(F.key==="Tab"&&F.preventDefault(),!X&&F.key.length===1&&_(F.key),["ArrowUp","ArrowDown","Home","End"].includes(F.key)){let oe=q().filter(me=>!me.disabled).map(me=>me.ref.current);if(["ArrowUp","End"].includes(F.key)&&(oe=oe.slice().reverse()),["ArrowUp","ArrowDown"].includes(F.key)){const me=F.target,ye=oe.indexOf(me);oe=oe.slice(ye+1)}setTimeout(()=>le(oe)),F.preventDefault()}})})})})})})});Ph.displayName=U1;var K1="SelectItemAlignedPosition",Nh=h.forwardRef((n,o)=>{const{__scopeSelect:l,onPlaced:s,...a}=n,c=Bn(hr,l),p=Un(hr,l),[f,m]=h.useState(null),[g,w]=h.useState(null),x=Je(o,O=>w(O)),S=Xi(l),E=h.useRef(!1),b=h.useRef(!0),{viewport:C,selectedItem:P,selectedItemText:N,focusSelectedItem:j}=p,z=h.useCallback(()=>{if(c.trigger&&c.valueNode&&f&&g&&C&&P&&N){const O=c.trigger.getBoundingClientRect(),ee=g.getBoundingClientRect(),re=c.valueNode.getBoundingClientRect(),ae=N.getBoundingClientRect();if(c.dir!=="rtl"){const me=ae.left-ee.left,ye=re.left-me,Se=O.left-ye,_e=O.width+Se,st=Math.max(_e,ee.width),gt=window.innerWidth-Vt,vt=sp(ye,[Vt,Math.max(Vt,gt-st)]);f.style.minWidth=_e+"px",f.style.left=vt+"px"}else{const me=ee.right-ae.right,ye=window.innerWidth-re.right-me,Se=window.innerWidth-O.right-ye,_e=O.width+Se,st=Math.max(_e,ee.width),gt=window.innerWidth-Vt,vt=sp(ye,[Vt,Math.max(Vt,gt-st)]);f.style.minWidth=_e+"px",f.style.right=vt+"px"}const pe=S(),q=window.innerHeight-Vt*2,ve=C.scrollHeight,se=window.getComputedStyle(g),ge=parseInt(se.borderTopWidth,10),le=parseInt(se.paddingTop,10),ce=parseInt(se.borderBottomWidth,10),$=parseInt(se.paddingBottom,10),J=ge+le+ve+$+ce,Y=Math.min(P.offsetHeight*5,J),_=window.getComputedStyle(C),W=parseInt(_.paddingTop,10),H=parseInt(_.paddingBottom,10),G=O.top+O.height/2-Vt,ne=q-G,te=P.offsetHeight/2,F=P.offsetTop+te,X=ge+le+F,K=J-X;if(X<=G){const me=pe.length>0&&P===pe[pe.length-1].ref.current;f.style.bottom="0px";const ye=g.clientHeight-C.offsetTop-C.offsetHeight,Se=Math.max(ne,te+(me?H:0)+ye+ce),_e=X+Se;f.style.height=_e+"px"}else{const me=pe.length>0&&P===pe[0].ref.current;f.style.top="0px";const Se=Math.max(G,ge+C.offsetTop+(me?W:0)+te)+K;f.style.height=Se+"px",C.scrollTop=X-G+C.offsetTop}f.style.margin=`${Vt}px 0`,f.style.minHeight=Y+"px",f.style.maxHeight=q+"px",s==null||s(),requestAnimationFrame(()=>E.current=!0)}},[S,c.trigger,c.valueNode,f,g,C,P,N,c.dir,s]);At(()=>z(),[z]);const[M,D]=h.useState();At(()=>{g&&D(window.getComputedStyle(g).zIndex)},[g]);const V=h.useCallback(O=>{O&&b.current===!0&&(z(),j==null||j(),b.current=!1)},[z,j]);return v.jsx(G1,{scope:l,contentWrapper:f,shouldExpandOnScrollRef:E,onScrollButtonChange:V,children:v.jsx("div",{ref:m,style:{display:"flex",flexDirection:"column",position:"fixed",zIndex:M},children:v.jsx(Be.div,{...a,ref:x,style:{boxSizing:"border-box",maxHeight:"100%",...a.style}})})})});Nh.displayName=K1;var Q1="SelectPopperPosition",vu=h.forwardRef((n,o)=>{const{__scopeSelect:l,align:s="start",collisionPadding:a=Vt,...c}=n,p=Zi(l);return v.jsx(US,{...p,...c,ref:o,align:s,collisionPadding:a,style:{boxSizing:"border-box",...c.style,"--radix-select-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-select-content-available-width":"var(--radix-popper-available-width)","--radix-select-content-available-height":"var(--radix-popper-available-height)","--radix-select-trigger-width":"var(--radix-popper-anchor-width)","--radix-select-trigger-height":"var(--radix-popper-anchor-height)"}})});vu.displayName=Q1;var[G1,Vu]=ao(hr,{}),yu="SelectViewport",Rh=h.forwardRef((n,o)=>{const{__scopeSelect:l,nonce:s,...a}=n,c=Un(yu,l),p=Vu(yu,l),f=Je(o,c.onViewportChange),m=h.useRef(0);return v.jsxs(v.Fragment,{children:[v.jsx("style",{dangerouslySetInnerHTML:{__html:"[data-radix-select-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-select-viewport]::-webkit-scrollbar{display:none}"},nonce:s}),v.jsx(Yi.Slot,{scope:l,children:v.jsx(Be.div,{"data-radix-select-viewport":"",role:"presentation",...a,ref:f,style:{position:"relative",flex:1,overflow:"hidden auto",...a.style},onScroll:Ve(a.onScroll,g=>{const w=g.currentTarget,{contentWrapper:x,shouldExpandOnScrollRef:S}=p;if(S!=null&&S.current&&x){const E=Math.abs(m.current-w.scrollTop);if(E>0){const b=window.innerHeight-Vt*2,C=parseFloat(x.style.minHeight),P=parseFloat(x.style.height),N=Math.max(C,P);if(N<b){const j=N+E,z=Math.min(b,j),M=j-z;x.style.height=z+"px",x.style.bottom==="0px"&&(w.scrollTop=M>0?M:0,x.style.justifyContent="flex-end")}}}m.current=w.scrollTop})})})]})});Rh.displayName=yu;var _h="SelectGroup",[Y1,X1]=ao(_h),Z1=h.forwardRef((n,o)=>{const{__scopeSelect:l,...s}=n,a=Au();return v.jsx(Y1,{scope:l,id:a,children:v.jsx(Be.div,{role:"group","aria-labelledby":a,...s,ref:o})})});Z1.displayName=_h;var Th="SelectLabel",q1=h.forwardRef((n,o)=>{const{__scopeSelect:l,...s}=n,a=X1(Th,l);return v.jsx(Be.div,{id:a.id,...s,ref:o})});q1.displayName=Th;var Di="SelectItem",[J1,jh]=ao(Di),zh=h.forwardRef((n,o)=>{const{__scopeSelect:l,value:s,disabled:a=!1,textValue:c,...p}=n,f=Bn(Di,l),m=Un(Di,l),g=f.value===s,[w,x]=h.useState(c??""),[S,E]=h.useState(!1),b=Je(o,j=>{var z;return(z=m.itemRefCallback)==null?void 0:z.call(m,j,s,a)}),C=Au(),P=h.useRef("touch"),N=()=>{a||(f.onValueChange(s),f.onOpenChange(!1))};if(s==="")throw new Error("A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.");return v.jsx(J1,{scope:l,value:s,disabled:a,textId:C,isSelected:g,onItemTextChange:h.useCallback(j=>{x(z=>z||((j==null?void 0:j.textContent)??"").trim())},[]),children:v.jsx(Yi.ItemSlot,{scope:l,value:s,disabled:a,textValue:w,children:v.jsx(Be.div,{role:"option","aria-labelledby":C,"data-highlighted":S?"":void 0,"aria-selected":g&&S,"data-state":g?"checked":"unchecked","aria-disabled":a||void 0,"data-disabled":a?"":void 0,tabIndex:a?void 0:-1,...p,ref:b,onFocus:Ve(p.onFocus,()=>E(!0)),onBlur:Ve(p.onBlur,()=>E(!1)),onClick:Ve(p.onClick,()=>{P.current!=="mouse"&&N()}),onPointerUp:Ve(p.onPointerUp,()=>{P.current==="mouse"&&N()}),onPointerDown:Ve(p.onPointerDown,j=>{P.current=j.pointerType}),onPointerMove:Ve(p.onPointerMove,j=>{var z;P.current=j.pointerType,a?(z=m.onItemLeave)==null||z.call(m):P.current==="mouse"&&j.currentTarget.focus({preventScroll:!0})}),onPointerLeave:Ve(p.onPointerLeave,j=>{var z;j.currentTarget===document.activeElement&&((z=m.onItemLeave)==null||z.call(m))}),onKeyDown:Ve(p.onKeyDown,j=>{var M;((M=m.searchRef)==null?void 0:M.current)!==""&&j.key===" "||(D1.includes(j.key)&&N(),j.key===" "&&j.preventDefault())})})})})});zh.displayName=Di;var qo="SelectItemText",Ah=h.forwardRef((n,o)=>{const{__scopeSelect:l,className:s,style:a,...c}=n,p=Bn(qo,l),f=Un(qo,l),m=jh(qo,l),g=H1(qo,l),[w,x]=h.useState(null),S=Je(o,N=>x(N),m.onItemTextChange,N=>{var j;return(j=f.itemTextRefCallback)==null?void 0:j.call(f,N,m.value,m.disabled)}),E=w==null?void 0:w.textContent,b=h.useMemo(()=>v.jsx("option",{value:m.value,disabled:m.disabled,children:E},m.value),[m.disabled,m.value,E]),{onNativeOptionAdd:C,onNativeOptionRemove:P}=g;return At(()=>(C(b),()=>P(b)),[C,P,b]),v.jsxs(v.Fragment,{children:[v.jsx(Be.span,{id:m.textId,...c,ref:S}),m.isSelected&&p.valueNode&&!p.valueNodeHasChildren?sl.createPortal(c.children,p.valueNode):null]})});Ah.displayName=qo;var Lh="SelectItemIndicator",Mh=h.forwardRef((n,o)=>{const{__scopeSelect:l,...s}=n;return jh(Lh,l).isSelected?v.jsx(Be.span,{"aria-hidden":!0,...s,ref:o}):null});Mh.displayName=Lh;var xu="SelectScrollUpButton",Ih=h.forwardRef((n,o)=>{const l=Un(xu,n.__scopeSelect),s=Vu(xu,n.__scopeSelect),[a,c]=h.useState(!1),p=Je(o,s.onScrollButtonChange);return At(()=>{if(l.viewport&&l.isPositioned){let f=function(){const g=m.scrollTop>0;c(g)};const m=l.viewport;return f(),m.addEventListener("scroll",f),()=>m.removeEventListener("scroll",f)}},[l.viewport,l.isPositioned]),a?v.jsx(Dh,{...n,ref:p,onAutoScroll:()=>{const{viewport:f,selectedItem:m}=l;f&&m&&(f.scrollTop=f.scrollTop-m.offsetHeight)}}):null});Ih.displayName=xu;var wu="SelectScrollDownButton",Oh=h.forwardRef((n,o)=>{const l=Un(wu,n.__scopeSelect),s=Vu(wu,n.__scopeSelect),[a,c]=h.useState(!1),p=Je(o,s.onScrollButtonChange);return At(()=>{if(l.viewport&&l.isPositioned){let f=function(){const g=m.scrollHeight-m.clientHeight,w=Math.ceil(m.scrollTop)<g;c(w)};const m=l.viewport;return f(),m.addEventListener("scroll",f),()=>m.removeEventListener("scroll",f)}},[l.viewport,l.isPositioned]),a?v.jsx(Dh,{...n,ref:p,onAutoScroll:()=>{const{viewport:f,selectedItem:m}=l;f&&m&&(f.scrollTop=f.scrollTop+m.offsetHeight)}}):null});Oh.displayName=wu;var Dh=h.forwardRef((n,o)=>{const{__scopeSelect:l,onAutoScroll:s,...a}=n,c=Un("SelectScrollButton",l),p=h.useRef(null),f=Xi(l),m=h.useCallback(()=>{p.current!==null&&(window.clearInterval(p.current),p.current=null)},[]);return h.useEffect(()=>()=>m(),[m]),At(()=>{var w;const g=f().find(x=>x.ref.current===document.activeElement);(w=g==null?void 0:g.ref.current)==null||w.scrollIntoView({block:"nearest"})},[f]),v.jsx(Be.div,{"aria-hidden":!0,...a,ref:o,style:{flexShrink:0,...a.style},onPointerDown:Ve(a.onPointerDown,()=>{p.current===null&&(p.current=window.setInterval(s,50))}),onPointerMove:Ve(a.onPointerMove,()=>{var g;(g=c.onItemLeave)==null||g.call(c),p.current===null&&(p.current=window.setInterval(s,50))}),onPointerLeave:Ve(a.onPointerLeave,()=>{m()})})}),eC="SelectSeparator",tC=h.forwardRef((n,o)=>{const{__scopeSelect:l,...s}=n;return v.jsx(Be.div,{"aria-hidden":!0,...s,ref:o})});tC.displayName=eC;var Su="SelectArrow",nC=h.forwardRef((n,o)=>{const{__scopeSelect:l,...s}=n,a=Zi(l),c=Bn(Su,l),p=Un(Su,l);return c.open&&p.position==="popper"?v.jsx(KS,{...a,...s,ref:o}):null});nC.displayName=Su;function $h(n){return n===""||n===void 0}var Fh=h.forwardRef((n,o)=>{const{value:l,...s}=n,a=h.useRef(null),c=Je(o,a),p=YS(l);return h.useEffect(()=>{const f=a.current,m=window.HTMLSelectElement.prototype,w=Object.getOwnPropertyDescriptor(m,"value").set;if(p!==l&&w){const x=new Event("change",{bubbles:!0});w.call(f,l),f.dispatchEvent(x)}},[p,l]),v.jsx(sh,{asChild:!0,children:v.jsx("select",{...s,ref:c,defaultValue:l})})});Fh.displayName="BubbleSelect";function Wh(n){const o=pr(n),l=h.useRef(""),s=h.useRef(0),a=h.useCallback(p=>{const f=l.current+p;o(f),function m(g){l.current=g,window.clearTimeout(s.current),g!==""&&(s.current=window.setTimeout(()=>m(""),1e3))}(f)},[o]),c=h.useCallback(()=>{l.current="",window.clearTimeout(s.current)},[]);return h.useEffect(()=>()=>window.clearTimeout(s.current),[]),[l,a,c]}function Hh(n,o,l){const a=o.length>1&&Array.from(o).every(g=>g===o[0])?o[0]:o,c=l?n.indexOf(l):-1;let p=rC(n,Math.max(c,0));a.length===1&&(p=p.filter(g=>g!==l));const m=p.find(g=>g.textValue.toLowerCase().startsWith(a.toLowerCase()));return m!==l?m:void 0}function rC(n,o){return n.map((l,s)=>n[(o+s)%n.length])}var oC=vh,lC=xh,iC=Sh,sC=Ch,aC=Eh,uC=bh,cC=Rh,dC=zh,fC=Ah,pC=Mh,mC=Ih,hC=Oh;function _p({...n}){return v.jsx(oC,{"data-slot":"select",...n})}function Tp({...n}){return v.jsx(iC,{"data-slot":"select-value",...n})}function jp({className:n,size:o="default",children:l,...s}){return v.jsxs(lC,{"data-slot":"select-trigger","data-size":o,className:De("border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-full items-center justify-between gap-2 rounded-md border bg-input-background px-3 py-2 text-sm whitespace-nowrap transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",n),...s,children:[l,v.jsx(sC,{asChild:!0,children:v.jsx(_u,{className:"size-4 opacity-50"})})]})}function zp({className:n,children:o,position:l="popper",...s}){return v.jsx(aC,{children:v.jsxs(uC,{"data-slot":"select-content",className:De("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md",l==="popper"&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",n),position:l,...s,children:[v.jsx(gC,{}),v.jsx(cC,{className:De("p-1",l==="popper"&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1"),children:o}),v.jsx(vC,{})]})})}function Ap({className:n,children:o,...l}){return v.jsxs(dC,{"data-slot":"select-item",className:De("focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2",n),...l,children:[v.jsx("span",{className:"absolute right-2 flex size-3.5 items-center justify-center",children:v.jsx(pC,{children:v.jsx(xx,{className:"size-4"})})}),v.jsx(fC,{children:o})]})}function gC({className:n,...o}){return v.jsx(mC,{"data-slot":"select-scroll-up-button",className:De("flex cursor-default items-center justify-center py-1",n),...o,children:v.jsx(Cx,{className:"size-4"})})}function vC({className:n,...o}){return v.jsx(hC,{"data-slot":"select-scroll-down-button",className:De("flex cursor-default items-center justify-center py-1",n),...o,children:v.jsx(_u,{className:"size-4"})})}const Dn=n=>({ko:"한국어",en:"English",ja:"日本語",zh:"中文",auto:"자동 감지"})[n]||n,yC=(n,o)=>{var a;return((a={en:{"분산 캐싱 구현 일정은 어떻게 되나요?":"What is the implementation schedule for distributed caching?","데이터베이스 샤딩 비용은 얼마나 들까요?":"How much would database sharding cost?","현재 시스템 모니터링 도구는 무엇을 사용하고 있나요?":"What system monitoring tools are currently being used?",성능:"performance",최적화:"optimization",데이터베이스:"database",캐시:"cache",모니터링:"monitoring","언제까지 완료될 예정인가요?":"When is it expected to be completed?","비용은 어느 정도 예상하시나요?":"How much cost do you estimate?"},ko:{"What is the implementation schedule for distributed caching?":"분산 캐싱 구현 일정은 어떻게 되나요?","How much would database sharding cost?":"데이터베이스 샤딩 비용은 얼마나 들까요?","What system monitoring tools are currently being used?":"현재 시스템 모니터링 도구는 무엇을 사용하고 있나요?",performance:"성능",optimization:"최적화",database:"데이터베이스",cache:"캐시",monitoring:"모니터링","When is it expected to be completed?":"언제까지 완료될 예정인가요?","How much cost do you estimate?":"비용은 어느 정도 예상하시나요?"},ja:{"분산 캐싱 구현 일정은 어떻게 되나요?":"分散キャッシングの実装スケジュールはどうなっていますか？","데이터베이스 샤딩 비용은 얼마나 들까요?":"データベースシャーディングのコストはどのくらいかかりますか？","현재 시스템 모니터링 도구는 무엇을 사용하고 있나요?":"現在のシステム監視ツールは何を使用していますか？",성능:"パフォーマンス",최적화:"最適化",데이터베이스:"データベース",캐시:"キャッシュ",모니터링:"モニタリング"},zh:{"분산 캐싱 구현 일정은 어떻게 되나요?":"分布式缓存实施进度如何？","데이터베이스 샤딩 비용은 얼마나 들까요?":"数据库分片的成本是多少？","현재 시스템 모니터링 도구는 무엇을 사용하고 있나요?":"目前使用什么系统监控工具？",성능:"性能",최적화:"优化",데이터베이스:"数据库",캐시:"缓存",모니터링:"监控"}}[o==="auto"?"en":o])==null?void 0:a[n])||n};function xC({onQuestionSubmit:n,suggestedQuestions:o=[],sourceLang:l,targetLang:s}){const[a,c]=h.useState(""),[p,f]=h.useState(!1),m=h.useRef(null),g=h.useMemo(()=>{if(!a.trim())return o.slice(0,3);const D=a.toLowerCase().split(" ").filter(V=>V.length>0);return o.filter(V=>D.some(O=>V.toLowerCase().includes(O))).slice(0,3)},[a,o]),[w,x]=h.useState(""),[S,E]=h.useState(""),b=h.useMemo(()=>{if(!a.trim())return w;const D=yC(a,l);return x(D),D},[a,l,w]),C=D=>{const V=D.target.value;c(V)},P=D=>{c(D)},N=()=>{a.trim()&&(E(a),f(!0),c(""))},j=()=>{f(!1)},z=D=>{if((D.metaKey||D.ctrlKey)&&D.key==="a"){D.preventDefault(),m.current&&m.current.select();return}D.key==="Enter"&&(D.preventDefault(),p?j():a.trim()&&N())},M=l==="auto"?"en":l;return v.jsxs("div",{className:"relative space-y-3",children:[v.jsxs("div",{className:"flex items-center gap-2",children:[v.jsx(Tx,{size:16,className:"text-amber-500"}),v.jsx("h3",{className:"text-sm",children:"질문 도우미"}),v.jsxs(du,{variant:"outline",className:"text-xs bg-gray-100 text-gray-600",children:[Dn(s)," → ",Dn(M)]})]}),v.jsxs("div",{className:"space-y-2",children:[v.jsxs("div",{className:"flex gap-2",children:[v.jsx(Xr,{ref:m,value:a,onChange:C,onKeyDown:z,placeholder:`질문을 ${Dn(s)}로 입력하세요...`,className:"text-sm flex-1"}),v.jsxs(zt,{onClick:N,disabled:!a.trim(),variant:"outline",size:"sm",className:"shrink-0",children:[v.jsx(cu,{size:14,className:"mr-1"}),"번역"]})]}),v.jsxs("p",{className:"text-xs text-gray-500",children:[Dn(s),"로 입력하면 ",Dn(M),"로 번역해드립니다"]})]}),g.length>0&&v.jsxs("div",{className:"space-y-2",children:[v.jsx("p",{className:"text-xs text-gray-600",children:"추천 질문:"}),v.jsx("div",{className:"space-y-1",children:g.map((D,V)=>v.jsx("button",{onClick:()=>P(D),className:"w-full text-left p-2 text-xs bg-gray-50 hover:bg-gray-100 rounded border text-gray-700 transition-colors",children:D},V))})]}),p&&v.jsx("div",{className:"absolute inset-0 z-10 bg-white/95 backdrop-blur-sm rounded-lg border shadow-lg animate-in fade-in-0 duration-200",tabIndex:0,onKeyDown:D=>{(D.key==="Enter"||D.key==="Escape")&&(D.preventDefault(),j())},children:v.jsx(Tu,{className:"h-full bg-green-50 border-green-200 shadow-sm",children:v.jsxs(ju,{className:"p-4 h-full flex flex-col",children:[v.jsxs("div",{className:"flex items-center justify-between mb-3",children:[v.jsxs(du,{variant:"outline",className:"text-xs bg-green-100 text-green-700 border-green-300",children:[Dn(M),"로 번역됨"]}),v.jsx(zt,{variant:"ghost",size:"sm",onClick:j,className:"h-6 w-6 p-0 hover:bg-green-200 text-green-600",children:v.jsx(Hx,{size:14})})]}),v.jsxs("div",{className:"flex-1 flex flex-col justify-center space-y-3",children:[v.jsxs("div",{className:"text-center",children:[v.jsxs("p",{className:"text-xs text-green-500 opacity-70 mb-1",children:["원문 (",Dn(s),")"]}),v.jsx("p",{className:"text-xs text-green-600 opacity-80 leading-relaxed whitespace-normal break-words",children:S})]}),v.jsxs("div",{className:"flex items-center gap-2 px-4",children:[v.jsx("div",{className:"flex-1 h-px bg-green-200"}),v.jsx(cu,{size:12,className:"text-green-400"}),v.jsx("div",{className:"flex-1 h-px bg-green-200"})]}),v.jsxs("div",{className:"text-center",children:[v.jsxs("p",{className:"text-xs text-green-500 opacity-70 mb-1",children:["번역 (",Dn(M),")"]}),v.jsx("p",{className:"text-sm text-green-800 leading-relaxed whitespace-normal break-words",children:b})]})]}),v.jsx("div",{className:"text-center mt-3",children:v.jsx("p",{className:"text-xs text-green-600 opacity-75",children:"Enter 또는 ESC 키를 눌러 닫기"})})]})})})]})}function wC({onClearAll:n,onAddItem:o,onAddTranscription:l,onAddTranslation:s}){const[a,c]=h.useState(""),p=()=>{console.log("Debug: 전체 아이템 지우기"),n==null||n()},f=()=>{console.log("Debug: 빈 아이템 추가"),o==null||o(a)},m=()=>{console.log("Debug: 전사 추가 -",a),l==null||l(a)},g=()=>{console.log("Debug: 번역 추가 -",a),s==null||s(a)};return v.jsx(Tu,{className:"border-orange-200 bg-orange-50/50 shadow-sm",children:v.jsxs(ju,{className:"p-2 space-y-1",children:[v.jsx(Xr,{value:a,onChange:w=>c(w.target.value),placeholder:"테스트 텍스트 입력...",className:"border-orange-200 focus:border-orange-400 bg-white h-7 text-xs"}),v.jsxs("div",{className:"grid grid-cols-2 gap-1",children:[v.jsxs(zt,{onClick:p,variant:"destructive",size:"sm",className:"text-xs h-6 bg-red-500 hover:bg-red-600 px-2",children:[v.jsx(jm,{size:10,className:"mr-1"}),"전체 지우기"]}),v.jsxs(zt,{onClick:f,variant:"outline",size:"sm",className:"text-xs h-6 border-orange-300 text-orange-700 hover:bg-orange-100 px-2",children:[v.jsx(Tm,{size:10,className:"mr-1"}),"아이템 추가"]}),v.jsxs(zt,{onClick:m,disabled:!a.trim(),variant:"outline",size:"sm",className:"text-xs h-6 border-blue-300 text-blue-700 hover:bg-blue-100 px-2",children:[v.jsx(kx,{size:10,className:"mr-1"}),"전사 추가"]}),v.jsxs(zt,{onClick:g,disabled:!a.trim(),variant:"outline",size:"sm",className:"text-xs h-6 border-green-300 text-green-700 hover:bg-green-100 px-2",children:[v.jsx(cu,{size:10,className:"mr-1"}),"번역 추가"]})]})]})})}const Lp=[{value:"ko",label:"한국어"},{value:"en",label:"English"},{value:"ja",label:"日본語"},{value:"zh",label:"中文"}];function Mp({isRecording:n,onRecordingToggle:o,sourceLang:l,targetLang:s,onSourceLangChange:a,onTargetLangChange:c,isOnlineMode:p,onModeToggle:f,suggestedQuestions:m=[],isQuestionHelperVisible:g,onQuestionHelperToggle:w,isDebugMode:x,onDebugModeToggle:S,isSummaryDebugMode:E,onSummaryDebugModeToggle:b,onClearAllTranslations:C,onAddItem:P,onAddTranscription:N,onAddTranslation:j,isLoading:z=!1,recordingError:M=null,hasPermission:D=null}){const V=()=>{a(s),c(l)},O=ee=>{console.log("Question for reference:",ee)};return v.jsxs("div",{className:"border-t bg-gray-50 p-4 space-y-4 shrink-0",children:[M&&v.jsx("div",{className:"bg-red-50 border border-red-200 rounded-lg p-3",children:v.jsxs("div",{className:"flex items-center gap-2 text-red-800",children:[v.jsx("span",{className:"text-sm font-medium",children:"오류:"}),v.jsx("span",{className:"text-sm",children:M})]})}),D===!1&&v.jsx("div",{className:"bg-yellow-50 border border-yellow-200 rounded-lg p-3",children:v.jsxs("div",{className:"flex items-center gap-2 text-yellow-800",children:[v.jsx("span",{className:"text-sm font-medium",children:"권한 필요:"}),v.jsx("span",{className:"text-sm",children:"시스템 오디오 녹음을 위해 마이크 권한이 필요합니다."})]})}),v.jsxs("div",{className:"bg-white rounded-lg p-4 border border-gray-200 shadow-sm",children:[v.jsxs("div",{className:"flex items-center justify-between mb-4",children:[v.jsxs("div",{className:"flex items-center gap-3",children:[v.jsx(We,{className:n?"text-sm text-muted-foreground":"text-sm",children:"Source:"}),v.jsxs(_p,{value:l,onValueChange:a,disabled:n,children:[v.jsx(jp,{className:"w-32",children:v.jsx(Tp,{})}),v.jsx(zp,{children:Lp.map(ee=>v.jsx(Ap,{value:ee.value,children:ee.label},ee.value))})]})]}),v.jsxs("div",{className:"flex items-center gap-4",children:[v.jsxs(zt,{onClick:o,variant:n?"destructive":D===!1?"outline":"default",size:"lg",className:"relative shrink-0 h-14 w-14 rounded-full shadow-lg",disabled:z,title:D===!1?"오디오 권한이 필요합니다. 클릭하여 권한을 요청하세요.":n?"녹음 중지":"시스템 오디오 녹음 시작",children:[z?v.jsx("div",{className:"animate-spin rounded-full h-6 w-6 border-b-2 border-current"}):n?v.jsx(Lx,{size:24}):v.jsx(Ix,{size:24}),n&&!z&&v.jsx("span",{className:"absolute -top-1 -right-1 w-4 h-4 bg-red-500 rounded-full animate-pulse"})]}),v.jsx(zt,{onClick:V,variant:"outline",size:"sm",disabled:n,className:"shrink-0 h-8 w-8 rounded-full p-0",title:"언어 전환",children:v.jsx($x,{size:14})})]}),v.jsxs("div",{className:"flex items-center gap-3",children:[v.jsx(We,{className:n?"text-sm text-muted-foreground":"text-sm",children:"Target:"}),v.jsxs(_p,{value:s,onValueChange:c,disabled:n,children:[v.jsx(jp,{className:"w-32",children:v.jsx(Tp,{})}),v.jsx(zp,{children:Lp.map(ee=>v.jsx(Ap,{value:ee.value,children:ee.label},ee.value))})]})]})]}),v.jsx("div",{className:"flex justify-center pt-3 border-t border-gray-100",children:v.jsxs("div",{className:"flex items-center gap-3",children:[v.jsx(We,{className:"text-sm",children:"회의 모드:"}),v.jsxs("div",{className:"flex items-center gap-2",children:[v.jsx(We,{className:"text-xs text-muted-foreground",children:"오프라인"}),v.jsx(tl,{checked:p,onCheckedChange:f}),v.jsx(We,{className:"text-xs text-muted-foreground",children:"온라인"})]})]})})]}),g&&v.jsx("div",{className:"bg-white rounded-lg p-4 border border-gray-200 shadow-sm",children:v.jsx(xC,{onQuestionSubmit:O,suggestedQuestions:m,sourceLang:l,targetLang:s})}),v.jsx("div",{className:"bg-white rounded-lg px-4 py-2 border border-gray-200 shadow-sm",children:v.jsxs("div",{className:"flex items-center justify-center gap-6",children:[v.jsx(vx,{size:18,className:"text-muted-foreground"}),v.jsxs("div",{className:"flex items-center gap-2",children:[v.jsx(We,{className:"text-sm text-muted-foreground",children:"실시간 통역"}),v.jsx(tl,{checked:x,onCheckedChange:S,className:"data-[state=checked]:bg-orange-600"})]}),v.jsxs("div",{className:"flex items-center gap-2",children:[v.jsx(We,{className:"text-sm text-muted-foreground",children:"요약패널"}),v.jsx(tl,{checked:E,onCheckedChange:b,className:"data-[state=checked]:bg-blue-600"})]})]})}),x&&v.jsx("div",{className:"animate-in slide-in-from-top-2 duration-300",children:v.jsx(wC,{onClearAll:C,onAddItem:P,onAddTranscription:N,onAddTranslation:j})})]})}const{createElement:ro,createContext:SC,forwardRef:Vh,useCallback:mt,useContext:Bh,useEffect:cr,useImperativeHandle:Uh,useLayoutEffect:CC,useMemo:EC,useRef:ht,useState:Jr}=Wi,Ip=Wi[`useId${Math.random()}`.slice(0,5)],bC=CC,qi=SC(null);qi.displayName="PanelGroupContext";const dr=bC,kC=typeof Ip=="function"?Ip:()=>null;let PC=0;function Bu(n=null){const o=kC(),l=ht(n||o||null);return l.current===null&&(l.current=""+PC++),n??l.current}function Kh({children:n,className:o="",collapsedSize:l,collapsible:s,defaultSize:a,forwardedRef:c,id:p,maxSize:f,minSize:m,onCollapse:g,onExpand:w,onResize:x,order:S,style:E,tagName:b="div",...C}){const P=Bh(qi);if(P===null)throw Error("Panel components must be rendered within a PanelGroup container");const{collapsePanel:N,expandPanel:j,getPanelSize:z,getPanelStyle:M,groupId:D,isPanelCollapsed:V,reevaluatePanelConstraints:O,registerPanel:ee,resizePanel:re,unregisterPanel:ae}=P,pe=Bu(p),q=ht({callbacks:{onCollapse:g,onExpand:w,onResize:x},constraints:{collapsedSize:l,collapsible:s,defaultSize:a,maxSize:f,minSize:m},id:pe,idIsFromProps:p!==void 0,order:S});ht({didLogMissingDefaultSizeWarning:!1}),dr(()=>{const{callbacks:se,constraints:ge}=q.current,le={...ge};q.current.id=pe,q.current.idIsFromProps=p!==void 0,q.current.order=S,se.onCollapse=g,se.onExpand=w,se.onResize=x,ge.collapsedSize=l,ge.collapsible=s,ge.defaultSize=a,ge.maxSize=f,ge.minSize=m,(le.collapsedSize!==ge.collapsedSize||le.collapsible!==ge.collapsible||le.maxSize!==ge.maxSize||le.minSize!==ge.minSize)&&O(q.current,le)}),dr(()=>{const se=q.current;return ee(se),()=>{ae(se)}},[S,pe,ee,ae]),Uh(c,()=>({collapse:()=>{N(q.current)},expand:se=>{j(q.current,se)},getId(){return pe},getSize(){return z(q.current)},isCollapsed(){return V(q.current)},isExpanded(){return!V(q.current)},resize:se=>{re(q.current,se)}}),[N,j,z,V,pe,re]);const ve=M(q.current,a);return ro(b,{...C,children:n,className:o,id:p,style:{...ve,...E},"data-panel":"","data-panel-collapsible":s||void 0,"data-panel-group-id":D,"data-panel-id":pe,"data-panel-size":parseFloat(""+ve.flexGrow).toFixed(1)})}const Qh=Vh((n,o)=>ro(Kh,{...n,forwardedRef:o}));Kh.displayName="Panel";Qh.displayName="forwardRef(Panel)";let Cu=null,ar=null;function NC(n,o){if(o){const l=(o&qh)!==0,s=(o&Jh)!==0,a=(o&eg)!==0,c=(o&tg)!==0;if(l)return a?"se-resize":c?"ne-resize":"e-resize";if(s)return a?"sw-resize":c?"nw-resize":"w-resize";if(a)return"s-resize";if(c)return"n-resize"}switch(n){case"horizontal":return"ew-resize";case"intersection":return"move";case"vertical":return"ns-resize"}}function RC(){ar!==null&&(document.head.removeChild(ar),Cu=null,ar=null)}function iu(n,o){const l=NC(n,o);Cu!==l&&(Cu=l,ar===null&&(ar=document.createElement("style"),document.head.appendChild(ar)),ar.innerHTML=`*{cursor: ${l}!important;}`)}function Gh(n){return n.type==="keydown"}function Yh(n){return n.type.startsWith("pointer")}function Xh(n){return n.type.startsWith("mouse")}function Ji(n){if(Yh(n)){if(n.isPrimary)return{x:n.clientX,y:n.clientY}}else if(Xh(n))return{x:n.clientX,y:n.clientY};return{x:1/0,y:1/0}}function _C(){if(typeof matchMedia=="function")return matchMedia("(pointer:coarse)").matches?"coarse":"fine"}function TC(n,o,l){return n.x<o.x+o.width&&n.x+n.width>o.x&&n.y<o.y+o.height&&n.y+n.height>o.y}function jC(n,o){if(n===o)throw new Error("Cannot compare node with itself");const l={a:$p(n),b:$p(o)};let s;for(;l.a.at(-1)===l.b.at(-1);)n=l.a.pop(),o=l.b.pop(),s=n;be(s,"Stacking order can only be calculated for elements with a common ancestor");const a={a:Dp(Op(l.a)),b:Dp(Op(l.b))};if(a.a===a.b){const c=s.childNodes,p={a:l.a.at(-1),b:l.b.at(-1)};let f=c.length;for(;f--;){const m=c[f];if(m===p.a)return 1;if(m===p.b)return-1}}return Math.sign(a.a-a.b)}const zC=/\b(?:position|zIndex|opacity|transform|webkitTransform|mixBlendMode|filter|webkitFilter|isolation)\b/;function AC(n){var o;const l=getComputedStyle((o=Zh(n))!==null&&o!==void 0?o:n).display;return l==="flex"||l==="inline-flex"}function LC(n){const o=getComputedStyle(n);return!!(o.position==="fixed"||o.zIndex!=="auto"&&(o.position!=="static"||AC(n))||+o.opacity<1||"transform"in o&&o.transform!=="none"||"webkitTransform"in o&&o.webkitTransform!=="none"||"mixBlendMode"in o&&o.mixBlendMode!=="normal"||"filter"in o&&o.filter!=="none"||"webkitFilter"in o&&o.webkitFilter!=="none"||"isolation"in o&&o.isolation==="isolate"||zC.test(o.willChange)||o.webkitOverflowScrolling==="touch")}function Op(n){let o=n.length;for(;o--;){const l=n[o];if(be(l,"Missing node"),LC(l))return l}return null}function Dp(n){return n&&Number(getComputedStyle(n).zIndex)||0}function $p(n){const o=[];for(;n;)o.push(n),n=Zh(n);return o}function Zh(n){const{parentNode:o}=n;return o&&o instanceof ShadowRoot?o.host:o}const qh=1,Jh=2,eg=4,tg=8,MC=_C()==="coarse";let Bt=[],eo=!1,Wn=new Map,es=new Map;const ll=new Set;function IC(n,o,l,s,a){var c;const{ownerDocument:p}=o,f={direction:l,element:o,hitAreaMargins:s,setResizeHandlerState:a},m=(c=Wn.get(p))!==null&&c!==void 0?c:0;return Wn.set(p,m+1),ll.add(f),$i(),function(){var w;es.delete(n),ll.delete(f);const x=(w=Wn.get(p))!==null&&w!==void 0?w:1;if(Wn.set(p,x-1),$i(),x===1&&Wn.delete(p),Bt.includes(f)){const S=Bt.indexOf(f);S>=0&&Bt.splice(S,1),Ku(),a("up",!0,null)}}}function Fp(n){const{target:o}=n,{x:l,y:s}=Ji(n);eo=!0,Uu({target:o,x:l,y:s}),$i(),Bt.length>0&&(Fi("down",n),n.preventDefault(),n.stopPropagation())}function Zo(n){const{x:o,y:l}=Ji(n);if(eo&&n.buttons===0&&(eo=!1,Fi("up",n)),!eo){const{target:s}=n;Uu({target:s,x:o,y:l})}Fi("move",n),Ku(),Bt.length>0&&n.preventDefault()}function Ur(n){const{target:o}=n,{x:l,y:s}=Ji(n);es.clear(),eo=!1,Bt.length>0&&n.preventDefault(),Fi("up",n),Uu({target:o,x:l,y:s}),Ku(),$i()}function Uu({target:n,x:o,y:l}){Bt.splice(0);let s=null;(n instanceof HTMLElement||n instanceof SVGElement)&&(s=n),ll.forEach(a=>{const{element:c,hitAreaMargins:p}=a,f=c.getBoundingClientRect(),{bottom:m,left:g,right:w,top:x}=f,S=MC?p.coarse:p.fine;if(o>=g-S&&o<=w+S&&l>=x-S&&l<=m+S){if(s!==null&&document.contains(s)&&c!==s&&!c.contains(s)&&!s.contains(c)&&jC(s,c)>0){let b=s,C=!1;for(;b&&!b.contains(c);){if(TC(b.getBoundingClientRect(),f)){C=!0;break}b=b.parentElement}if(C)return}Bt.push(a)}})}function su(n,o){es.set(n,o)}function Ku(){let n=!1,o=!1;Bt.forEach(s=>{const{direction:a}=s;a==="horizontal"?n=!0:o=!0});let l=0;es.forEach(s=>{l|=s}),n&&o?iu("intersection",l):n?iu("horizontal",l):o?iu("vertical",l):RC()}function $i(){Wn.forEach((n,o)=>{const{body:l}=o;l.removeEventListener("contextmenu",Ur),l.removeEventListener("pointerdown",Fp),l.removeEventListener("pointerleave",Zo),l.removeEventListener("pointermove",Zo)}),window.removeEventListener("pointerup",Ur),window.removeEventListener("pointercancel",Ur),ll.size>0&&(eo?(Bt.length>0&&Wn.forEach((n,o)=>{const{body:l}=o;n>0&&(l.addEventListener("contextmenu",Ur),l.addEventListener("pointerleave",Zo),l.addEventListener("pointermove",Zo))}),window.addEventListener("pointerup",Ur),window.addEventListener("pointercancel",Ur)):Wn.forEach((n,o)=>{const{body:l}=o;n>0&&(l.addEventListener("pointerdown",Fp,{capture:!0}),l.addEventListener("pointermove",Zo))}))}function Fi(n,o){ll.forEach(l=>{const{setResizeHandlerState:s}=l,a=Bt.includes(l);s(n,a,o)})}function OC(){const[n,o]=Jr(0);return mt(()=>o(l=>l+1),[])}function be(n,o){if(!n)throw console.error(o),Error(o)}const Qu=10;function gr(n,o,l=Qu){return n.toFixed(l)===o.toFixed(l)?0:n>o?1:-1}function mn(n,o,l=Qu){return gr(n,o,l)===0}function Ct(n,o,l){return gr(n,o,l)===0}function DC(n,o,l){if(n.length!==o.length)return!1;for(let s=0;s<n.length;s++){const a=n[s],c=o[s];if(!Ct(a,c,l))return!1}return!0}function Gr({panelConstraints:n,panelIndex:o,size:l}){const s=n[o];be(s!=null,`Panel constraints not found for index ${o}`);let{collapsedSize:a=0,collapsible:c,maxSize:p=100,minSize:f=0}=s;if(gr(l,f)<0)if(c){const m=(a+f)/2;gr(l,m)<0?l=a:l=f}else l=f;return l=Math.min(p,l),l=parseFloat(l.toFixed(Qu)),l}function Jo({delta:n,initialLayout:o,panelConstraints:l,pivotIndices:s,prevLayout:a,trigger:c}){if(Ct(n,0))return o;const p=[...o],[f,m]=s;be(f!=null,"Invalid first pivot index"),be(m!=null,"Invalid second pivot index");let g=0;if(c==="keyboard"){{const x=n<0?m:f,S=l[x];be(S,`Panel constraints not found for index ${x}`);const{collapsedSize:E=0,collapsible:b,minSize:C=0}=S;if(b){const P=o[x];if(be(P!=null,`Previous layout not found for panel index ${x}`),Ct(P,E)){const N=C-P;gr(N,Math.abs(n))>0&&(n=n<0?0-N:N)}}}{const x=n<0?f:m,S=l[x];be(S,`No panel constraints found for index ${x}`);const{collapsedSize:E=0,collapsible:b,minSize:C=0}=S;if(b){const P=o[x];if(be(P!=null,`Previous layout not found for panel index ${x}`),Ct(P,C)){const N=P-E;gr(N,Math.abs(n))>0&&(n=n<0?0-N:N)}}}}{const x=n<0?1:-1;let S=n<0?m:f,E=0;for(;;){const C=o[S];be(C!=null,`Previous layout not found for panel index ${S}`);const N=Gr({panelConstraints:l,panelIndex:S,size:100})-C;if(E+=N,S+=x,S<0||S>=l.length)break}const b=Math.min(Math.abs(n),Math.abs(E));n=n<0?0-b:b}{let S=n<0?f:m;for(;S>=0&&S<l.length;){const E=Math.abs(n)-Math.abs(g),b=o[S];be(b!=null,`Previous layout not found for panel index ${S}`);const C=b-E,P=Gr({panelConstraints:l,panelIndex:S,size:C});if(!Ct(b,P)&&(g+=b-P,p[S]=P,g.toPrecision(3).localeCompare(Math.abs(n).toPrecision(3),void 0,{numeric:!0})>=0))break;n<0?S--:S++}}if(DC(a,p))return a;{const x=n<0?m:f,S=o[x];be(S!=null,`Previous layout not found for panel index ${x}`);const E=S+g,b=Gr({panelConstraints:l,panelIndex:x,size:E});if(p[x]=b,!Ct(b,E)){let C=E-b,N=n<0?m:f;for(;N>=0&&N<l.length;){const j=p[N];be(j!=null,`Previous layout not found for panel index ${N}`);const z=j+C,M=Gr({panelConstraints:l,panelIndex:N,size:z});if(Ct(j,M)||(C-=M-j,p[N]=M),Ct(C,0))break;n>0?N--:N++}}}const w=p.reduce((x,S)=>S+x,0);return Ct(w,100)?p:a}function $C({layout:n,panelsArray:o,pivotIndices:l}){let s=0,a=100,c=0,p=0;const f=l[0];be(f!=null,"No pivot index found"),o.forEach((x,S)=>{const{constraints:E}=x,{maxSize:b=100,minSize:C=0}=E;S===f?(s=C,a=b):(c+=C,p+=b)});const m=Math.min(a,100-c),g=Math.max(s,100-p),w=n[f];return{valueMax:m,valueMin:g,valueNow:w}}function il(n,o=document){return Array.from(o.querySelectorAll(`[data-panel-resize-handle-id][data-panel-group-id="${n}"]`))}function ng(n,o,l=document){const a=il(n,l).findIndex(c=>c.getAttribute("data-panel-resize-handle-id")===o);return a??null}function rg(n,o,l){const s=ng(n,o,l);return s!=null?[s,s+1]:[-1,-1]}function og(n,o=document){var l;if(o instanceof HTMLElement&&(o==null||(l=o.dataset)===null||l===void 0?void 0:l.panelGroupId)==n)return o;const s=o.querySelector(`[data-panel-group][data-panel-group-id="${n}"]`);return s||null}function ts(n,o=document){const l=o.querySelector(`[data-panel-resize-handle-id="${n}"]`);return l||null}function FC(n,o,l,s=document){var a,c,p,f;const m=ts(o,s),g=il(n,s),w=m?g.indexOf(m):-1,x=(a=(c=l[w])===null||c===void 0?void 0:c.id)!==null&&a!==void 0?a:null,S=(p=(f=l[w+1])===null||f===void 0?void 0:f.id)!==null&&p!==void 0?p:null;return[x,S]}function WC({committedValuesRef:n,eagerValuesRef:o,groupId:l,layout:s,panelDataArray:a,panelGroupElement:c,setLayout:p}){ht({didWarnAboutMissingResizeHandle:!1}),dr(()=>{if(!c)return;const f=il(l,c);for(let m=0;m<a.length-1;m++){const{valueMax:g,valueMin:w,valueNow:x}=$C({layout:s,panelsArray:a,pivotIndices:[m,m+1]}),S=f[m];if(S!=null){const E=a[m];be(E,`No panel data found for index "${m}"`),S.setAttribute("aria-controls",E.id),S.setAttribute("aria-valuemax",""+Math.round(g)),S.setAttribute("aria-valuemin",""+Math.round(w)),S.setAttribute("aria-valuenow",x!=null?""+Math.round(x):"")}}return()=>{f.forEach((m,g)=>{m.removeAttribute("aria-controls"),m.removeAttribute("aria-valuemax"),m.removeAttribute("aria-valuemin"),m.removeAttribute("aria-valuenow")})}},[l,s,a,c]),cr(()=>{if(!c)return;const f=o.current;be(f,"Eager values not found");const{panelDataArray:m}=f,g=og(l,c);be(g!=null,`No group found for id "${l}"`);const w=il(l,c);be(w,`No resize handles found for group id "${l}"`);const x=w.map(S=>{const E=S.getAttribute("data-panel-resize-handle-id");be(E,"Resize handle element has no handle id attribute");const[b,C]=FC(l,E,m,c);if(b==null||C==null)return()=>{};const P=N=>{if(!N.defaultPrevented)switch(N.key){case"Enter":{N.preventDefault();const j=m.findIndex(z=>z.id===b);if(j>=0){const z=m[j];be(z,`No panel data found for index ${j}`);const M=s[j],{collapsedSize:D=0,collapsible:V,minSize:O=0}=z.constraints;if(M!=null&&V){const ee=Jo({delta:Ct(M,D)?O-D:D-M,initialLayout:s,panelConstraints:m.map(re=>re.constraints),pivotIndices:rg(l,E,c),prevLayout:s,trigger:"keyboard"});s!==ee&&p(ee)}}break}}};return S.addEventListener("keydown",P),()=>{S.removeEventListener("keydown",P)}});return()=>{x.forEach(S=>S())}},[c,n,o,l,s,a,p])}function Wp(n,o){if(n.length!==o.length)return!1;for(let l=0;l<n.length;l++)if(n[l]!==o[l])return!1;return!0}function lg(n,o){const l=n==="horizontal",{x:s,y:a}=Ji(o);return l?s:a}function HC(n,o,l,s,a){const c=l==="horizontal",p=ts(o,a);be(p,`No resize handle element found for id "${o}"`);const f=p.getAttribute("data-panel-group-id");be(f,"Resize handle element has no group id attribute");let{initialCursorPosition:m}=s;const g=lg(l,n),w=og(f,a);be(w,`No group element found for id "${f}"`);const x=w.getBoundingClientRect(),S=c?x.width:x.height;return(g-m)/S*100}function VC(n,o,l,s,a,c){if(Gh(n)){const p=l==="horizontal";let f=0;n.shiftKey?f=100:a!=null?f=a:f=10;let m=0;switch(n.key){case"ArrowDown":m=p?0:f;break;case"ArrowLeft":m=p?-f:0;break;case"ArrowRight":m=p?f:0;break;case"ArrowUp":m=p?0:-f;break;case"End":m=100;break;case"Home":m=-100;break}return m}else return s==null?0:HC(n,o,l,s,c)}function BC({panelDataArray:n}){const o=Array(n.length),l=n.map(c=>c.constraints);let s=0,a=100;for(let c=0;c<n.length;c++){const p=l[c];be(p,`Panel constraints not found for index ${c}`);const{defaultSize:f}=p;f!=null&&(s++,o[c]=f,a-=f)}for(let c=0;c<n.length;c++){const p=l[c];be(p,`Panel constraints not found for index ${c}`);const{defaultSize:f}=p;if(f!=null)continue;const m=n.length-s,g=a/m;s++,o[c]=g,a-=g}return o}function Kr(n,o,l){o.forEach((s,a)=>{const c=n[a];be(c,`Panel data not found for index ${a}`);const{callbacks:p,constraints:f,id:m}=c,{collapsedSize:g=0,collapsible:w}=f,x=l[m];if(x==null||s!==x){l[m]=s;const{onCollapse:S,onExpand:E,onResize:b}=p;b&&b(s,x),w&&(S||E)&&(E&&(x==null||mn(x,g))&&!mn(s,g)&&E(),S&&(x==null||!mn(x,g))&&mn(s,g)&&S())}})}function Ri(n,o){if(n.length!==o.length)return!1;for(let l=0;l<n.length;l++)if(n[l]!=o[l])return!1;return!0}function UC({defaultSize:n,dragState:o,layout:l,panelData:s,panelIndex:a,precision:c=3}){const p=l[a];let f;return p==null?f=n!=null?n.toPrecision(c):"1":s.length===1?f="1":f=p.toPrecision(c),{flexBasis:0,flexGrow:f,flexShrink:1,overflow:"hidden",pointerEvents:o!==null?"none":void 0}}function KC(n,o=10){let l=null;return(...a)=>{l!==null&&clearTimeout(l),l=setTimeout(()=>{n(...a)},o)}}function Hp(n){try{if(typeof localStorage<"u")n.getItem=o=>localStorage.getItem(o),n.setItem=(o,l)=>{localStorage.setItem(o,l)};else throw new Error("localStorage not supported in this environment")}catch(o){console.error(o),n.getItem=()=>null,n.setItem=()=>{}}}function ig(n){return`react-resizable-panels:${n}`}function sg(n){return n.map(o=>{const{constraints:l,id:s,idIsFromProps:a,order:c}=o;return a?s:c?`${c}:${JSON.stringify(l)}`:JSON.stringify(l)}).sort((o,l)=>o.localeCompare(l)).join(",")}function ag(n,o){try{const l=ig(n),s=o.getItem(l);if(s){const a=JSON.parse(s);if(typeof a=="object"&&a!=null)return a}}catch{}return null}function QC(n,o,l){var s,a;const c=(s=ag(n,l))!==null&&s!==void 0?s:{},p=sg(o);return(a=c[p])!==null&&a!==void 0?a:null}function GC(n,o,l,s,a){var c;const p=ig(n),f=sg(o),m=(c=ag(n,a))!==null&&c!==void 0?c:{};m[f]={expandToSizes:Object.fromEntries(l.entries()),layout:s};try{a.setItem(p,JSON.stringify(m))}catch(g){console.error(g)}}function Vp({layout:n,panelConstraints:o}){const l=[...n],s=l.reduce((c,p)=>c+p,0);if(l.length!==o.length)throw Error(`Invalid ${o.length} panel layout: ${l.map(c=>`${c}%`).join(", ")}`);if(!Ct(s,100)&&l.length>0)for(let c=0;c<o.length;c++){const p=l[c];be(p!=null,`No layout data found for index ${c}`);const f=100/s*p;l[c]=f}let a=0;for(let c=0;c<o.length;c++){const p=l[c];be(p!=null,`No layout data found for index ${c}`);const f=Gr({panelConstraints:o,panelIndex:c,size:p});p!=f&&(a+=p-f,l[c]=f)}if(!Ct(a,0))for(let c=0;c<o.length;c++){const p=l[c];be(p!=null,`No layout data found for index ${c}`);const f=p+a,m=Gr({panelConstraints:o,panelIndex:c,size:f});if(p!==m&&(a-=m-p,l[c]=m,Ct(a,0)))break}return l}const YC=100,el={getItem:n=>(Hp(el),el.getItem(n)),setItem:(n,o)=>{Hp(el),el.setItem(n,o)}},Bp={};function ug({autoSaveId:n=null,children:o,className:l="",direction:s,forwardedRef:a,id:c=null,onLayout:p=null,keyboardResizeBy:f=null,storage:m=el,style:g,tagName:w="div",...x}){const S=Bu(c),E=ht(null),[b,C]=Jr(null),[P,N]=Jr([]),j=OC(),z=ht({}),M=ht(new Map),D=ht(0),V=ht({autoSaveId:n,direction:s,dragState:b,id:S,keyboardResizeBy:f,onLayout:p,storage:m}),O=ht({layout:P,panelDataArray:[],panelDataArrayChanged:!1});ht({didLogIdAndOrderWarning:!1,didLogPanelConstraintsWarning:!1,prevPanelIds:[]}),Uh(a,()=>({getId:()=>V.current.id,getLayout:()=>{const{layout:H}=O.current;return H},setLayout:H=>{const{onLayout:G}=V.current,{layout:ne,panelDataArray:te}=O.current,F=Vp({layout:H,panelConstraints:te.map(X=>X.constraints)});Wp(ne,F)||(N(F),O.current.layout=F,G&&G(F),Kr(te,F,z.current))}}),[]),dr(()=>{V.current.autoSaveId=n,V.current.direction=s,V.current.dragState=b,V.current.id=S,V.current.onLayout=p,V.current.storage=m}),WC({committedValuesRef:V,eagerValuesRef:O,groupId:S,layout:P,panelDataArray:O.current.panelDataArray,setLayout:N,panelGroupElement:E.current}),cr(()=>{const{panelDataArray:H}=O.current;if(n){if(P.length===0||P.length!==H.length)return;let G=Bp[n];G==null&&(G=KC(GC,YC),Bp[n]=G);const ne=[...H],te=new Map(M.current);G(n,ne,te,P,m)}},[n,P,m]),cr(()=>{});const ee=mt(H=>{const{onLayout:G}=V.current,{layout:ne,panelDataArray:te}=O.current;if(H.constraints.collapsible){const F=te.map(me=>me.constraints),{collapsedSize:X=0,panelSize:K,pivotIndices:oe}=ir(te,H,ne);if(be(K!=null,`Panel size not found for panel "${H.id}"`),!mn(K,X)){M.current.set(H.id,K);const ye=Qr(te,H)===te.length-1?K-X:X-K,Se=Jo({delta:ye,initialLayout:ne,panelConstraints:F,pivotIndices:oe,prevLayout:ne,trigger:"imperative-api"});Ri(ne,Se)||(N(Se),O.current.layout=Se,G&&G(Se),Kr(te,Se,z.current))}}},[]),re=mt((H,G)=>{const{onLayout:ne}=V.current,{layout:te,panelDataArray:F}=O.current;if(H.constraints.collapsible){const X=F.map(_e=>_e.constraints),{collapsedSize:K=0,panelSize:oe=0,minSize:me=0,pivotIndices:ye}=ir(F,H,te),Se=G??me;if(mn(oe,K)){const _e=M.current.get(H.id),st=_e!=null&&_e>=Se?_e:Se,vt=Qr(F,H)===F.length-1?oe-st:st-oe,Ue=Jo({delta:vt,initialLayout:te,panelConstraints:X,pivotIndices:ye,prevLayout:te,trigger:"imperative-api"});Ri(te,Ue)||(N(Ue),O.current.layout=Ue,ne&&ne(Ue),Kr(F,Ue,z.current))}}},[]),ae=mt(H=>{const{layout:G,panelDataArray:ne}=O.current,{panelSize:te}=ir(ne,H,G);return be(te!=null,`Panel size not found for panel "${H.id}"`),te},[]),pe=mt((H,G)=>{const{panelDataArray:ne}=O.current,te=Qr(ne,H);return UC({defaultSize:G,dragState:b,layout:P,panelData:ne,panelIndex:te})},[b,P]),q=mt(H=>{const{layout:G,panelDataArray:ne}=O.current,{collapsedSize:te=0,collapsible:F,panelSize:X}=ir(ne,H,G);return be(X!=null,`Panel size not found for panel "${H.id}"`),F===!0&&mn(X,te)},[]),ve=mt(H=>{const{layout:G,panelDataArray:ne}=O.current,{collapsedSize:te=0,collapsible:F,panelSize:X}=ir(ne,H,G);return be(X!=null,`Panel size not found for panel "${H.id}"`),!F||gr(X,te)>0},[]),se=mt(H=>{const{panelDataArray:G}=O.current;G.push(H),G.sort((ne,te)=>{const F=ne.order,X=te.order;return F==null&&X==null?0:F==null?-1:X==null?1:F-X}),O.current.panelDataArrayChanged=!0,j()},[j]);dr(()=>{if(O.current.panelDataArrayChanged){O.current.panelDataArrayChanged=!1;const{autoSaveId:H,onLayout:G,storage:ne}=V.current,{layout:te,panelDataArray:F}=O.current;let X=null;if(H){const oe=QC(H,F,ne);oe&&(M.current=new Map(Object.entries(oe.expandToSizes)),X=oe.layout)}X==null&&(X=BC({panelDataArray:F}));const K=Vp({layout:X,panelConstraints:F.map(oe=>oe.constraints)});Wp(te,K)||(N(K),O.current.layout=K,G&&G(K),Kr(F,K,z.current))}}),dr(()=>{const H=O.current;return()=>{H.layout=[]}},[]);const ge=mt(H=>{let G=!1;const ne=E.current;return ne&&window.getComputedStyle(ne,null).getPropertyValue("direction")==="rtl"&&(G=!0),function(F){F.preventDefault();const X=E.current;if(!X)return()=>null;const{direction:K,dragState:oe,id:me,keyboardResizeBy:ye,onLayout:Se}=V.current,{layout:_e,panelDataArray:st}=O.current,{initialLayout:gt}=oe??{},vt=rg(me,H,X);let Ue=VC(F,H,K,oe,ye,X);const Mt=K==="horizontal";Mt&&G&&(Ue=-Ue);const Kn=st.map(fl=>fl.constraints),kt=Jo({delta:Ue,initialLayout:gt??_e,panelConstraints:Kn,pivotIndices:vt,prevLayout:_e,trigger:Gh(F)?"keyboard":"mouse-or-touch"}),vr=!Ri(_e,kt);(Yh(F)||Xh(F))&&D.current!=Ue&&(D.current=Ue,!vr&&Ue!==0?Mt?su(H,Ue<0?qh:Jh):su(H,Ue<0?eg:tg):su(H,0)),vr&&(N(kt),O.current.layout=kt,Se&&Se(kt),Kr(st,kt,z.current))}},[]),le=mt((H,G)=>{const{onLayout:ne}=V.current,{layout:te,panelDataArray:F}=O.current,X=F.map(_e=>_e.constraints),{panelSize:K,pivotIndices:oe}=ir(F,H,te);be(K!=null,`Panel size not found for panel "${H.id}"`);const ye=Qr(F,H)===F.length-1?K-G:G-K,Se=Jo({delta:ye,initialLayout:te,panelConstraints:X,pivotIndices:oe,prevLayout:te,trigger:"imperative-api"});Ri(te,Se)||(N(Se),O.current.layout=Se,ne&&ne(Se),Kr(F,Se,z.current))},[]),ce=mt((H,G)=>{const{layout:ne,panelDataArray:te}=O.current,{collapsedSize:F=0,collapsible:X}=G,{collapsedSize:K=0,collapsible:oe,maxSize:me=100,minSize:ye=0}=H.constraints,{panelSize:Se}=ir(te,H,ne);Se!=null&&(X&&oe&&mn(Se,F)?mn(F,K)||le(H,K):Se<ye?le(H,ye):Se>me&&le(H,me))},[le]),$=mt((H,G)=>{const{direction:ne}=V.current,{layout:te}=O.current;if(!E.current)return;const F=ts(H,E.current);be(F,`Drag handle element not found for id "${H}"`);const X=lg(ne,G);C({dragHandleId:H,dragHandleRect:F.getBoundingClientRect(),initialCursorPosition:X,initialLayout:te})},[]),J=mt(()=>{C(null)},[]),Y=mt(H=>{const{panelDataArray:G}=O.current,ne=Qr(G,H);ne>=0&&(G.splice(ne,1),delete z.current[H.id],O.current.panelDataArrayChanged=!0,j())},[j]),_=EC(()=>({collapsePanel:ee,direction:s,dragState:b,expandPanel:re,getPanelSize:ae,getPanelStyle:pe,groupId:S,isPanelCollapsed:q,isPanelExpanded:ve,reevaluatePanelConstraints:ce,registerPanel:se,registerResizeHandle:ge,resizePanel:le,startDragging:$,stopDragging:J,unregisterPanel:Y,panelGroupElement:E.current}),[ee,b,s,re,ae,pe,S,q,ve,ce,se,ge,le,$,J,Y]),W={display:"flex",flexDirection:s==="horizontal"?"row":"column",height:"100%",overflow:"hidden",width:"100%"};return ro(qi.Provider,{value:_},ro(w,{...x,children:o,className:l,id:c,ref:E,style:{...W,...g},"data-panel-group":"","data-panel-group-direction":s,"data-panel-group-id":S}))}const cg=Vh((n,o)=>ro(ug,{...n,forwardedRef:o}));ug.displayName="PanelGroup";cg.displayName="forwardRef(PanelGroup)";function Qr(n,o){return n.findIndex(l=>l===o||l.id===o.id)}function ir(n,o,l){const s=Qr(n,o),c=s===n.length-1?[s-1,s]:[s,s+1],p=l[s];return{...o.constraints,panelSize:p,pivotIndices:c}}function XC({disabled:n,handleId:o,resizeHandler:l,panelGroupElement:s}){cr(()=>{if(n||l==null||s==null)return;const a=ts(o,s);if(a==null)return;const c=p=>{if(!p.defaultPrevented)switch(p.key){case"ArrowDown":case"ArrowLeft":case"ArrowRight":case"ArrowUp":case"End":case"Home":{p.preventDefault(),l(p);break}case"F6":{p.preventDefault();const f=a.getAttribute("data-panel-group-id");be(f,`No group element found for id "${f}"`);const m=il(f,s),g=ng(f,o,s);be(g!==null,`No resize element found for id "${o}"`);const w=p.shiftKey?g>0?g-1:m.length-1:g+1<m.length?g+1:0;m[w].focus();break}}};return a.addEventListener("keydown",c),()=>{a.removeEventListener("keydown",c)}},[s,n,o,l])}function dg({children:n=null,className:o="",disabled:l=!1,hitAreaMargins:s,id:a,onBlur:c,onDragging:p,onFocus:f,style:m={},tabIndex:g=0,tagName:w="div",...x}){var S,E;const b=ht(null),C=ht({onDragging:p});cr(()=>{C.current.onDragging=p});const P=Bh(qi);if(P===null)throw Error("PanelResizeHandle components must be rendered within a PanelGroup container");const{direction:N,groupId:j,registerResizeHandle:z,startDragging:M,stopDragging:D,panelGroupElement:V}=P,O=Bu(a),[ee,re]=Jr("inactive"),[ae,pe]=Jr(!1),[q,ve]=Jr(null),se=ht({state:ee});dr(()=>{se.current.state=ee}),cr(()=>{if(l)ve(null);else{const $=z(O);ve(()=>$)}},[l,O,z]);const ge=(S=s==null?void 0:s.coarse)!==null&&S!==void 0?S:15,le=(E=s==null?void 0:s.fine)!==null&&E!==void 0?E:5;return cr(()=>{if(l||q==null)return;const $=b.current;return be($,"Element ref not attached"),IC(O,$,N,{coarse:ge,fine:le},(Y,_,W)=>{if(_)switch(Y){case"down":{re("drag"),be(W,'Expected event to be defined for "down" action'),M(O,W);const{onDragging:H}=C.current;H&&H(!0);break}case"move":{const{state:H}=se.current;H!=="drag"&&re("hover"),be(W,'Expected event to be defined for "move" action'),q(W);break}case"up":{re("hover"),D();const{onDragging:H}=C.current;H&&H(!1);break}}else re("inactive")})},[ge,N,l,le,z,O,q,M,D]),XC({disabled:l,handleId:O,resizeHandler:q,panelGroupElement:V}),ro(w,{...x,children:n,className:o,id:a,onBlur:()=>{pe(!1),c==null||c()},onFocus:()=>{pe(!0),f==null||f()},ref:b,role:"separator",style:{...{touchAction:"none",userSelect:"none"},...m},tabIndex:g,"data-panel-group-direction":N,"data-panel-group-id":j,"data-resize-handle":"","data-resize-handle-active":ee==="drag"?"pointer":ae?"keyboard":void 0,"data-resize-handle-state":ee,"data-panel-resize-handle-enabled":!l,"data-panel-resize-handle-id":O})}dg.displayName="PanelResizeHandle";function ZC({className:n,...o}){return v.jsx(cg,{"data-slot":"resizable-panel-group",className:De("flex h-full w-full data-[panel-group-direction=vertical]:flex-col",n),...o})}function Up({...n}){return v.jsx(Qh,{"data-slot":"resizable-panel",...n})}function qC({withHandle:n,className:o,...l}){return v.jsx(dg,{"data-slot":"resizable-handle",className:De("bg-border focus-visible:ring-ring relative flex w-px items-center justify-center after:absolute after:inset-y-0 after:left-1/2 after:w-1 after:-translate-x-1/2 focus-visible:ring-1 focus-visible:ring-offset-1 focus-visible:outline-hidden data-[panel-group-direction=vertical]:h-px data-[panel-group-direction=vertical]:w-full data-[panel-group-direction=vertical]:after:left-0 data-[panel-group-direction=vertical]:after:h-1 data-[panel-group-direction=vertical]:after:w-full data-[panel-group-direction=vertical]:after:-translate-y-1/2 data-[panel-group-direction=vertical]:after:translate-x-0 [&[data-panel-group-direction=vertical]>div]:rotate-90",o),...l,children:n&&v.jsx("div",{className:"bg-border z-10 flex h-4 w-3 items-center justify-center rounded-xs border",children:v.jsx(Nx,{className:"size-2.5"})})})}function JC({title:n="Voice Interpretation App"}){return v.jsxs("div",{className:"h-8 bg-gray-50 border-b border-gray-200 flex items-center justify-center relative select-none",style:{WebkitAppRegion:"drag",paddingLeft:"70px"},children:[v.jsx("div",{className:"flex items-center gap-2",children:v.jsx("h1",{className:"text-xs font-medium text-gray-700",children:n})}),v.jsx("div",{className:"absolute left-0 top-0 w-16 h-full",style:{WebkitAppRegion:"no-drag"}})]})}var Kp={};function eE(){const[n,o]=h.useState({isRecording:!1,isLoading:!1,error:null,hasPermission:null}),l=h.useRef(null),s=h.useRef(null),a=h.useRef([]),c=h.useCallback(async()=>{try{o(w=>({...w,isLoading:!0,error:null}));const g=await window.electronAPI.checkAudioPermissions();o(w=>({...w,hasPermission:g.success?g.hasPermission:!1,isLoading:!1,error:g.success?null:g.message||"Failed to check permissions"}))}catch(g){console.error("Error checking permissions:",g),o(w=>({...w,hasPermission:!1,isLoading:!1,error:"Failed to check permissions"}))}},[]),p=h.useCallback(async()=>{try{o(w=>({...w,isLoading:!0,error:null}));const g=await window.electronAPI.requestAudioPermissions();o(w=>({...w,hasPermission:g.success?g.granted:!1,isLoading:!1,error:g.success?g.granted?null:"Permission denied":g.message||"Failed to request permissions"}))}catch(g){console.error("Error requesting permissions:",g),o(w=>({...w,hasPermission:!1,isLoading:!1,error:"Failed to request permissions"}))}},[]),f=h.useCallback(async()=>{try{o(b=>({...b,isLoading:!0,error:null})),n.hasPermission===null&&await c(),n.hasPermission===!1&&await p();const g=await window.electronAPI.checkAudioPermissions();if(!g.success||!g.hasPermission)throw new Error("Audio permissions not granted");const w=await window.electronAPI.enableLoopbackAudio();if(!w.success)throw new Error(w.message||"Failed to enable loopback audio");const x=await navigator.mediaDevices.getDisplayMedia({video:!0,audio:!0});x.getVideoTracks().forEach(b=>{b.stop(),x.removeTrack(b)}),l.current=x;const E=new MediaRecorder(x,{mimeType:"audio/webm;codecs=opus"});a.current=[],E.ondataavailable=b=>{b.data.size>0&&a.current.push(b.data)},E.start(1e3),s.current=E,o(b=>({...b,isRecording:!0,isLoading:!1,hasPermission:!0}))}catch(g){console.error("Error starting recording:",g);try{await window.electronAPI.disableLoopbackAudio()}catch(w){console.error("Error during cleanup:",w)}o(w=>({...w,isRecording:!1,isLoading:!1,error:g instanceof Error?g.message:"Failed to start recording"}))}},[n.hasPermission,c,p]),m=h.useCallback(async()=>{var g;try{if(o(w=>({...w,isLoading:!0,error:null})),!s.current||!l.current)throw new Error("No active recording found");return new Promise((w,x)=>{const S=s.current;S.onstop=async()=>{var E;try{const b=new Blob(a.current,{type:"audio/webm;codecs=opus"}),P=`system-audio-${new Date().toISOString().replace(/:/g,"-").replace(/\./g,"-")}.webm`,N=`${Kp.HOME||Kp.USERPROFILE}/Downloads/${P}`,j=await b.arrayBuffer(),z=await window.electronAPI.saveAudioFile(N,j);if(!z.success)throw new Error(z.message||"Failed to save audio file");(E=l.current)==null||E.getTracks().forEach(M=>M.stop()),l.current=null,s.current=null,a.current=[],await window.electronAPI.disableLoopbackAudio(),o(M=>({...M,isRecording:!1,isLoading:!1})),w(z.filePath||N)}catch(b){console.error("Error in onstop handler:",b),x(b)}},S.stop()})}catch(w){return console.error("Error stopping recording:",w),(g=l.current)==null||g.getTracks().forEach(x=>x.stop()),l.current=null,s.current=null,await window.electronAPI.disableLoopbackAudio(),o(x=>({...x,isRecording:!1,isLoading:!1,error:w instanceof Error?w.message:"Failed to stop recording"})),null}},[]);return{recordingState:n,startRecording:f,stopRecording:m,checkPermissions:c,requestPermissions:p}}function tE(){const{recordingState:n,startRecording:o,stopRecording:l,checkPermissions:s,requestPermissions:a}=eE(),[c,p]=h.useState("ko"),[f,m]=h.useState("en"),[g,w]=h.useState(!1),[x,S]=h.useState(!0),[E,b]=h.useState(!1),[C,P]=h.useState(!1),[N,j]=h.useState(!1),[z,M]=h.useState([]),[D,V]=h.useState(""),[O,ee]=h.useState([]),[re,ae]=h.useState([]),[pe,q]=h.useState(!1),[ve,se]=h.useState(!1);h.useEffect(()=>{M([]),ee([]),V(""),ae([]),s()},[s]);const ge=async()=>{if(n.isRecording){console.log("시스템 오디오 녹음 중지");try{const K=await l();K&&(console.log("녹음이 저장되었습니다:",K),alert(`녹음이 저장되었습니다: ${K}`))}catch(K){console.error("녹음 중지 실패:",K),alert(`녹음 중지 실패: ${K instanceof Error?K.message:"알 수 없는 오류"}`)}}else{if(console.log("시스템 오디오 녹음 시작"),n.hasPermission===!1){console.log("권한이 없습니다. 권한을 요청합니다."),await a();return}try{await o(),console.log("녹음이 성공적으로 시작되었습니다.")}catch(K){console.error("녹음 시작 실패:",K),alert(`녹음 시작 실패: ${K instanceof Error?K.message:"알 수 없는 오류"}`)}}},le=()=>{S(!x)},ce=()=>{b(!E)},$=()=>{P(!C)},J=()=>{j(!N)},Y=()=>new Date().toTimeString().slice(0,8),_=K=>K.map((oe,me)=>{if(oe.id.startsWith("debug-")){const Se=me===K.length-1&&K.filter(_e=>_e.id.startsWith("debug-")).length>0;return{...oe,isDebugLatest:Se}}return{...oe,isDebugLatest:!1}}),W=()=>{M([]),console.log("모든 번역 아이템이 삭제되었습니다.")},H=K=>{const oe={id:`debug-${Date.now()}`,timestamp:Y(),originalText:"",translatedText:"",originalLang:c.toUpperCase(),targetLang:f.toUpperCase(),confidence:"high",isDebugLatest:!0};M(me=>{const ye=[...me,oe];return _(ye)}),q(!0),console.log("빈 아이템이 추가되었습니다:",oe)},G=K=>{if(z.length===0){console.log("전사 추가 실패: 추가할 아이템이 없습니다. 먼저 '아이템 추가'를 클릭하세요.");return}if(!K.trim()){console.log("전사 추가 실패: 텍스트가 비어있습니다.");return}M(oe=>{const me=[...oe],ye=me.length-1;return me[ye]={...me[ye],originalText:K.trim()},_(me)}),console.log("전사가 마지막 아이템에 추가되었습니다:",K)},ne=K=>{if(z.length===0){console.log("번역 추가 실패: 추가할 아이템이 없습니다. 먼저 '아이템 추가'를 클릭하세요.");return}if(!K.trim()){console.log("번역 추가 실패: 텍스트가 비어있습니다.");return}M(oe=>{const me=[...oe],ye=me.length-1;return me[ye]={...me[ye],translatedText:K.trim()},_(me)}),console.log("번역이 마지막 아이템에 추가되었습니다:",K)},te=K=>{V(K),console.log("현재 주제가 변경되었습니다:",K)},F=()=>{ee([]),console.log("모든 구간별 요약이 삭제되었습니다.")},X=(K,oe,me)=>{const ye={id:`summary-${Date.now()}`,timeRange:K.trim(),summary:oe.trim(),keyPoints:me.filter(Se=>Se.trim()!=="")};ee(Se=>[...Se,ye]),se(!0),console.log("새 구간별 요약이 추가되었습니다:",ye)};return v.jsxs("div",{className:"h-screen flex flex-col bg-gray-50 overflow-hidden",children:[v.jsx(JC,{title:"Voice Interpretation App"}),v.jsx("div",{className:"flex-1 flex bg-gray-50 overflow-hidden",children:x?v.jsxs(ZC,{direction:"horizontal",className:"h-full w-full",children:[v.jsx(Up,{defaultSize:70,minSize:50,maxSize:85,children:v.jsxs("div",{className:"flex-1 flex flex-col min-h-0 overflow-hidden h-full",children:[v.jsx("div",{className:"flex-1 min-h-0 overflow-hidden",children:v.jsx(op,{translations:z,isSummaryPanelVisible:x,onSummaryPanelToggle:le,isQuestionHelperVisible:E,onQuestionHelperToggle:ce,scrollToBottom:pe,onScrolledToBottom:()=>q(!1)})}),v.jsx("div",{className:"shrink-0 overflow-visible",children:v.jsx(Mp,{isRecording:n.isRecording,onRecordingToggle:ge,sourceLang:c,targetLang:f,onSourceLangChange:p,onTargetLangChange:m,isOnlineMode:g,onModeToggle:()=>w(!g),suggestedQuestions:re,isQuestionHelperVisible:E,onQuestionHelperToggle:ce,isDebugMode:C,onDebugModeToggle:$,isSummaryDebugMode:N,onSummaryDebugModeToggle:J,onClearAllTranslations:W,onAddItem:H,onAddTranscription:G,onAddTranslation:ne,isLoading:n.isLoading,recordingError:n.error,hasPermission:n.hasPermission})})]})}),v.jsx(qC,{withHandle:!0,className:"bg-gray-200 hover:bg-gray-300 transition-colors duration-200"}),v.jsx(Up,{defaultSize:30,minSize:15,maxSize:50,children:v.jsx("div",{className:"h-full overflow-hidden",children:v.jsx(uw,{currentTopic:D,summaries:O,isSummaryDebugMode:N,onTopicChange:te,onClearAllSummaries:F,onAddSummary:X,summaryScrollToBottom:ve,onSummaryScrolledToBottom:()=>se(!1)})})})]}):v.jsxs("div",{className:"flex-1 flex flex-col min-h-0 overflow-hidden",children:[v.jsx("div",{className:"flex-1 min-h-0 overflow-hidden",children:v.jsx(op,{translations:z,isSummaryPanelVisible:x,onSummaryPanelToggle:le,isQuestionHelperVisible:E,onQuestionHelperToggle:ce,scrollToBottom:pe,onScrolledToBottom:()=>q(!1)})}),v.jsx("div",{className:"shrink-0 overflow-visible",children:v.jsx(Mp,{isRecording:n.isRecording,onRecordingToggle:ge,sourceLang:c,targetLang:f,onSourceLangChange:p,onTargetLangChange:m,isOnlineMode:g,onModeToggle:()=>w(!g),suggestedQuestions:re,isQuestionHelperVisible:E,onQuestionHelperToggle:ce,isDebugMode:C,onDebugModeToggle:$,isSummaryDebugMode:N,onSummaryDebugModeToggle:J,onClearAllTranslations:W,onAddItem:H,onAddTranscription:G,onAddTranslation:ne,isLoading:n.isLoading,recordingError:n.error,hasPermission:n.hasPermission})})]})})]})}gy.createRoot(document.getElementById("root")).render(v.jsx(h.StrictMode,{children:v.jsx(tE,{})}));
