import { useEffect, useRef, useState, useCallback } from "react";
import { ScrollArea } from "./ui/scroll-area";
import { Switch } from "./ui/switch";
import { Label } from "./ui/label";
import { Button } from "./ui/button";
import { ChevronDown } from "lucide-react";
import { TranslationBlock } from "./TranslationBlock";

interface Translation {
  id: string;
  timestamp: string;
  originalText: string;
  translatedText: string;
  originalLang: string;
  targetLang: string;
  confidence: 'high' | 'medium' | 'low';
  isDebugLatest?: boolean;
}

interface TranslationPanelProps {
  translations: Translation[];
  isSummaryPanelVisible: boolean;
  onSummaryPanelToggle: () => void;
  isQuestionHelperVisible: boolean;
  onQuestionHelperToggle: () => void;
  scrollToBottom?: boolean;
  onScrolledToBottom?: () => void;
}

export function TranslationPanel({ 
  translations,
  isSummaryPanelVisible,
  onSummaryPanelToggle,
  isQuestionHelperVisible,
  onQuestionHelperToggle,
  scrollToBottom,
  onScrolledToBottom
}: TranslationPanelProps) {
  const endOfListRef = useRef<HTMLDivElement>(null);
  const contentRef = useRef<HTMLDivElement>(null); // ScrollArea 대신 내부 content div에 ref
  const [showScrollButton, setShowScrollButton] = useState(false);

  const handlePlay = (id: string) => {
    const translation = translations.find(t => t.id === id);
    if (translation) {
      console.log(`Playing text: ${translation.originalText} in language: ${translation.originalLang}`);
      // 실제로는 TTS API 호출
    }
  };

  // 스크롤 위치 확인 함수
  const checkScrollPosition = useCallback(() => {
    if (!contentRef.current) {
      return;
    }
    
    // 부모 요소들을 탐색해서 스크롤 가능한 요소 찾기
    let scrollableElement = null;
    let currentElement = contentRef.current.parentElement;
    let level = 0;
    
    while (currentElement && level < 5) {
      if (currentElement.scrollHeight > currentElement.clientHeight) {
        scrollableElement = currentElement;
        break;
      }
      
      currentElement = currentElement.parentElement;
      level++;
    }
    
    if (scrollableElement) {
      const { scrollTop, scrollHeight, clientHeight } = scrollableElement;
      
      // 맨 아래인지 체크 (10px 오차 허용)
      const isAtBottom = (scrollTop + clientHeight) >= (scrollHeight - 10);
      const canScroll = scrollHeight > clientHeight;
      const shouldShow = canScroll && !isAtBottom;
      
      setShowScrollButton(shouldShow);
    } else {
      setShowScrollButton(false);
    }
  }, []);

  // 스크롤을 최하단으로 이동시키는 함수
  const scrollToBottomHandler = useCallback(() => {
    if (endOfListRef.current) {
      endOfListRef.current.scrollIntoView({ 
        behavior: 'smooth',
        block: 'end'
      });
    }
  }, []);

  // 스크롤을 최하단으로 이동시키는 함수 (props에서 오는 경우)
  useEffect(() => {
    if (scrollToBottom && endOfListRef.current) {
      endOfListRef.current.scrollIntoView({ 
        behavior: 'smooth',
        block: 'end'
      });
      // 스크롤 완료 후 상태 리셋
      setTimeout(() => {
        onScrolledToBottom?.();
      }, 500);
    }
  }, [scrollToBottom, onScrolledToBottom]);

  // 1초마다 스크롤 위치 체크 (setInterval 방식)
  useEffect(() => {
    // 1초마다 스크롤 위치 체크
    const intervalId = setInterval(() => {
      checkScrollPosition();
    }, 1000);
    
    // 초기 상태도 바로 확인
    checkScrollPosition();
    
    // cleanup
    return () => {
      clearInterval(intervalId);
    };
  }, [checkScrollPosition]);

  // translations 변경 시 스크롤 위치 재확인
  useEffect(() => {
    // 약간의 지연을 두어 DOM 업데이트 후 확인
    const timeoutId = setTimeout(checkScrollPosition, 100);
    return () => clearTimeout(timeoutId);
  }, [translations, checkScrollPosition]);

  return (
    <div className="flex-1 pt-4 px-4 flex flex-col h-full relative">
      {/* 헤더 영역 */}
      <div className="mb-4 shrink-0">
        <div className="flex items-start justify-between">
          {/* 좌측: 제목과 설명 */}
          <div>
            <h2 className="text-lg mb-1">실시간 통역</h2>
            <p className="text-sm text-muted-foreground">음성이 실시간으로 문단 단위로 번역되어 표시됩니다</p>
          </div>
          
          {/* 우측: 토글 스위치들 - 그룹핑된 배경과 정렬된 토글들 */}
          <div className="bg-gray-50 rounded-lg p-3 border border-gray-200 shadow-sm">
            <div className="flex flex-col gap-2.5">
              {/* 요약 패널 토글 */}
              <div className="flex items-center gap-3">
                <Label className="text-xs text-muted-foreground w-20 text-right">요약 패널:</Label>
                <div className="flex items-center gap-1">
                  <Label className="text-xs text-muted-foreground">숨김</Label>
                  <Switch 
                    checked={isSummaryPanelVisible} 
                    onCheckedChange={onSummaryPanelToggle}
                    className="data-[state=checked]:bg-blue-600 scale-75"
                  />
                  <Label className="text-xs text-muted-foreground">표시</Label>
                </div>
              </div>

              {/* 질문 도우미 토글 */}
              <div className="flex items-center gap-3">
                <Label className="text-xs text-muted-foreground w-20 text-right">질문 도우미:</Label>
                <div className="flex items-center gap-1">
                  <Label className="text-xs text-muted-foreground">숨김</Label>
                  <Switch 
                    checked={isQuestionHelperVisible} 
                    onCheckedChange={onQuestionHelperToggle}
                    className="data-[state=checked]:bg-purple-600 scale-75"
                  />
                  <Label className="text-xs text-muted-foreground">표시</Label>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      {/* 스크롤 영역 - 남은 공간을 모두 활용 */}
      <ScrollArea className="flex-1 min-h-0">
        <div ref={contentRef} className="space-y-4 pr-2">
          {translations.length === 0 ? (
            <div className="text-center py-12 text-muted-foreground">
              <p>마이크를 켜고 대화를 시작해주세요</p>
              <p className="text-xs mt-1">문단 단위로 통역 결과가 표시됩니다</p>
            </div>
          ) : (
            <>
              {translations.map((translation) => (
                <TranslationBlock
                  key={translation.id}
                  {...translation}
                  onPlay={handlePlay}
                />
              ))}
              {/* 스크롤 기준점을 위한 보이지 않는 div */}
              <div ref={endOfListRef} />
            </>
          )}
        </div>
      </ScrollArea>

      {/* 스크롤을 하단으로 이동시키는 Floating Button */}
      {showScrollButton && (
        <Button
          onClick={scrollToBottomHandler}
          className="absolute bottom-4 right-4 h-10 w-10 rounded-full shadow-lg bg-blue-600/80 hover:bg-blue-700/90 text-white border-0 transition-all duration-200 hover:scale-105 z-50 backdrop-blur-sm"
          size="icon"
          title="맨 아래로 스크롤"
        >
          <ChevronDown className="h-4 w-4" />
        </Button>
      )}
    </div>
  );
}