# Electron 앱 설정 완료

프론트엔드 프로젝트가 Electron으로 성공적으로 래핑되었습니다! 🎉

## 설치된 구성 요소

- ✅ Electron main process (`electron/main.ts`)
- ✅ Electron preload script (`electron/preload.ts`)
- ✅ TypeScript 설정 (`electron/tsconfig.json`)
- ✅ 빌드 스크립트 (`scripts/build-electron.js`)
- ✅ package.json 설정 (scripts, electron-builder 설정)
- ✅ Vite 설정 (Electron 호환)

## 사용 방법

### 개발 환경에서 실행
```bash
# Vite 개발 서버와 Electron을 동시에 실행
npm run electron:dev
```

### 프로덕션 빌드
```bash
# React 앱과 Electron을 빌드
npm run electron:build

# 배포 가능한 macOS 앱 생성 (.dmg, .zip)
npm run electron:pack
```

### 배포용 패키지 생성
```bash
# 배포용 패키지 생성 (퍼블리시 안함)
npm run electron:dist
```

## 주요 특징

### macOS 최적화
- **Native 메뉴바**: macOS 표준 메뉴 (File, Edit, View, Window)
- **타이틀바 스타일**: `hiddenInset`으로 macOS 네이티브 느낌
- **Universal Binary**: Intel과 Apple Silicon 모두 지원

### 보안 설정
- **Context Isolation**: 활성화됨
- **Node Integration**: 비활성화됨
- **Preload Script**: 안전한 API 노출

### 개발자 경험
- **Hot Reload**: 개발 중 실시간 리로드
- **DevTools**: 개발 환경에서 자동 열림
- **TypeScript**: 전체 프로젝트 TypeScript 지원

## 프로젝트 구조

```
your-project/
├── electron/
│   ├── main.ts          # Electron 메인 프로세스
│   ├── preload.ts       # 프리로드 스크립트
│   └── tsconfig.json    # Electron TypeScript 설정
├── scripts/
│   └── build-electron.js # 빌드 스크립트
├── dist/                # React 빌드 결과
├── dist-electron/       # Electron 빌드 결과
└── dist-electron-pack/  # 최종 앱 패키지
```

## 다음 단계

1. **아이콘 추가**: `assets/icon.icns` 파일을 추가하여 앱 아이콘 설정
2. **앱 서명**: macOS 배포를 위한 코드 서명 설정
3. **자동 업데이트**: electron-updater를 추가하여 자동 업데이트 기능 구현

## 문제 해결

### 빌드 실패 시
```bash
# 의존성 재설치
rm -rf node_modules package-lock.json
npm install

# 빌드 디렉토리 정리
rm -rf dist dist-electron dist-electron-pack
```

### 개발 서버 연결 실패 시
- Vite 개발 서버가 5173 포트에서 실행 중인지 확인
- 방화벽 설정 확인

## ✅ 설정 완료!

Electron 앱이 성공적으로 빌드되었습니다!

### 생성된 앱 실행 방법

1. **개발 환경에서 실행**:
```bash
npm run electron:dev
```

2. **빌드된 macOS 앱 실행**:
```bash
open "dist-electron-pack/mac/Voice Interpretation App.app"
```
또는 Finder에서 `dist-electron-pack/mac/Voice Interpretation App.app`을 더블클릭

### 배포

빌드된 앱은 `dist-electron-pack/mac/Voice Interpretation App.app`에 있으며, 이를 다른 macOS 기기에 복사하여 실행할 수 있습니다.

**참고**: 현재 개발자 서명이 적용되어 있어 Gatekeeper 경고가 나타날 수 있습니다. 이는 정상이며, "시스템 설정 > 개인정보 보호 및 보안"에서 앱 실행을 허용하거나, 터미널에서 `xattr -cr "Voice Interpretation App.app"` 명령어로 격리 속성을 제거할 수 있습니다. 