import { useState, useEffect } from "react";
import { TranslationPanel } from "./components/TranslationPanel";
import { SummaryPanel } from "./components/SummaryPanel";
import { ControlPanel } from "./components/ControlPanel";
import { ResizablePanelGroup, ResizablePanel, ResizableHandle } from "./components/ui/resizable";
import { TitleBar } from "./components/TitleBar";
import { useSystemAudioRecording } from "./hooks/useSystemAudioRecording";

interface Translation {
  id: string;
  timestamp: string;
  originalText: string;
  translatedText: string;
  originalLang: string;
  targetLang: string;
  confidence: 'high' | 'medium' | 'low';
  isDebugLatest?: boolean; // 디버그 모드에서 가장 최근 아이템인지 표시
}

interface SummaryItem {
  id: string;
  timeRange: string;
  summary: string;
  keyPoints: string[];
}

export default function App() {
  // 시스템 오디오 녹음 Hook
  const { recordingState, startRecording, stopRecording, checkPermissions, requestPermissions } = useSystemAudioRecording();

  // 상태 관리
  const [sourceLang, setSourceLang] = useState('ko');
  const [targetLang, setTargetLang] = useState('en');
  const [isOnlineMode, setIsOnlineMode] = useState(false);
  const [isSummaryPanelVisible, setIsSummaryPanelVisible] = useState(true);
  const [isQuestionHelperVisible, setIsQuestionHelperVisible] = useState(false);
  const [isDebugMode, setIsDebugMode] = useState(false);
  const [isSummaryDebugMode, setIsSummaryDebugMode] = useState(false);
  const [translations, setTranslations] = useState<Translation[]>([]);
  const [currentTopic, setCurrentTopic] = useState("");
  const [summaries, setSummaries] = useState<SummaryItem[]>([]);
  const [suggestedQuestions, setSuggestedQuestions] = useState<string[]>([]);
  const [scrollToBottom, setScrollToBottom] = useState(false);
  const [summaryScrollToBottom, setSummaryScrollToBottom] = useState(false);

  // 초기 데이터 설정 (모든 데이터를 빈 상태로 시작)
  useEffect(() => {
    // 모든 데이터를 빈 상태로 초기화
    setTranslations([]);
    setSummaries([]);
    setCurrentTopic("");
    setSuggestedQuestions([]);

    // 앱 시작 시 오디오 권한 확인
    checkPermissions();
  }, [checkPermissions]);

  const handleRecordingToggle = async () => {
    if (!recordingState.isRecording) {
      console.log("시스템 오디오 녹음 시작");

      // 권한이 없는 경우 권한 요청
      if (recordingState.hasPermission === false) {
        console.log("권한이 없습니다. 권한을 요청합니다.");
        await requestPermissions();
        return;
      }

      try {
        await startRecording();
        console.log("녹음이 성공적으로 시작되었습니다.");
      } catch (error) {
        console.error("녹음 시작 실패:", error);
        alert(`녹음 시작 실패: ${error instanceof Error ? error.message : '알 수 없는 오류'}`);
      }
    } else {
      console.log("시스템 오디오 녹음 중지");
      try {
        const filePath = await stopRecording();
        if (filePath) {
          console.log("녹음이 저장되었습니다:", filePath);
          alert(`녹음이 저장되었습니다: ${filePath}`);
        }
      } catch (error) {
        console.error("녹음 중지 실패:", error);
        alert(`녹음 중지 실패: ${error instanceof Error ? error.message : '알 수 없는 오류'}`);
      }
    }
  };

  const handleSummaryPanelToggle = () => {
    setIsSummaryPanelVisible(!isSummaryPanelVisible);
  };

  const handleQuestionHelperToggle = () => {
    setIsQuestionHelperVisible(!isQuestionHelperVisible);
  };

  const handleDebugModeToggle = () => {
    setIsDebugMode(!isDebugMode);
  };

  const handleSummaryDebugModeToggle = () => {
    setIsSummaryDebugMode(!isSummaryDebugMode);
  };

  // 현재 시간을 HH:MM:SS 형식으로 포맷하는 함수
  const getCurrentTimestamp = () => {
    const now = new Date();
    return now.toTimeString().slice(0, 8);
  };

  // 디버그 아이템들의 isDebugLatest 상태를 업데이트하는 함수
  const updateDebugLatestStatus = (translationsList: Translation[]) => {
    return translationsList.map((translation, index) => {
      const isDebugItem = translation.id.startsWith('debug-');
      if (isDebugItem) {
        // 디버그 아이템 중에서 마지막 아이템인지 확인
        const isLastDebugItem = index === translationsList.length - 1 &&
          translationsList.filter(t => t.id.startsWith('debug-')).length > 0;
        return {
          ...translation,
          isDebugLatest: isLastDebugItem
        };
      }
      return {
        ...translation,
        isDebugLatest: false
      };
    });
  };

  // 디버그 패널 핸들러 함수들
  const handleClearAllTranslations = () => {
    setTranslations([]);
    console.log("모든 번역 아이템이 삭제되었습니다.");
  };

  const handleAddItem = (text: string) => {
    const newItem: Translation = {
      id: `debug-${Date.now()}`,
      timestamp: getCurrentTimestamp(),
      originalText: '',
      translatedText: '',
      originalLang: sourceLang.toUpperCase(),
      targetLang: targetLang.toUpperCase(),
      confidence: 'high',
      isDebugLatest: true
    };

    setTranslations(prev => {
      const updatedList = [...prev, newItem];
      return updateDebugLatestStatus(updatedList);
    });
    setScrollToBottom(true);
    console.log("빈 아이템이 추가되었습니다:", newItem);
  };

  const handleAddTranscription = (text: string) => {
    if (translations.length === 0) {
      console.log("전사 추가 실패: 추가할 아이템이 없습니다. 먼저 '아이템 추가'를 클릭하세요.");
      return;
    }

    if (!text.trim()) {
      console.log("전사 추가 실패: 텍스트가 비어있습니다.");
      return;
    }

    setTranslations(prev => {
      const newTranslations = [...prev];
      const lastIndex = newTranslations.length - 1;

      // 마지막 아이템의 originalText 업데이트
      newTranslations[lastIndex] = {
        ...newTranslations[lastIndex],
        originalText: text.trim()
      };

      return updateDebugLatestStatus(newTranslations);
    });

    // 기존 아이템 업데이트 시에는 스크롤하지 않음
    // setScrollToBottom(true);
    console.log("전사가 마지막 아이템에 추가되었습니다:", text);
  };

  const handleAddTranslation = (text: string) => {
    if (translations.length === 0) {
      console.log("번역 추가 실패: 추가할 아이템이 없습니다. 먼저 '아이템 추가'를 클릭하세요.");
      return;
    }

    if (!text.trim()) {
      console.log("번역 추가 실패: 텍스트가 비어있습니다.");
      return;
    }

    setTranslations(prev => {
      const newTranslations = [...prev];
      const lastIndex = newTranslations.length - 1;

      // 마지막 아이템의 translatedText 업데이트
      newTranslations[lastIndex] = {
        ...newTranslations[lastIndex],
        translatedText: text.trim()
      };

      return updateDebugLatestStatus(newTranslations);
    });

    // 기존 아이템 업데이트 시에는 스크롤하지 않음
    // setScrollToBottom(true);
    console.log("번역이 마지막 아이템에 추가되었습니다:", text);
  };

  const handleTopicChange = (newTopic: string) => {
    setCurrentTopic(newTopic);
    console.log("현재 주제가 변경되었습니다:", newTopic);
  };

  // 요약 패널 디버그 핸들러 함수들
  const handleClearAllSummaries = () => {
    setSummaries([]);
    console.log("모든 구간별 요약이 삭제되었습니다.");
  };

  const handleAddSummary = (timeRange: string, summary: string, keyPoints: string[]) => {
    const newSummary: SummaryItem = {
      id: `summary-${Date.now()}`,
      timeRange: timeRange.trim(),
      summary: summary.trim(),
      keyPoints: keyPoints.filter(point => point.trim() !== '')
    };

    setSummaries(prev => [...prev, newSummary]);
    setSummaryScrollToBottom(true);
    console.log("새 구간별 요약이 추가되었습니다:", newSummary);
  };

  return (
    <div className="h-screen flex flex-col bg-gray-50 overflow-hidden">
      {/* Title Bar */}
      <TitleBar title="Voice Interpretation App" />
      {/* Main Content */}
      <div className="flex-1 flex bg-gray-50 overflow-hidden">
        {isSummaryPanelVisible ? (
          /* Resizable layout when summary panel is visible */
          <ResizablePanelGroup direction="horizontal" className="h-full w-full">
            {/* 좌측: 실시간 통역 패널 + 제어 패널 */}
            <ResizablePanel defaultSize={70} minSize={50} maxSize={85}>
              <div className="flex-1 flex flex-col min-h-0 overflow-hidden h-full">
                {/* 실시간 통역 패널 */}
                <div className="flex-1 min-h-0 overflow-hidden">
                  <TranslationPanel
                    translations={translations}
                    isSummaryPanelVisible={isSummaryPanelVisible}
                    onSummaryPanelToggle={handleSummaryPanelToggle}
                    isQuestionHelperVisible={isQuestionHelperVisible}
                    onQuestionHelperToggle={handleQuestionHelperToggle}
                    scrollToBottom={scrollToBottom}
                    onScrolledToBottom={() => setScrollToBottom(false)}
                  />
                </div>

                {/* 제어 패널 */}
                <div className="shrink-0 overflow-visible">
                  <ControlPanel
                    isRecording={recordingState.isRecording}
                    onRecordingToggle={handleRecordingToggle}
                    sourceLang={sourceLang}
                    targetLang={targetLang}
                    onSourceLangChange={setSourceLang}
                    onTargetLangChange={setTargetLang}
                    isOnlineMode={isOnlineMode}
                    onModeToggle={() => setIsOnlineMode(!isOnlineMode)}
                    suggestedQuestions={suggestedQuestions}
                    isQuestionHelperVisible={isQuestionHelperVisible}
                    onQuestionHelperToggle={handleQuestionHelperToggle}
                    isDebugMode={isDebugMode}
                    onDebugModeToggle={handleDebugModeToggle}
                    isSummaryDebugMode={isSummaryDebugMode}
                    onSummaryDebugModeToggle={handleSummaryDebugModeToggle}
                    onClearAllTranslations={handleClearAllTranslations}
                    onAddItem={handleAddItem}
                    onAddTranscription={handleAddTranscription}
                    onAddTranslation={handleAddTranslation}
                  />
                </div>
              </div>
            </ResizablePanel>

            {/* Resizable handle */}
            <ResizableHandle
              withHandle
              className="bg-gray-200 hover:bg-gray-300 transition-colors duration-200"
            />

            {/* 우측: 요약 및 주제 패널 */}
            <ResizablePanel defaultSize={30} minSize={15} maxSize={50}>
              <div className="h-full overflow-hidden">
                <SummaryPanel
                  currentTopic={currentTopic}
                  summaries={summaries}
                  isSummaryDebugMode={isSummaryDebugMode}
                  onTopicChange={handleTopicChange}
                  onClearAllSummaries={handleClearAllSummaries}
                  onAddSummary={handleAddSummary}
                  summaryScrollToBottom={summaryScrollToBottom}
                  onSummaryScrolledToBottom={() => setSummaryScrollToBottom(false)}
                />
              </div>
            </ResizablePanel>
          </ResizablePanelGroup>
        ) : (
          /* Full width layout when summary panel is hidden */
          <div className="flex-1 flex flex-col min-h-0 overflow-hidden">
            {/* 실시간 통역 패널 */}
            <div className="flex-1 min-h-0 overflow-hidden">
              <TranslationPanel
                translations={translations}
                isSummaryPanelVisible={isSummaryPanelVisible}
                onSummaryPanelToggle={handleSummaryPanelToggle}
                isQuestionHelperVisible={isQuestionHelperVisible}
                onQuestionHelperToggle={handleQuestionHelperToggle}
                scrollToBottom={scrollToBottom}
                onScrolledToBottom={() => setScrollToBottom(false)}
              />
            </div>

            {/* 제어 패널 */}
            <div className="shrink-0 overflow-visible">
              <ControlPanel
                isRecording={recordingState.isRecording}
                onRecordingToggle={handleRecordingToggle}
                sourceLang={sourceLang}
                targetLang={targetLang}
                onSourceLangChange={setSourceLang}
                onTargetLangChange={setTargetLang}
                isOnlineMode={isOnlineMode}
                onModeToggle={() => setIsOnlineMode(!isOnlineMode)}
                suggestedQuestions={suggestedQuestions}
                isQuestionHelperVisible={isQuestionHelperVisible}
                onQuestionHelperToggle={handleQuestionHelperToggle}
                isDebugMode={isDebugMode}
                onDebugModeToggle={handleDebugModeToggle}
                isSummaryDebugMode={isSummaryDebugMode}
                onSummaryDebugModeToggle={handleSummaryDebugModeToggle}
                onClearAllTranslations={handleClearAllTranslations}
                onAddItem={handleAddItem}
                onAddTranscription={handleAddTranscription}
                onAddTranslation={handleAddTranslation}
              />
            </div>
          </div>
        )}
      </div>
    </div>
  );
}