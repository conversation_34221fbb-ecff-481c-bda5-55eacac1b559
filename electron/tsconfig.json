{"compilerOptions": {"target": "ES2020", "module": "ESNext", "lib": ["ES2020"], "allowJs": true, "outDir": "../dist-electron", "strict": true, "noImplicitReturns": true, "noFallthroughCasesInSwitch": true, "moduleResolution": "node", "allowSyntheticDefaultImports": true, "esModuleInterop": true, "experimentalDecorators": true, "emitDecoratorMetadata": true, "forceConsistentCasingInFileNames": true, "useDefineForClassFields": true, "skipLibCheck": true, "types": ["node"]}, "include": ["**/*"], "exclude": ["node_modules", "dist"]}