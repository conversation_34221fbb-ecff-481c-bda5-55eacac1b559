directories:
  output: dist-electron-pack
  buildResources: build
appId: com.voiceinterpretation.app
productName: Voice Interpretation App
files:
  - filter:
      - dist/**/*
      - dist-electron/**/*
      - node_modules/**/*
mac:
  category: public.app-category.productivity
  icon: assets/icon.icns
  identity: null
  target:
    - target: dmg
      arch:
        - x64
        - arm64
    - target: zip
      arch:
        - x64
        - arm64
dmg:
  contents:
    - x: 110
      'y': 150
    - x: 240
      'y': 150
      type: link
      path: /Applications
extraMetadata:
  main: dist-electron/main.js
electronVersion: 33.4.11
