// Electron API types for renderer process
export interface ElectronAPI {
  platform: string;
  versions: NodeJS.ProcessVersions;
  
  // Audio recording APIs
  checkAudioPermissions: () => Promise<{
    success: boolean;
    hasPermission: boolean;
    message?: string;
  }>;
  
  requestAudioPermissions: () => Promise<{
    success: boolean;
    granted: boolean;
    message?: string;
  }>;
  
  enableLoopbackAudio: () => Promise<{
    success: boolean;
    message: string;
  }>;
  
  disableLoopbackAudio: () => Promise<{
    success: boolean;
    message: string;
  }>;
  
  saveAudioFile: (filePath: string, arrayBuffer: ArrayBuffer) => Promise<{
    success: boolean;
    filePath?: string;
    message: string;
  }>;
}

declare global {
  interface Window {
    electronAPI: ElectronAPI;
  }
}
