import { useState } from "react";
import { Trash2, Plus, FileText, Languages } from "lucide-react";
import { Input } from "./ui/input";
import { Button } from "./ui/button";
import { Card, CardContent } from "./ui/card";

interface DebugPanelProps {
  onClearAll?: () => void;
  onAddItem?: (text: string) => void;
  onAddTranscription?: (text: string) => void;
  onAddTranslation?: (text: string) => void;
}

export function DebugPanel({
  onClearAll,
  onAddItem,
  onAddTranscription,
  onAddTranslation
}: DebugPanelProps) {
  const [inputText, setInputText] = useState("");

  const handleClearAll = () => {
    console.log("Debug: 전체 아이템 지우기");
    onClearAll?.();
  };

  const handleAddItem = () => {
    console.log("Debug: 빈 아이템 추가");
    onAddItem?.(inputText);
  };

  const handleAddTranscription = () => {
    console.log("Debug: 전사 추가 -", inputText);
    onAddTranscription?.(inputText);
  };

  const handleAddTranslation = () => {
    console.log("Debug: 번역 추가 -", inputText);
    onAddTranslation?.(inputText);
  };

  return (
    <Card className="border-orange-200 bg-orange-50/50 shadow-sm">
      <CardContent className="p-2 space-y-1">
        {/* 텍스트 입력 필드 */}
        <Input
          value={inputText}
          onChange={(e) => setInputText(e.target.value)}
          placeholder="테스트 텍스트 입력..."
          className="border-orange-200 focus:border-orange-400 bg-white h-7 text-xs"
        />

        {/* 버튼 그룹 */}
        <div className="grid grid-cols-2 gap-1">
          {/* 전체 아이템 지우기 */}
          <Button
            onClick={handleClearAll}
            variant="destructive"
            size="sm"
            className="text-xs h-6 bg-red-500 hover:bg-red-600 px-2"
          >
            <Trash2 size={10} className="mr-1" />
            전체 지우기
          </Button>

          {/* 아이템 추가 */}
          <Button
            onClick={handleAddItem}
            variant="outline"
            size="sm"
            className="text-xs h-6 border-orange-300 text-orange-700 hover:bg-orange-100 px-2"
          >
            <Plus size={10} className="mr-1" />
            아이템 추가
          </Button>

          {/* 전사 추가 */}
          <Button
            onClick={handleAddTranscription}
            disabled={!inputText.trim()}
            variant="outline"
            size="sm"
            className="text-xs h-6 border-blue-300 text-blue-700 hover:bg-blue-100 px-2"
          >
            <FileText size={10} className="mr-1" />
            전사 추가
          </Button>

          {/* 번역 추가 */}
          <Button
            onClick={handleAddTranslation}
            disabled={!inputText.trim()}
            variant="outline"
            size="sm"
            className="text-xs h-6 border-green-300 text-green-700 hover:bg-green-100 px-2"
          >
            <Languages size={10} className="mr-1" />
            번역 추가
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}