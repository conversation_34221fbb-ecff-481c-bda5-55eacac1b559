import { app, BrowserWindow, Menu, shell, ipcMain } from 'electron';
import path from 'path';
import { fileURLToPath } from 'url';
import {
  initAudioSystem,
  checkAudioPermissions,
  requestAudioPermissions,
  enableLoopbackAudio,
  disableLoopbackAudio,
  getRecordingStatus
} from './audio-recorder.js';
import { ipcEvents } from './audio-config.js';
import fs from 'fs';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Keep a global reference of the window object
let mainWindow: BrowserWindow | null = null;

const isDevelopment = process.env.NODE_ENV === 'development';

function createWindow(): void {
  // Create the browser window
  mainWindow = new BrowserWindow({
    width: 1200,
    height: 800,
    minWidth: 800,
    minHeight: 600,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      preload: path.join(__dirname, 'preload.js')
    },
    titleBarStyle: 'hiddenInset', // macOS style title bar
    trafficLightPosition: { x: 16, y: 12 }, // Position of window controls
    show: false, // Don't show until ready-to-show
  });

  // Load the app
  if (isDevelopment) {
    mainWindow.loadURL('http://localhost:5173');
    // Open DevTools in development
    mainWindow.webContents.openDevTools();
  } else {
    mainWindow.loadFile(path.join(__dirname, '../dist/index.html'));
  }

  // Show window when ready to prevent visual flash
  mainWindow.once('ready-to-show', () => {
    mainWindow?.show();
    
    if (isDevelopment) {
      mainWindow?.focus();
    }
  });

  // Handle window closed
  mainWindow.on('closed', () => {
    mainWindow = null;
  });

  // Handle external links
  mainWindow.webContents.setWindowOpenHandler(({ url }) => {
    shell.openExternal(url);
    return { action: 'deny' };
  });
}

// Initialize audio system before app is ready
initAudioSystem();

// Setup IPC handlers for audio functionality
setupAudioIpcHandlers();

// This method will be called when Electron has finished initialization
app.whenReady().then(() => {
  createWindow();

  // On macOS it's common to re-create a window in the app when the
  // dock icon is clicked and there are no other windows open.
  app.on('activate', () => {
    if (BrowserWindow.getAllWindows().length === 0) {
      createWindow();
    }
  });

  // Set up the menu
  if (process.platform === 'darwin') {
    const template = [
      {
        label: app.getName(),
        submenu: [
          { role: 'about' },
          { type: 'separator' },
          { role: 'services' },
          { type: 'separator' },
          { role: 'hide' },
          { role: 'hideothers' },
          { role: 'unhide' },
          { type: 'separator' },
          { role: 'quit' }
        ]
      },
      {
        label: 'Edit',
        submenu: [
          { role: 'undo' },
          { role: 'redo' },
          { type: 'separator' },
          { role: 'cut' },
          { role: 'copy' },
          { role: 'paste' }
        ]
      },
      {
        label: 'View',
        submenu: [
          { role: 'reload' },
          { role: 'forceReload' },
          { role: 'toggleDevTools' },
          { type: 'separator' },
          { role: 'resetZoom' },
          { role: 'zoomIn' },
          { role: 'zoomOut' },
          { type: 'separator' },
          { role: 'togglefullscreen' }
        ]
      },
      {
        label: 'Window',
        submenu: [
          { role: 'minimize' },
          { role: 'close' }
        ]
      }
    ];

    const menu = Menu.buildFromTemplate(template as any);
    Menu.setApplicationMenu(menu);
  }
});

// Quit when all windows are closed, except on macOS
app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

app.on('activate', () => {
  if (mainWindow === null) {
    createWindow();
  }
});

// Security: Prevent new window creation
app.on('web-contents-created', (event, contents) => {
  contents.setWindowOpenHandler(({ url }) => {
    shell.openExternal(url);
    return { action: 'deny' };
  });
});

// Setup IPC handlers for audio functionality
function setupAudioIpcHandlers(): void {
  // Check audio permissions
  ipcMain.handle(ipcEvents.checkPermissions, async () => {
    try {
      const hasPermission = await checkAudioPermissions();
      return {
        success: true,
        hasPermission
      };
    } catch (error) {
      console.error('Error checking permissions:', error);
      return {
        success: false,
        hasPermission: false,
        message: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  });

  // Request audio permissions
  ipcMain.handle(ipcEvents.requestPermissions, async () => {
    try {
      const granted = await requestAudioPermissions();
      return {
        success: true,
        granted
      };
    } catch (error) {
      console.error('Error requesting permissions:', error);
      return {
        success: false,
        granted: false,
        message: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  });

  // Enable loopback audio
  ipcMain.handle(ipcEvents.enableLoopbackAudio, async () => {
    try {
      await enableLoopbackAudio();
      return {
        success: true,
        message: 'Loopback audio enabled successfully'
      };
    } catch (error) {
      console.error('Error enabling loopback audio:', error);
      return {
        success: false,
        message: error instanceof Error ? error.message : 'Failed to enable loopback audio'
      };
    }
  });

  // Disable loopback audio
  ipcMain.handle(ipcEvents.disableLoopbackAudio, async () => {
    try {
      disableLoopbackAudio();
      return {
        success: true,
        message: 'Loopback audio disabled successfully'
      };
    } catch (error) {
      console.error('Error disabling loopback audio:', error);
      return {
        success: false,
        message: error instanceof Error ? error.message : 'Failed to disable loopback audio'
      };
    }
  });

  // Save audio file
  ipcMain.handle(ipcEvents.saveAudioFile, async (_, filePath: string, arrayBuffer: ArrayBuffer) => {
    try {
      const buffer = Buffer.from(arrayBuffer);

      // Ensure directory exists
      const dir = path.dirname(filePath);
      if (!fs.existsSync(dir)) {
        fs.mkdirSync(dir, { recursive: true });
      }

      // Write file
      fs.writeFileSync(filePath, buffer);

      console.log('Audio file saved successfully:', filePath);
      return {
        success: true,
        filePath,
        message: 'Audio file saved successfully'
      };
    } catch (error) {
      console.error('Error saving audio file:', error);
      return {
        success: false,
        message: error instanceof Error ? error.message : 'Failed to save audio file'
      };
    }
  });

  console.log('Audio IPC handlers registered successfully');
}