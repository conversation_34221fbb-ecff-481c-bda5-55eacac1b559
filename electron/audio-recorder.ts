import { app, session, desktopCapturer, systemPreferences } from 'electron';
import { type DesktopCapturerSource } from 'electron/main';
import { buildFeatureFlags, defaultSourcesOptions, featureSwitchKey, type InitAudioOptions } from './audio-config.js';
import fs from 'fs';
import path from 'path';
import os from 'os';

let isAudioEnabled = false;
let currentRecording: any = null;
let recordedChunks: any[] = [];

export const initAudioSystem = (options: InitAudioOptions = {}): void => {
    const {
        sourcesOptions = defaultSourcesOptions,
        forceCoreAudioTap = false,
    } = options;

    // Get other enabled features from the command line.
    const otherEnabledFeatures = app.commandLine.getSwitchValue(featureSwitchKey)?.split(',');

    // Remove the switch if it exists.
    if (app.commandLine.hasSwitch(featureSwitchKey)) {
        app.commandLine.removeSwitch(featureSwitchKey);
    }

    // Add the feature flags to the command line with any other user-enabled features concatenated.
    const currentFeatureFlags = buildFeatureFlags({
        otherEnabledFeatures,
        forceCoreAudioTap,
    });

    app.commandLine.appendSwitch(featureSwitchKey, currentFeatureFlags);
    console.log('Audio system initialized with flags:', currentFeatureFlags);
};

export const checkAudioPermissions = async (): Promise<boolean> => {
    try {
        // macOS에서는 microphone 권한을 확인
        if (process.platform === 'darwin') {
            const microphonePermission = systemPreferences.getMediaAccessStatus('microphone');
            
            console.log('Permissions status:', {
                microphone: microphonePermission,
            });

            return microphonePermission === 'granted';
        }
        
        // 다른 플랫폼에서는 기본적으로 true 반환
        return true;
    } catch (error) {
        console.error('Error checking audio permissions:', error);
        return false;
    }
};

export const requestAudioPermissions = async (): Promise<boolean> => {
    try {
        if (process.platform === 'darwin') {
            // macOS에서 권한 요청
            const micResult = await systemPreferences.askForMediaAccess('microphone');
            
            console.log('Permission request results:', {
                microphone: micResult,
            });

            return micResult;
        }
        
        return true;
    } catch (error) {
        console.error('Error requesting audio permissions:', error);
        return false;
    }
};

export const enableLoopbackAudio = async (): Promise<void> => {
    try {
        session.defaultSession.setDisplayMediaRequestHandler(async (_, callback) => {
            let sources: DesktopCapturerSource[];

            try {
                sources = await desktopCapturer.getSources(defaultSourcesOptions);
            } catch (error) {
                console.error('Failed to get sources for system audio loopback capture:', error);
                throw new Error(`Failed to get sources for system audio loopback capture.`);
            }

            if (sources.length === 0) {
                throw new Error(`No sources found for system audio loopback capture.`);
            }

            callback({ video: sources[0], audio: 'loopback' });
        });
        
        isAudioEnabled = true;
        console.log('Loopback audio enabled successfully');
    } catch (error) {
        console.error('Error enabling loopback audio:', error);
        throw error;
    }
};

export const disableLoopbackAudio = (): void => {
    session.defaultSession.setDisplayMediaRequestHandler(null);
    isAudioEnabled = false;
    console.log('Loopback audio disabled');
};

export const startRecording = async (): Promise<{ success: boolean; message: string }> => {
    try {
        if (!isAudioEnabled) {
            throw new Error('Loopback audio is not enabled. Call enableLoopbackAudio first.');
        }

        if (currentRecording) {
            throw new Error('Recording is already in progress');
        }

        // Reset recorded chunks
        recordedChunks = [];

        // 녹음 시작을 위한 플래그 설정
        currentRecording = { status: 'recording' };
        console.log('Recording started successfully');
        
        return {
            success: true,
            message: 'Recording started successfully'
        };
    } catch (error) {
        console.error('Error starting recording:', error);
        return {
            success: false,
            message: error instanceof Error ? error.message : 'Unknown error occurred'
        };
    }
};

export const stopRecording = async (): Promise<{ success: boolean; filePath?: string; message: string }> => {
    try {
        if (!currentRecording) {
            return {
                success: false,
                message: 'No recording in progress'
            };
        }

        // 녹음 중지 로직 구현
        currentRecording = null;

        // 파일 저장 경로 생성
        const timestamp = new Date().toISOString().replace(/:/g, '-').replace(/\./g, '-');
        const fileName = `system-audio-${timestamp}.webm`;
        const downloadsPath = path.join(os.homedir(), 'Downloads');
        const filePath = path.join(downloadsPath, fileName);

        console.log('Recording stopped, file would be saved to:', filePath);

        return {
            success: true,
            filePath,
            message: 'Recording stopped and saved successfully'
        };
    } catch (error) {
        console.error('Error stopping recording:', error);
        return {
            success: false,
            message: error instanceof Error ? error.message : 'Unknown error occurred'
        };
    }
};

export const getRecordingStatus = (): { isRecording: boolean; isAudioEnabled: boolean } => {
    return {
        isRecording: currentRecording !== null,
        isAudioEnabled
    };
}; 