import { useEffect, useRef } from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "./ui/card";
import { ScrollArea } from "./ui/scroll-area";
import { Badge } from "./ui/badge";
import { Clock, MessageSquare } from "lucide-react";
import { SummaryDebugPanel } from "./SummaryDebugPanel";

interface SummaryItem {
  id: string;
  timeRange: string;
  summary: string;
  keyPoints: string[];
}

interface SummaryPanelProps {
  currentTopic: string;
  summaries: SummaryItem[];
  isSummaryDebugMode?: boolean;
  onTopicChange?: (topic: string) => void;
  onClearAllSummaries?: () => void;
  onAddSummary?: (timeRange: string, summary: string, keyPoints: string[]) => void;
  summaryScrollToBottom?: boolean;
  onSummaryScrolledToBottom?: () => void;
}

export function SummaryPanel({ 
  currentTopic, 
  summaries, 
  isSummaryDebugMode = false, 
  onTopicChange,
  onClearAllSummaries,
  onAddSummary,
  summaryScrollToBottom = false,
  onSummaryScrolledToBottom
}: SummaryPanelProps) {
  const contentRef = useRef<HTMLDivElement>(null);
  const scrollAreaContainerRef = useRef<HTMLDivElement>(null);

  // 스크롤을 최하단으로 이동시키는 효과
  useEffect(() => {
    if (summaryScrollToBottom) {
      // DOM 업데이트 후에 스크롤을 실행하기 위해 setTimeout 사용
      setTimeout(() => {
        let scrolled = false;

        // 방법 1: ScrollArea 컨테이너에서 viewport 찾기
        if (scrollAreaContainerRef.current) {
          const viewport = scrollAreaContainerRef.current.querySelector('[data-radix-scroll-area-viewport]');
          if (viewport) {
            viewport.scrollTop = viewport.scrollHeight;
            scrolled = true;
            console.log("스크롤 실행됨 (방법 1): viewport.scrollHeight =", viewport.scrollHeight);
          }
        }

        // 방법 2: contentRef에서 상위 요소로 찾기
        if (!scrolled && contentRef.current) {
          const scrollContainer = contentRef.current.closest('[data-radix-scroll-area-viewport]');
          if (scrollContainer) {
            scrollContainer.scrollTop = scrollContainer.scrollHeight;
            scrolled = true;
            console.log("스크롤 실행됨 (방법 2): scrollContainer.scrollHeight =", scrollContainer.scrollHeight);
          }
        }

        // 방법 3: 직접적인 DOM 트리 탐색
        if (!scrolled && contentRef.current) {
          const parent = contentRef.current.parentElement;
          if (parent && parent.hasAttribute('data-radix-scroll-area-viewport')) {
            parent.scrollTop = parent.scrollHeight;
            scrolled = true;
            console.log("스크롤 실행됨 (방법 3): parent.scrollHeight =", parent.scrollHeight);
          }
        }

        if (!scrolled) {
          console.log("스크롤 실행 실패: viewport를 찾을 수 없음");
        }

        onSummaryScrolledToBottom?.();
      }, 50); // 50ms 지연
    }
  }, [summaryScrollToBottom, summaries.length, onSummaryScrolledToBottom]);

  return (
    <div className="h-full flex flex-col bg-white border-l">
      {/* 현재 주제 */}
      <div className="p-4 border-b bg-blue-50 shrink-0">
        <div className="flex items-start gap-2">
          <MessageSquare size={16} className="text-blue-600 mt-1 shrink-0" />
          <div>
            <h3 className="text-sm mb-1">현재 주제</h3>
            <p className="text-sm text-blue-800">{currentTopic}</p>
          </div>
        </div>
      </div>

      {/* 구간별 요약 */}
      {/* 구간별 요약 헤더 - 고정 */}
      <div className="p-4 border-b shrink-0">
        <div className="flex items-center gap-2">
          <Clock size={16} className="text-gray-600" />
          <h3 className="text-sm">구간별 요약</h3>
        </div>
      </div>

      {/* 구간별 요약 콘텐츠 - 스크롤 영역 */}
      <div className={`flex-1 min-h-0 ${isSummaryDebugMode ? 'overflow-hidden' : ''}`}>
        <div ref={scrollAreaContainerRef} className="h-full">
          <ScrollArea className="h-full">
            <div ref={contentRef} className="p-4 space-y-3">
              {summaries.map((summary) => (
                <Card key={summary.id} className="shadow-sm">
                  <CardHeader className="pb-1">
                    <CardTitle className="text-sm flex items-center gap-2">
                      <Badge variant="outline" className="text-xs">
                        {summary.timeRange}
                      </Badge>
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="pt-1">
                    <p className="text-sm text-gray-800 mb-2">
                      {summary.summary}
                    </p>
                    <div className="space-y-1">
                      <p className="text-xs text-gray-600">주요 포인트:</p>
                      <ul className="space-y-0.5">
                        {summary.keyPoints.map((point, index) => (
                          <li key={index} className="text-xs text-gray-700 flex items-start gap-1">
                            <span className="text-blue-500 mt-1">•</span>
                            <span>{point}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </ScrollArea>
        </div>
      </div>

      {/* 요약패널 디버그 - 조건부 렌더링 */}
      {isSummaryDebugMode && onTopicChange && (
        <div className="shrink-0 border-t bg-white animate-in slide-in-from-bottom-2 duration-300 max-h-[50vh] overflow-y-auto">
          <div className="p-4">
            <SummaryDebugPanel
              currentTopic={currentTopic}
              onTopicChange={onTopicChange}
              onClearAllSummaries={onClearAllSummaries}
              onAddSummary={onAddSummary}
            />
          </div>
        </div>
      )}
    </div>
  );
}