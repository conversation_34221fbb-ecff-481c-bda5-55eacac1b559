# MacOS Voice Interpretation App

실시간 음성 통역을 지원하는 MacOS 애플리케이션입니다. 온라인/오프라인 회의에서 실시간 통역과 회의록 자동 생성 기능을 제공합니다.

![Application Preview](https://via.placeholder.com/800x500/f3f4f6/1f2937?text=MacOS+Voice+Interpretation+App)

## 🚀 주요 기능

### 핵심 기능
- **실시간 음성 통역**: 음성을 실시간으로 인식하고 번역
- **회의록 자동 생성**: AI 기반 회의 요약 및 주요 포인트 추출
- **질문 도우미**: 번역 관련 질문 및 제안 기능
- **전문 용어 사전**: 맞춤형 전문 용어 번역 지원
- **다크/라이트 모드**: 사용자 선호에 따른 테마 전환

### 인터페이스 구성
- **좌측 패널**: 실시간 통역 표시 (원문/번역문, 타임스탬프, 언어 태그)
- **우측 패널**: 회의 요약 (현재 주제, 5분 간격 요약)
- **하단 제어 패널**: 마이크 버튼, 언어 선택 드롭다운

### 최근 UI 개선사항
- 질문 도우미 아이콘과 요약 패널 아이콘 겹침 현상 수정
- 패널 표시/숨김 시 부드러운 애니메이션 효과 추가
- 번역 패널 하단 여백 제거로 더 많은 정보 표시
- 원문과 번역문 간격 최적화
- Switch 토글을 직관적인 닫기(X) 버튼으로 교체
- 닫힌 패널 재열기를 위한 플로팅 액션 버튼 추가

## 🛠 기술 스택

### Frontend Framework
- **React 18.2.0** - UI 라이브러리
- **TypeScript 5.7.2** - 타입 안전성
- **Vite 6.0.5** - 빌드 도구 및 개발 서버

### UI Components & Styling
- **Tailwind CSS v4.0.0-alpha.15** - 유틸리티 CSS 프레임워크
- **Radix UI** - 접근 가능한 UI 컴포넌트 라이브러리
- **ShadCN/UI** - 사전 구성된 컴포넌트 시스템
- **Lucide React 0.487.0** - 아이콘 라이브러리

### Key Dependencies
- **react-resizable-panels 2.1.7** - 크기 조절 가능한 패널
- **class-variance-authority 0.7.1** - 컴포넌트 variant 관리
- **clsx & tailwind-merge** - CSS 클래스 유틸리티

## 📦 설치 및 설정

### 시스템 요구사항
- **Node.js**: 18.0.0 이상
- **npm**: 9.0.0 이상 또는 **yarn**: 1.22.0 이상

### 1. 프로젝트 클론
```bash
git clone <repository-url>
cd macos-voice-interpretation-app
```

### 2. 의존성 설치
```bash
# npm 사용
npm install

# 또는 yarn 사용
yarn install
```

### 3. 개발 서버 실행
```bash
# npm 사용
npm run dev

# 또는 yarn 사용
yarn dev
```

개발 서버가 실행되면 `http://localhost:5173`에서 애플리케이션에 접근할 수 있습니다.

### 4. 빌드
```bash
# 프로덕션 빌드
npm run build

# 빌드 결과 미리보기
npm run preview
```

## 📋 주요 의존성 설명

### Core Dependencies

#### UI Framework
- `@radix-ui/react-*`: 접근 가능하고 무스타일 UI 컴포넌트
  - `react-select@2.1.6`: 드롭다운 선택 컴포넌트
  - `react-switch@1.1.3`: 토글 스위치 컴포넌트
  - `react-slot@1.1.2`: 컴포넌트 합성을 위한 슬롯 시스템
  - `react-label@2.1.2`: 접근 가능한 라벨 컴포넌트
  - `react-scroll-area@1.2.0`: 커스텀 스크롤 영역

#### Layout & Interaction
- `react-resizable-panels@2.1.7`: 사용자가 크기를 조절할 수 있는 패널 시스템
- `lucide-react@0.487.0`: 모던하고 일관된 아이콘 세트

#### Styling Utilities
- `class-variance-authority@0.7.1`: 타입 안전한 variant 기반 스타일링
- `clsx@^2.1.1`: 조건부 CSS 클래스 결합
- `tailwind-merge@^2.5.0`: Tailwind 클래스 충돌 해결

### Development Dependencies
- `@vitejs/plugin-react@^4.3.4`: Vite React 플러그인
- `@typescript-eslint/*`: TypeScript ESLint 설정
- `autoprefixer@^10.4.20`: CSS 자동 prefixing
- `eslint@^9.17.0`: 코드 품질 검사

## 📁 프로젝트 구조

```
├── App.tsx                 # 메인 애플리케이션 컴포넌트
├── package.json            # 프로젝트 설정 및 의존성
├── components/             # React 컴포넌트
│   ├── ControlPanel.tsx    # 하단 제어 패널
│   ├── QuestionHelper.tsx  # 질문 도우미 컴포넌트
│   ├── SummaryPanel.tsx    # 우측 요약 패널
│   ├── TranslationBlock.tsx # 개별 번역 블록
│   ├── TranslationPanel.tsx # 좌측 번역 패널
│   ├── figma/              # Figma 관련 컴포넌트
│   └── ui/                 # ShadCN UI 컴포넌트 (최적화됨)
│       ├── badge.tsx       # 배지 컴포넌트
│       ├── button.tsx      # 버튼 컴포넌트
│       ├── card.tsx        # 카드 컴포넌트
│       ├── input.tsx       # 입력 필드 컴포넌트
│       ├── label.tsx       # 라벨 컴포넌트
│       ├── resizable.tsx   # 크기 조절 패널
│       ├── scroll-area.tsx # 스크롤 영역
│       ├── select.tsx      # 선택 드롭다운
│       ├── switch.tsx      # 토글 스위치
│       └── utils.ts        # 유틸리티 함수
└── styles/
    └── globals.css         # 전역 스타일 및 Tailwind 설정
```

## 🎨 스타일링 시스템

### Tailwind CSS v4 설정
이 프로젝트는 **Tailwind CSS v4.0.0-alpha.15**를 사용합니다.

#### ⚠️ 중요: Tailwind CSS v4 알파 버전 사용
- **현재 알파 버전**: 4.0.0-alpha.15 (프로덕션 준비 완료되지 않음)
- **새로운 문법**: v4에서 도입된 새로운 CSS 작성 방식 사용
- **호환성 주의**: 기존 v3 프로젝트와 다른 설정 방식

#### Tailwind v4의 새로운 기능:
- **`@theme inline` 블록**: CSS 변수를 Tailwind 토큰으로 직접 변환
- **`@custom-variant`**: 사용자 정의 variant 생성 (다크 모드 등)
- **향상된 CSS 변수 시스템**: 더 유연한 테마 관리
- **기본 타이포그래피**: HTML 요소별 기본 스타일 자동 적용

#### 주요 v4 문법 예시:
```css
/* 사용자 정의 variant (다크 모드) */
@custom-variant dark (&:is(.dark *));

/* 테마 토큰 정의 */
@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --radius-lg: var(--radius);
}

/* CSS 변수 기반 테마 */
:root {
  --font-size: 14px;
  --background: #ffffff;
  --foreground: oklch(0.145 0 0);
  --primary: #030213;
  --border: rgba(0, 0, 0, 0.1);
}

.dark {
  --background: oklch(0.145 0 0);
  --foreground: oklch(0.985 0 0);
}
```

#### 설정 특징:
- **기본 폰트 크기**: 14px (사용자 설정으로 14pt-24pt 조정 가능)
- **OKLCH 색상 공간**: 더 정확한 색상 표현
- **자동 타이포그래피**: HTML 요소별 기본 폰트 설정
- **동적 테마 전환**: CSS 변수를 통한 실시간 테마 변경

### 컴포넌트 스타일링
- **ShadCN/UI**: 일관된 디자인 시스템 (필요한 컴포넌트만 선별 포함)
- **Variant-based styling**: CVA를 통한 컴포넌트 변형 관리
- **반응형 디자인**: 모바일부터 데스크톱까지 지원

## 🔧 개발 가이드

### 새 컴포넌트 추가
1. `components/` 디렉토리에 컴포넌트 파일 생성
2. TypeScript 인터페이스 정의
3. ShadCN UI 컴포넌트 활용
4. 적절한 props validation 추가

### UI 컴포넌트 사용
```tsx
import { Button } from "./components/ui/button";
import { Select, SelectContent, SelectItem } from "./components/ui/select";

// 사용 예시
<Button variant="default" size="sm">
  버튼 텍스트
</Button>
```

### 최적화된 UI 컴포넌트 시스템
이 프로젝트는 **실제 사용되는 UI 컴포넌트만** 포함하도록 최적화되었습니다:

#### 포함된 컴포넌트:
- **badge**: 태그 및 상태 표시
- **button**: 모든 인터랙션 버튼
- **card**: 콘텐츠 그룹핑
- **input**: 텍스트 입력 필드
- **label**: 접근성 라벨
- **resizable**: 패널 크기 조절
- **scroll-area**: 커스텀 스크롤
- **select**: 언어 선택 드롭다운
- **switch**: 토글 컨트롤

#### 제거된 컴포넌트:
불필요한 40개 이상의 UI 컴포넌트 제거로 **빌드 시간 단축** 및 **번들 크기 최적화**

### 스타일 커스터마이징
1. `styles/globals.css`에서 CSS 변수 수정
2. 컴포넌트별 Tailwind 클래스 적용
3. 다크 모드 지원을 위한 변수 추가

## 🚦 스크립트 명령어

```bash
# 개발 서버 실행
npm run dev

# TypeScript 체크 후 프로덕션 빌드
npm run build

# 빌드 결과 미리보기
npm run preview

# ESLint를 통한 코드 검사
npm run lint
```

## 🌟 향후 개발 계획

### 현재 상태
- [x] 기본 UI 컴포넌트 구현
- [x] 모의 데이터를 통한 레이아웃 구성
- [x] 패널 시스템 및 반응형 디자인
- [x] 테마 시스템 (다크/라이트 모드)
- [x] UI 컴포넌트 시스템 최적화

### 다음 단계
- [ ] 실제 음성 인식 API 연동
- [ ] 실시간 번역 서비스 통합
- [ ] AI 기반 회의 요약 기능 구현
- [ ] 전문 용어 사전 데이터베이스 구축
- [ ] 사용자 설정 저장 기능
- [ ] 회의록 내보내기 기능

## 🛡 문제 해결

### 일반적인 문제

#### 1. 의존성 설치 오류
```bash
# node_modules 및 lock 파일 제거 후 재설치
rm -rf node_modules package-lock.json
npm install
```

#### 2. TypeScript 오류
```bash
# TypeScript 캐시 초기화
npx tsc --build --clean
```

#### 3. Import 버전 명시 오류
```bash
# 오류 예시
Module not found: Can't resolve 'lucide-react@0.487.0'
Module not found: Can't resolve '@radix-ui/react-accordion@1.2.3'
```
**해결 방법:**
- UI 컴포넌트에서 `@version` 제거
- `import { Icon } from "lucide-react@0.487.0"` → `import { Icon } from "lucide-react"`
- `import * as Primitive from "@radix-ui/react-*@version"` → `import * as Primitive from "@radix-ui/react-*"`

#### 4. Tailwind CSS v4 관련 문제
- `styles/globals.css` 파일이 올바르게 import되었는지 확인
- **v4 알파 버전 특이사항**: 
  - `@theme inline` 블록이 제대로 처리되는지 확인
  - `@custom-variant` 문법이 지원되는지 확인
  - CSS 변수가 올바르게 Tailwind 토큰으로 변환되는지 확인
- **빌드 오류 시**: Tailwind CSS v4 호환 가능한 PostCSS 설정 확인

#### 5. 사용되지 않는 UI 컴포넌트 관련
- **최적화 완료**: 사용되지 않는 40개 이상의 UI 컴포넌트 제거됨
- **필요한 컴포넌트만 유지**: 실제 사용되는 9개 컴포넌트만 보존
- **빌드 성능 개선**: 불필요한 의존성 제거로 빌드 시간 단축

#### 6. Radix UI 컴포넌트 오류
- 정확한 버전의 Radix UI 패키지 설치 확인
- `package.json`의 버전과 실제 사용 버전 일치 여부 확인

### 버전 호환성
- **Node.js 18+ 필수**: 최신 ESM 및 TypeScript 지원
- **Radix UI 컴포넌트**: 지정된 정확한 버전 사용 필수
- **⚠️ Import 문법 주의사항**:
  - 일부 UI 컴포넌트에서 `import from "package@version"` 문법 사용 시 빌드 오류 발생
  - 표준 `import from "package"` 문법으로 수정 필요
  - package.json의 버전과 실제 설치된 패키지 버전이 일치해야 함
- **⚠️ Tailwind CSS v4 주의사항**:
  - 알파 버전으로 API 변경 가능성 있음
  - v3와 호환되지 않는 새로운 문법 사용
  - 프로덕션 사용 시 안정성 검토 필요
  - PostCSS 플러그인 호환성 확인 필요

### 프로젝트 최적화 현황
- **UI 컴포넌트 최적화**: 49개 → 9개 컴포넌트로 축소
- **번들 크기 최적화**: 불필요한 의존성 제거
- **빌드 성능 향상**: 컴파일 시간 단축
- **코드 가독성 개선**: 실제 사용되는 컴포넌트만 유지

### Tailwind v4 마이그레이션 가이드
현재 프로젝트는 이미 v4로 설정되어 있으나, 참고용 주요 변경사항:

```css
/* v3 방식 (사용하지 않음) */
@tailwind base;
@tailwind components;  
@tailwind utilities;

/* v4 방식 (현재 사용 중) */
@theme inline {
  /* 직접 토큰 정의 */
}

@custom-variant dark (&:is(.dark *));
```

## 📄 라이선스

이 프로젝트는 MIT 라이선스 하에 제공됩니다.

## 🤝 기여 방법

1. 프로젝트를 Fork
2. 기능 브랜치 생성 (`git checkout -b feature/AmazingFeature`)
3. 변경사항 커밋 (`git commit -m 'Add some AmazingFeature'`)
4. 브랜치에 Push (`git push origin feature/AmazingFeature`)
5. Pull Request 생성

## 📞 지원

문제가 발생하거나 질문이 있으시면 이슈를 생성해 주세요.

---

**Voice Interpretation Team** - MacOS 실시간 음성 통역 애플리케이션