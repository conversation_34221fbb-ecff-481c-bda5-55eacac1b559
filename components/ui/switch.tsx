"use client";

import * as React from "react";
import * as SwitchPrimitive from "@radix-ui/react-switch";

import { cn } from "./utils";

function Switch({
  className,
  ...props
}: React.ComponentProps<typeof SwitchPrimitive.Root>) {
  return (
    <SwitchPrimitive.Root
      data-slot="switch"
      className={cn(
        "peer inline-flex h-[1.15rem] w-8 shrink-0 items-center rounded-full transition-all outline-none disabled:cursor-not-allowed disabled:opacity-50",
        // Off state styling - 더 명확한 배경과 테두리
        "data-[state=unchecked]:bg-gray-200 data-[state=unchecked]:border-2 data-[state=unchecked]:border-gray-300 data-[state=unchecked]:shadow-inner",
        "dark:data-[state=unchecked]:bg-gray-700 dark:data-[state=unchecked]:border-gray-600",
        // On state styling - 얇은 테두리로 깔끔하게
        "data-[state=checked]:bg-primary data-[state=checked]:border data-[state=checked]:border-primary-foreground/20",
        // Focus state
        "focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]",
        className,
      )}
      {...props}
    >
      <SwitchPrimitive.Thumb
        data-slot="switch-thumb"
        className={cn(
          "pointer-events-none block size-4 rounded-full ring-0 transition-transform shadow-sm border border-gray-300",
          // Off state thumb - 더 명확한 대비
          "data-[state=unchecked]:translate-x-0 data-[state=unchecked]:bg-white data-[state=unchecked]:shadow-md",
          "dark:data-[state=unchecked]:bg-gray-200 dark:data-[state=unchecked]:border-gray-500",
          // On state thumb - 흰색 배경에서도 잘 보이도록 테두리 추가
          "data-[state=checked]:translate-x-4 data-[state=checked]:bg-white data-[state=checked]:border-gray-400",
          "dark:data-[state=checked]:bg-primary-foreground dark:data-[state=checked]:border-gray-300",
        )}
      />
    </SwitchPrimitive.Root>
  );
}

export { Switch };
