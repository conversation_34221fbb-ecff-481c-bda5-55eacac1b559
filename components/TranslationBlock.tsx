import { Clock } from "lucide-react";

interface TranslationBlockProps {
  id: string;
  timestamp: string;
  originalText: string;
  translatedText: string;
  originalLang: string;
  targetLang: string;
  confidence: 'high' | 'medium' | 'low';
  isDebugLatest?: boolean;
  onPlay?: (id: string) => void;
}

export function TranslationBlock({
  id,
  timestamp,
  originalText,
  translatedText,
  originalLang,
  targetLang,
  confidence,
  isDebugLatest = false,
  onPlay
}: TranslationBlockProps) {
  const confidenceColors = {
    high: 'border-l-green-500 bg-green-50',
    medium: 'border-l-yellow-500 bg-yellow-50',
    low: 'border-l-orange-500 bg-orange-50'
  };

  // 빈 아이템인지 확인
  const isEmpty = !originalText.trim() && !translatedText.trim();
  const isDebugItem = id.startsWith('debug-');
  
  // Source와 Target 언어가 같은지 확인
  const isSameLanguage = originalLang.toLowerCase() === targetLang.toLowerCase();

  // 디버그 아이템의 배경색 설정
  const getBackgroundColor = () => {
    if (isDebugItem) {
      if (isDebugLatest) {
        // 마지막 디버그 아이템: 노란색 배경
        return 'border-l-yellow-500 bg-yellow-100';
      } else {
        // 이전 디버그 아이템들: 초록색 배경
        return 'border-l-green-500 bg-green-100';
      }
    }
    // 일반 아이템은 기존 로직 사용
    return confidenceColors[confidence];
  };

  return (
    <div className={`p-4 mb-4 border-l-4 rounded-lg ${getBackgroundColor()} hover:shadow-sm transition-shadow cursor-pointer ${isEmpty ? 'opacity-60' : ''}`}
         onClick={() => onPlay?.(id)}>
      <div className="flex items-center justify-between mb-3">
        <div className="flex items-center gap-2">
          <Clock size={14} className="text-muted-foreground" />
          <span className="text-xs text-muted-foreground">{timestamp}</span>
          {isDebugItem && (
            <span className={`text-xs px-1.5 py-0.5 rounded text-center ${
              isDebugLatest 
                ? 'bg-yellow-200 text-yellow-800' 
                : 'bg-green-200 text-green-800'
            }`}>
              {isDebugLatest ? 'DEBUG (최신)' : 'DEBUG'}
            </span>
          )}
        </div>
      </div>
      
      <div className="space-y-2">
        {isSameLanguage ? (
          // 동일 언어일 경우: 원문만 표시 (번역 텍스트 크기로)
          <div>
            <div className="flex items-center gap-2 mb-1.5">
              <span className="text-xs px-2 py-1 bg-blue-100 text-blue-800 rounded uppercase">{originalLang}</span>
              <span className="text-xs text-muted-foreground">원문</span>
            </div>
            <p className="leading-relaxed">
              {originalText || (
                <span className="text-muted-foreground italic">
                  음성 인식 대기 중...
                </span>
              )}
            </p>
          </div>
        ) : (
          // 다른 언어일 경우: 원문과 번역 모두 표시
          <>
            <div>
              <div className="flex items-center gap-2 mb-1.5">
                <span className="text-xs px-2 py-1 bg-blue-100 text-blue-800 rounded uppercase">{originalLang}</span>
                <span className="text-xs text-muted-foreground">원문</span>
              </div>
              <p className="text-sm text-gray-700 leading-relaxed mb-2">
                {originalText || (
                  <span className="text-muted-foreground italic">
                    음성 인식 대기 중...
                  </span>
                )}
              </p>
            </div>
            <div>
              <div className="flex items-center gap-2 mb-1.5">
                <span className="text-xs px-2 py-1 bg-green-100 text-green-800 rounded uppercase">{targetLang}</span>
                <span className="text-xs text-muted-foreground">번역</span>
              </div>
              <p className="leading-relaxed">
                {translatedText || (
                  <span className="text-muted-foreground italic">
                    번역 대기 중...
                  </span>
                )}
              </p>
            </div>
          </>
        )}
      </div>
    </div>
  );
}