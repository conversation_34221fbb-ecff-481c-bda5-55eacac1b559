function sy(n,o){for(var l=0;l<o.length;l++){const s=o[l];if(typeof s!="string"&&!Array.isArray(s)){for(const a in s)if(a!=="default"&&!(a in n)){const c=Object.getOwnPropertyDescriptor(s,a);c&&Object.defineProperty(n,a,c.get?c:{enumerable:!0,get:()=>s[a]})}}}return Object.freeze(Object.defineProperty(n,Symbol.toStringTag,{value:"Module"}))}(function(){const o=document.createElement("link").relList;if(o&&o.supports&&o.supports("modulepreload"))return;for(const a of document.querySelectorAll('link[rel="modulepreload"]'))s(a);new MutationObserver(a=>{for(const c of a)if(c.type==="childList")for(const p of c.addedNodes)p.tagName==="LINK"&&p.rel==="modulepreload"&&s(p)}).observe(document,{childList:!0,subtree:!0});function l(a){const c={};return a.integrity&&(c.integrity=a.integrity),a.referrerPolicy&&(c.referrerPolicy=a.referrerPolicy),a.crossOrigin==="use-credentials"?c.credentials="include":a.crossOrigin==="anonymous"?c.credentials="omit":c.credentials="same-origin",c}function s(a){if(a.ep)return;a.ep=!0;const c=l(a);fetch(a.href,c)}})();function Kp(n){return n&&n.__esModule&&Object.prototype.hasOwnProperty.call(n,"default")?n.default:n}var Ua={exports:{}},Go={},Ka={exports:{}},ke={};/**
 * @license React
 * react.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Df;function ay(){if(Df)return ke;Df=1;var n=Symbol.for("react.element"),o=Symbol.for("react.portal"),l=Symbol.for("react.fragment"),s=Symbol.for("react.strict_mode"),a=Symbol.for("react.profiler"),c=Symbol.for("react.provider"),p=Symbol.for("react.context"),f=Symbol.for("react.forward_ref"),m=Symbol.for("react.suspense"),y=Symbol.for("react.memo"),w=Symbol.for("react.lazy"),x=Symbol.iterator;function S(_){return _===null||typeof _!="object"?null:(_=x&&_[x]||_["@@iterator"],typeof _=="function"?_:null)}var E={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},P=Object.assign,C={};function k(_,W,V){this.props=_,this.context=W,this.refs=C,this.updater=V||E}k.prototype.isReactComponent={},k.prototype.setState=function(_,W){if(typeof _!="object"&&typeof _!="function"&&_!=null)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,_,W,"setState")},k.prototype.forceUpdate=function(_){this.updater.enqueueForceUpdate(this,_,"forceUpdate")};function N(){}N.prototype=k.prototype;function j(_,W,V){this.props=_,this.context=W,this.refs=C,this.updater=V||E}var A=j.prototype=new N;A.constructor=j,P(A,k.prototype),A.isPureReactComponent=!0;var O=Array.isArray,I=Object.prototype.hasOwnProperty,B={current:null},D={key:!0,ref:!0,__self:!0,__source:!0};function oe(_,W,V){var Y,ne={},ee=null,F=null;if(W!=null)for(Y in W.ref!==void 0&&(F=W.ref),W.key!==void 0&&(ee=""+W.key),W)I.call(W,Y)&&!D.hasOwnProperty(Y)&&(ne[Y]=W[Y]);var H=arguments.length-2;if(H===1)ne.children=V;else if(1<H){for(var te=Array(H),J=0;J<H;J++)te[J]=arguments[J+2];ne.children=te}if(_&&_.defaultProps)for(Y in H=_.defaultProps,H)ne[Y]===void 0&&(ne[Y]=H[Y]);return{$$typeof:n,type:_,key:ee,ref:F,props:ne,_owner:B.current}}function re(_,W){return{$$typeof:n,type:_.type,key:W,ref:_.ref,props:_.props,_owner:_._owner}}function ae(_){return typeof _=="object"&&_!==null&&_.$$typeof===n}function me(_){var W={"=":"=0",":":"=2"};return"$"+_.replace(/[=:]/g,function(V){return W[V]})}var Z=/\/+/g;function ge(_,W){return typeof _=="object"&&_!==null&&_.key!=null?me(""+_.key):W.toString(36)}function se(_,W,V,Y,ne){var ee=typeof _;(ee==="undefined"||ee==="boolean")&&(_=null);var F=!1;if(_===null)F=!0;else switch(ee){case"string":case"number":F=!0;break;case"object":switch(_.$$typeof){case n:case o:F=!0}}if(F)return F=_,ne=ne(F),_=Y===""?"."+ge(F,0):Y,O(ne)?(V="",_!=null&&(V=_.replace(Z,"$&/")+"/"),se(ne,W,V,"",function(J){return J})):ne!=null&&(ae(ne)&&(ne=re(ne,V+(!ne.key||F&&F.key===ne.key?"":(""+ne.key).replace(Z,"$&/")+"/")+_)),W.push(ne)),1;if(F=0,Y=Y===""?".":Y+":",O(_))for(var H=0;H<_.length;H++){ee=_[H];var te=Y+ge(ee,H);F+=se(ee,W,V,te,ne)}else if(te=S(_),typeof te=="function")for(_=te.call(_),H=0;!(ee=_.next()).done;)ee=ee.value,te=Y+ge(ee,H++),F+=se(ee,W,V,te,ne);else if(ee==="object")throw W=String(_),Error("Objects are not valid as a React child (found: "+(W==="[object Object]"?"object with keys {"+Object.keys(_).join(", ")+"}":W)+"). If you meant to render a collection of children, use an array instead.");return F}function ve(_,W,V){if(_==null)return _;var Y=[],ne=0;return se(_,Y,"","",function(ee){return W.call(V,ee,ne++)}),Y}function le(_){if(_._status===-1){var W=_._result;W=W(),W.then(function(V){(_._status===0||_._status===-1)&&(_._status=1,_._result=V)},function(V){(_._status===0||_._status===-1)&&(_._status=2,_._result=V)}),_._status===-1&&(_._status=0,_._result=W)}if(_._status===1)return _._result.default;throw _._result}var de={current:null},$={transition:null},q={ReactCurrentDispatcher:de,ReactCurrentBatchConfig:$,ReactCurrentOwner:B};function G(){throw Error("act(...) is not supported in production builds of React.")}return ke.Children={map:ve,forEach:function(_,W,V){ve(_,function(){W.apply(this,arguments)},V)},count:function(_){var W=0;return ve(_,function(){W++}),W},toArray:function(_){return ve(_,function(W){return W})||[]},only:function(_){if(!ae(_))throw Error("React.Children.only expected to receive a single React element child.");return _}},ke.Component=k,ke.Fragment=l,ke.Profiler=a,ke.PureComponent=j,ke.StrictMode=s,ke.Suspense=m,ke.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=q,ke.act=G,ke.cloneElement=function(_,W,V){if(_==null)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+_+".");var Y=P({},_.props),ne=_.key,ee=_.ref,F=_._owner;if(W!=null){if(W.ref!==void 0&&(ee=W.ref,F=B.current),W.key!==void 0&&(ne=""+W.key),_.type&&_.type.defaultProps)var H=_.type.defaultProps;for(te in W)I.call(W,te)&&!D.hasOwnProperty(te)&&(Y[te]=W[te]===void 0&&H!==void 0?H[te]:W[te])}var te=arguments.length-2;if(te===1)Y.children=V;else if(1<te){H=Array(te);for(var J=0;J<te;J++)H[J]=arguments[J+2];Y.children=H}return{$$typeof:n,type:_.type,key:ne,ref:ee,props:Y,_owner:F}},ke.createContext=function(_){return _={$$typeof:p,_currentValue:_,_currentValue2:_,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null},_.Provider={$$typeof:c,_context:_},_.Consumer=_},ke.createElement=oe,ke.createFactory=function(_){var W=oe.bind(null,_);return W.type=_,W},ke.createRef=function(){return{current:null}},ke.forwardRef=function(_){return{$$typeof:f,render:_}},ke.isValidElement=ae,ke.lazy=function(_){return{$$typeof:w,_payload:{_status:-1,_result:_},_init:le}},ke.memo=function(_,W){return{$$typeof:y,type:_,compare:W===void 0?null:W}},ke.startTransition=function(_){var W=$.transition;$.transition={};try{_()}finally{$.transition=W}},ke.unstable_act=G,ke.useCallback=function(_,W){return de.current.useCallback(_,W)},ke.useContext=function(_){return de.current.useContext(_)},ke.useDebugValue=function(){},ke.useDeferredValue=function(_){return de.current.useDeferredValue(_)},ke.useEffect=function(_,W){return de.current.useEffect(_,W)},ke.useId=function(){return de.current.useId()},ke.useImperativeHandle=function(_,W,V){return de.current.useImperativeHandle(_,W,V)},ke.useInsertionEffect=function(_,W){return de.current.useInsertionEffect(_,W)},ke.useLayoutEffect=function(_,W){return de.current.useLayoutEffect(_,W)},ke.useMemo=function(_,W){return de.current.useMemo(_,W)},ke.useReducer=function(_,W,V){return de.current.useReducer(_,W,V)},ke.useRef=function(_){return de.current.useRef(_)},ke.useState=function(_){return de.current.useState(_)},ke.useSyncExternalStore=function(_,W,V){return de.current.useSyncExternalStore(_,W,V)},ke.useTransition=function(){return de.current.useTransition()},ke.version="18.3.1",ke}var $f;function Eu(){return $f||($f=1,Ka.exports=ay()),Ka.exports}/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Ff;function uy(){if(Ff)return Go;Ff=1;var n=Eu(),o=Symbol.for("react.element"),l=Symbol.for("react.fragment"),s=Object.prototype.hasOwnProperty,a=n.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,c={key:!0,ref:!0,__self:!0,__source:!0};function p(f,m,y){var w,x={},S=null,E=null;y!==void 0&&(S=""+y),m.key!==void 0&&(S=""+m.key),m.ref!==void 0&&(E=m.ref);for(w in m)s.call(m,w)&&!c.hasOwnProperty(w)&&(x[w]=m[w]);if(f&&f.defaultProps)for(w in m=f.defaultProps,m)x[w]===void 0&&(x[w]=m[w]);return{$$typeof:o,type:f,key:S,ref:E,props:x,_owner:a.current}}return Go.Fragment=l,Go.jsx=p,Go.jsxs=p,Go}var Wf;function cy(){return Wf||(Wf=1,Ua.exports=uy()),Ua.exports}var g=cy(),h=Eu();const $n=Kp(h),Wi=sy({__proto__:null,default:$n},[h]);var Si={},Qa={exports:{}},ht={},Ga={exports:{}},Ya={};/**
 * @license React
 * scheduler.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Vf;function dy(){return Vf||(Vf=1,function(n){function o($,q){var G=$.length;$.push(q);e:for(;0<G;){var _=G-1>>>1,W=$[_];if(0<a(W,q))$[_]=q,$[G]=W,G=_;else break e}}function l($){return $.length===0?null:$[0]}function s($){if($.length===0)return null;var q=$[0],G=$.pop();if(G!==q){$[0]=G;e:for(var _=0,W=$.length,V=W>>>1;_<V;){var Y=2*(_+1)-1,ne=$[Y],ee=Y+1,F=$[ee];if(0>a(ne,G))ee<W&&0>a(F,ne)?($[_]=F,$[ee]=G,_=ee):($[_]=ne,$[Y]=G,_=Y);else if(ee<W&&0>a(F,G))$[_]=F,$[ee]=G,_=ee;else break e}}return q}function a($,q){var G=$.sortIndex-q.sortIndex;return G!==0?G:$.id-q.id}if(typeof performance=="object"&&typeof performance.now=="function"){var c=performance;n.unstable_now=function(){return c.now()}}else{var p=Date,f=p.now();n.unstable_now=function(){return p.now()-f}}var m=[],y=[],w=1,x=null,S=3,E=!1,P=!1,C=!1,k=typeof setTimeout=="function"?setTimeout:null,N=typeof clearTimeout=="function"?clearTimeout:null,j=typeof setImmediate<"u"?setImmediate:null;typeof navigator<"u"&&navigator.scheduling!==void 0&&navigator.scheduling.isInputPending!==void 0&&navigator.scheduling.isInputPending.bind(navigator.scheduling);function A($){for(var q=l(y);q!==null;){if(q.callback===null)s(y);else if(q.startTime<=$)s(y),q.sortIndex=q.expirationTime,o(m,q);else break;q=l(y)}}function O($){if(C=!1,A($),!P)if(l(m)!==null)P=!0,le(I);else{var q=l(y);q!==null&&de(O,q.startTime-$)}}function I($,q){P=!1,C&&(C=!1,N(oe),oe=-1),E=!0;var G=S;try{for(A(q),x=l(m);x!==null&&(!(x.expirationTime>q)||$&&!me());){var _=x.callback;if(typeof _=="function"){x.callback=null,S=x.priorityLevel;var W=_(x.expirationTime<=q);q=n.unstable_now(),typeof W=="function"?x.callback=W:x===l(m)&&s(m),A(q)}else s(m);x=l(m)}if(x!==null)var V=!0;else{var Y=l(y);Y!==null&&de(O,Y.startTime-q),V=!1}return V}finally{x=null,S=G,E=!1}}var B=!1,D=null,oe=-1,re=5,ae=-1;function me(){return!(n.unstable_now()-ae<re)}function Z(){if(D!==null){var $=n.unstable_now();ae=$;var q=!0;try{q=D(!0,$)}finally{q?ge():(B=!1,D=null)}}else B=!1}var ge;if(typeof j=="function")ge=function(){j(Z)};else if(typeof MessageChannel<"u"){var se=new MessageChannel,ve=se.port2;se.port1.onmessage=Z,ge=function(){ve.postMessage(null)}}else ge=function(){k(Z,0)};function le($){D=$,B||(B=!0,ge())}function de($,q){oe=k(function(){$(n.unstable_now())},q)}n.unstable_IdlePriority=5,n.unstable_ImmediatePriority=1,n.unstable_LowPriority=4,n.unstable_NormalPriority=3,n.unstable_Profiling=null,n.unstable_UserBlockingPriority=2,n.unstable_cancelCallback=function($){$.callback=null},n.unstable_continueExecution=function(){P||E||(P=!0,le(I))},n.unstable_forceFrameRate=function($){0>$||125<$?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):re=0<$?Math.floor(1e3/$):5},n.unstable_getCurrentPriorityLevel=function(){return S},n.unstable_getFirstCallbackNode=function(){return l(m)},n.unstable_next=function($){switch(S){case 1:case 2:case 3:var q=3;break;default:q=S}var G=S;S=q;try{return $()}finally{S=G}},n.unstable_pauseExecution=function(){},n.unstable_requestPaint=function(){},n.unstable_runWithPriority=function($,q){switch($){case 1:case 2:case 3:case 4:case 5:break;default:$=3}var G=S;S=$;try{return q()}finally{S=G}},n.unstable_scheduleCallback=function($,q,G){var _=n.unstable_now();switch(typeof G=="object"&&G!==null?(G=G.delay,G=typeof G=="number"&&0<G?_+G:_):G=_,$){case 1:var W=-1;break;case 2:W=250;break;case 5:W=**********;break;case 4:W=1e4;break;default:W=5e3}return W=G+W,$={id:w++,callback:q,priorityLevel:$,startTime:G,expirationTime:W,sortIndex:-1},G>_?($.sortIndex=G,o(y,$),l(m)===null&&$===l(y)&&(C?(N(oe),oe=-1):C=!0,de(O,G-_))):($.sortIndex=W,o(m,$),P||E||(P=!0,le(I))),$},n.unstable_shouldYield=me,n.unstable_wrapCallback=function($){var q=S;return function(){var G=S;S=q;try{return $.apply(this,arguments)}finally{S=G}}}}(Ya)),Ya}var Hf;function fy(){return Hf||(Hf=1,Ga.exports=dy()),Ga.exports}/**
 * @license React
 * react-dom.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Bf;function py(){if(Bf)return ht;Bf=1;var n=Eu(),o=fy();function l(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,r=1;r<arguments.length;r++)t+="&args[]="+encodeURIComponent(arguments[r]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var s=new Set,a={};function c(e,t){p(e,t),p(e+"Capture",t)}function p(e,t){for(a[e]=t,e=0;e<t.length;e++)s.add(t[e])}var f=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),m=Object.prototype.hasOwnProperty,y=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,w={},x={};function S(e){return m.call(x,e)?!0:m.call(w,e)?!1:y.test(e)?x[e]=!0:(w[e]=!0,!1)}function E(e,t,r,i){if(r!==null&&r.type===0)return!1;switch(typeof t){case"function":case"symbol":return!0;case"boolean":return i?!1:r!==null?!r.acceptsBooleans:(e=e.toLowerCase().slice(0,5),e!=="data-"&&e!=="aria-");default:return!1}}function P(e,t,r,i){if(t===null||typeof t>"u"||E(e,t,r,i))return!0;if(i)return!1;if(r!==null)switch(r.type){case 3:return!t;case 4:return t===!1;case 5:return isNaN(t);case 6:return isNaN(t)||1>t}return!1}function C(e,t,r,i,u,d,v){this.acceptsBooleans=t===2||t===3||t===4,this.attributeName=i,this.attributeNamespace=u,this.mustUseProperty=r,this.propertyName=e,this.type=t,this.sanitizeURL=d,this.removeEmptyString=v}var k={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach(function(e){k[e]=new C(e,0,!1,e,null,!1,!1)}),[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach(function(e){var t=e[0];k[t]=new C(t,1,!1,e[1],null,!1,!1)}),["contentEditable","draggable","spellCheck","value"].forEach(function(e){k[e]=new C(e,2,!1,e.toLowerCase(),null,!1,!1)}),["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach(function(e){k[e]=new C(e,2,!1,e,null,!1,!1)}),"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach(function(e){k[e]=new C(e,3,!1,e.toLowerCase(),null,!1,!1)}),["checked","multiple","muted","selected"].forEach(function(e){k[e]=new C(e,3,!0,e,null,!1,!1)}),["capture","download"].forEach(function(e){k[e]=new C(e,4,!1,e,null,!1,!1)}),["cols","rows","size","span"].forEach(function(e){k[e]=new C(e,6,!1,e,null,!1,!1)}),["rowSpan","start"].forEach(function(e){k[e]=new C(e,5,!1,e.toLowerCase(),null,!1,!1)});var N=/[\-:]([a-z])/g;function j(e){return e[1].toUpperCase()}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach(function(e){var t=e.replace(N,j);k[t]=new C(t,1,!1,e,null,!1,!1)}),"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach(function(e){var t=e.replace(N,j);k[t]=new C(t,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)}),["xml:base","xml:lang","xml:space"].forEach(function(e){var t=e.replace(N,j);k[t]=new C(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)}),["tabIndex","crossOrigin"].forEach(function(e){k[e]=new C(e,1,!1,e.toLowerCase(),null,!1,!1)}),k.xlinkHref=new C("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1),["src","href","action","formAction"].forEach(function(e){k[e]=new C(e,1,!1,e.toLowerCase(),null,!0,!0)});function A(e,t,r,i){var u=k.hasOwnProperty(t)?k[t]:null;(u!==null?u.type!==0:i||!(2<t.length)||t[0]!=="o"&&t[0]!=="O"||t[1]!=="n"&&t[1]!=="N")&&(P(t,r,u,i)&&(r=null),i||u===null?S(t)&&(r===null?e.removeAttribute(t):e.setAttribute(t,""+r)):u.mustUseProperty?e[u.propertyName]=r===null?u.type===3?!1:"":r:(t=u.attributeName,i=u.attributeNamespace,r===null?e.removeAttribute(t):(u=u.type,r=u===3||u===4&&r===!0?"":""+r,i?e.setAttributeNS(i,t,r):e.setAttribute(t,r))))}var O=n.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,I=Symbol.for("react.element"),B=Symbol.for("react.portal"),D=Symbol.for("react.fragment"),oe=Symbol.for("react.strict_mode"),re=Symbol.for("react.profiler"),ae=Symbol.for("react.provider"),me=Symbol.for("react.context"),Z=Symbol.for("react.forward_ref"),ge=Symbol.for("react.suspense"),se=Symbol.for("react.suspense_list"),ve=Symbol.for("react.memo"),le=Symbol.for("react.lazy"),de=Symbol.for("react.offscreen"),$=Symbol.iterator;function q(e){return e===null||typeof e!="object"?null:(e=$&&e[$]||e["@@iterator"],typeof e=="function"?e:null)}var G=Object.assign,_;function W(e){if(_===void 0)try{throw Error()}catch(r){var t=r.stack.trim().match(/\n( *(at )?)/);_=t&&t[1]||""}return`
`+_+e}var V=!1;function Y(e,t){if(!e||V)return"";V=!0;var r=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(t)if(t=function(){throw Error()},Object.defineProperty(t.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(t,[])}catch(M){var i=M}Reflect.construct(e,[],t)}else{try{t.call()}catch(M){i=M}e.call(t.prototype)}else{try{throw Error()}catch(M){i=M}e()}}catch(M){if(M&&i&&typeof M.stack=="string"){for(var u=M.stack.split(`
`),d=i.stack.split(`
`),v=u.length-1,b=d.length-1;1<=v&&0<=b&&u[v]!==d[b];)b--;for(;1<=v&&0<=b;v--,b--)if(u[v]!==d[b]){if(v!==1||b!==1)do if(v--,b--,0>b||u[v]!==d[b]){var R=`
`+u[v].replace(" at new "," at ");return e.displayName&&R.includes("<anonymous>")&&(R=R.replace("<anonymous>",e.displayName)),R}while(1<=v&&0<=b);break}}}finally{V=!1,Error.prepareStackTrace=r}return(e=e?e.displayName||e.name:"")?W(e):""}function ne(e){switch(e.tag){case 5:return W(e.type);case 16:return W("Lazy");case 13:return W("Suspense");case 19:return W("SuspenseList");case 0:case 2:case 15:return e=Y(e.type,!1),e;case 11:return e=Y(e.type.render,!1),e;case 1:return e=Y(e.type,!0),e;default:return""}}function ee(e){if(e==null)return null;if(typeof e=="function")return e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case D:return"Fragment";case B:return"Portal";case re:return"Profiler";case oe:return"StrictMode";case ge:return"Suspense";case se:return"SuspenseList"}if(typeof e=="object")switch(e.$$typeof){case me:return(e.displayName||"Context")+".Consumer";case ae:return(e._context.displayName||"Context")+".Provider";case Z:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case ve:return t=e.displayName||null,t!==null?t:ee(e.type)||"Memo";case le:t=e._payload,e=e._init;try{return ee(e(t))}catch{}}return null}function F(e){var t=e.type;switch(e.tag){case 24:return"Cache";case 9:return(t.displayName||"Context")+".Consumer";case 10:return(t._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return e=t.render,e=e.displayName||e.name||"",t.displayName||(e!==""?"ForwardRef("+e+")":"ForwardRef");case 7:return"Fragment";case 5:return t;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return ee(t);case 8:return t===oe?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if(typeof t=="function")return t.displayName||t.name||null;if(typeof t=="string")return t}return null}function H(e){switch(typeof e){case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function te(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function J(e){var t=te(e)?"checked":"value",r=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),i=""+e[t];if(!e.hasOwnProperty(t)&&typeof r<"u"&&typeof r.get=="function"&&typeof r.set=="function"){var u=r.get,d=r.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return u.call(this)},set:function(v){i=""+v,d.call(this,v)}}),Object.defineProperty(e,t,{enumerable:r.enumerable}),{getValue:function(){return i},setValue:function(v){i=""+v},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function fe(e){e._valueTracker||(e._valueTracker=J(e))}function we(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var r=t.getValue(),i="";return e&&(i=te(e)?e.checked?"true":"false":e.value),e=i,e!==r?(t.setValue(e),!0):!1}function Se(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}function Pe(e,t){var r=t.checked;return G({},t,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:r??e._wrapperState.initialChecked})}function tt(e,t){var r=t.defaultValue==null?"":t.defaultValue,i=t.checked!=null?t.checked:t.defaultChecked;r=H(t.value!=null?t.value:r),e._wrapperState={initialChecked:i,initialValue:r,controlled:t.type==="checkbox"||t.type==="radio"?t.checked!=null:t.value!=null}}function st(e,t){t=t.checked,t!=null&&A(e,"checked",t,!1)}function Ze(e,t){st(e,t);var r=H(t.value),i=t.type;if(r!=null)i==="number"?(r===0&&e.value===""||e.value!=r)&&(e.value=""+r):e.value!==""+r&&(e.value=""+r);else if(i==="submit"||i==="reset"){e.removeAttribute("value");return}t.hasOwnProperty("value")?Mt(e,t.type,r):t.hasOwnProperty("defaultValue")&&Mt(e,t.type,H(t.defaultValue)),t.checked==null&&t.defaultChecked!=null&&(e.defaultChecked=!!t.defaultChecked)}function Ue(e,t,r){if(t.hasOwnProperty("value")||t.hasOwnProperty("defaultValue")){var i=t.type;if(!(i!=="submit"&&i!=="reset"||t.value!==void 0&&t.value!==null))return;t=""+e._wrapperState.initialValue,r||t===e.value||(e.value=t),e.defaultValue=t}r=e.name,r!==""&&(e.name=""),e.defaultChecked=!!e._wrapperState.initialChecked,r!==""&&(e.name=r)}function Mt(e,t,r){(t!=="number"||Se(e.ownerDocument)!==e)&&(r==null?e.defaultValue=""+e._wrapperState.initialValue:e.defaultValue!==""+r&&(e.defaultValue=""+r))}var Kn=Array.isArray;function kt(e,t,r,i){if(e=e.options,t){t={};for(var u=0;u<r.length;u++)t["$"+r[u]]=!0;for(r=0;r<e.length;r++)u=t.hasOwnProperty("$"+e[r].value),e[r].selected!==u&&(e[r].selected=u),u&&i&&(e[r].defaultSelected=!0)}else{for(r=""+H(r),t=null,u=0;u<e.length;u++){if(e[u].value===r){e[u].selected=!0,i&&(e[u].defaultSelected=!0);return}t!==null||e[u].disabled||(t=e[u])}t!==null&&(t.selected=!0)}}function vr(e,t){if(t.dangerouslySetInnerHTML!=null)throw Error(l(91));return G({},t,{value:void 0,defaultValue:void 0,children:""+e._wrapperState.initialValue})}function fl(e,t){var r=t.value;if(r==null){if(r=t.children,t=t.defaultValue,r!=null){if(t!=null)throw Error(l(92));if(Kn(r)){if(1<r.length)throw Error(l(93));r=r[0]}t=r}t==null&&(t=""),r=t}e._wrapperState={initialValue:H(r)}}function Gu(e,t){var r=H(t.value),i=H(t.defaultValue);r!=null&&(r=""+r,r!==e.value&&(e.value=r),t.defaultValue==null&&e.defaultValue!==r&&(e.defaultValue=r)),i!=null&&(e.defaultValue=""+i)}function Yu(e){var t=e.textContent;t===e._wrapperState.initialValue&&t!==""&&t!==null&&(e.value=t)}function Xu(e){switch(e){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function ns(e,t){return e==null||e==="http://www.w3.org/1999/xhtml"?Xu(t):e==="http://www.w3.org/2000/svg"&&t==="foreignObject"?"http://www.w3.org/1999/xhtml":e}var pl,Zu=function(e){return typeof MSApp<"u"&&MSApp.execUnsafeLocalFunction?function(t,r,i,u){MSApp.execUnsafeLocalFunction(function(){return e(t,r,i,u)})}:e}(function(e,t){if(e.namespaceURI!=="http://www.w3.org/2000/svg"||"innerHTML"in e)e.innerHTML=t;else{for(pl=pl||document.createElement("div"),pl.innerHTML="<svg>"+t.valueOf().toString()+"</svg>",t=pl.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;t.firstChild;)e.appendChild(t.firstChild)}});function uo(e,t){if(t){var r=e.firstChild;if(r&&r===e.lastChild&&r.nodeType===3){r.nodeValue=t;return}}e.textContent=t}var co={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},dg=["Webkit","ms","Moz","O"];Object.keys(co).forEach(function(e){dg.forEach(function(t){t=t+e.charAt(0).toUpperCase()+e.substring(1),co[t]=co[e]})});function qu(e,t,r){return t==null||typeof t=="boolean"||t===""?"":r||typeof t!="number"||t===0||co.hasOwnProperty(e)&&co[e]?(""+t).trim():t+"px"}function Ju(e,t){e=e.style;for(var r in t)if(t.hasOwnProperty(r)){var i=r.indexOf("--")===0,u=qu(r,t[r],i);r==="float"&&(r="cssFloat"),i?e.setProperty(r,u):e[r]=u}}var fg=G({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function rs(e,t){if(t){if(fg[e]&&(t.children!=null||t.dangerouslySetInnerHTML!=null))throw Error(l(137,e));if(t.dangerouslySetInnerHTML!=null){if(t.children!=null)throw Error(l(60));if(typeof t.dangerouslySetInnerHTML!="object"||!("__html"in t.dangerouslySetInnerHTML))throw Error(l(61))}if(t.style!=null&&typeof t.style!="object")throw Error(l(62))}}function os(e,t){if(e.indexOf("-")===-1)return typeof t.is=="string";switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var ls=null;function is(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var ss=null,yr=null,xr=null;function ec(e){if(e=Ao(e)){if(typeof ss!="function")throw Error(l(280));var t=e.stateNode;t&&(t=Il(t),ss(e.stateNode,e.type,t))}}function tc(e){yr?xr?xr.push(e):xr=[e]:yr=e}function nc(){if(yr){var e=yr,t=xr;if(xr=yr=null,ec(e),t)for(e=0;e<t.length;e++)ec(t[e])}}function rc(e,t){return e(t)}function oc(){}var as=!1;function lc(e,t,r){if(as)return e(t,r);as=!0;try{return rc(e,t,r)}finally{as=!1,(yr!==null||xr!==null)&&(oc(),nc())}}function fo(e,t){var r=e.stateNode;if(r===null)return null;var i=Il(r);if(i===null)return null;r=i[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(i=!i.disabled)||(e=e.type,i=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!i;break e;default:e=!1}if(e)return null;if(r&&typeof r!="function")throw Error(l(231,t,typeof r));return r}var us=!1;if(f)try{var po={};Object.defineProperty(po,"passive",{get:function(){us=!0}}),window.addEventListener("test",po,po),window.removeEventListener("test",po,po)}catch{us=!1}function pg(e,t,r,i,u,d,v,b,R){var M=Array.prototype.slice.call(arguments,3);try{t.apply(r,M)}catch(K){this.onError(K)}}var mo=!1,ml=null,hl=!1,cs=null,mg={onError:function(e){mo=!0,ml=e}};function hg(e,t,r,i,u,d,v,b,R){mo=!1,ml=null,pg.apply(mg,arguments)}function gg(e,t,r,i,u,d,v,b,R){if(hg.apply(this,arguments),mo){if(mo){var M=ml;mo=!1,ml=null}else throw Error(l(198));hl||(hl=!0,cs=M)}}function Qn(e){var t=e,r=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,(t.flags&4098)!==0&&(r=t.return),e=t.return;while(e)}return t.tag===3?r:null}function ic(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function sc(e){if(Qn(e)!==e)throw Error(l(188))}function vg(e){var t=e.alternate;if(!t){if(t=Qn(e),t===null)throw Error(l(188));return t!==e?null:e}for(var r=e,i=t;;){var u=r.return;if(u===null)break;var d=u.alternate;if(d===null){if(i=u.return,i!==null){r=i;continue}break}if(u.child===d.child){for(d=u.child;d;){if(d===r)return sc(u),e;if(d===i)return sc(u),t;d=d.sibling}throw Error(l(188))}if(r.return!==i.return)r=u,i=d;else{for(var v=!1,b=u.child;b;){if(b===r){v=!0,r=u,i=d;break}if(b===i){v=!0,i=u,r=d;break}b=b.sibling}if(!v){for(b=d.child;b;){if(b===r){v=!0,r=d,i=u;break}if(b===i){v=!0,i=d,r=u;break}b=b.sibling}if(!v)throw Error(l(189))}}if(r.alternate!==i)throw Error(l(190))}if(r.tag!==3)throw Error(l(188));return r.stateNode.current===r?e:t}function ac(e){return e=vg(e),e!==null?uc(e):null}function uc(e){if(e.tag===5||e.tag===6)return e;for(e=e.child;e!==null;){var t=uc(e);if(t!==null)return t;e=e.sibling}return null}var cc=o.unstable_scheduleCallback,dc=o.unstable_cancelCallback,yg=o.unstable_shouldYield,xg=o.unstable_requestPaint,$e=o.unstable_now,wg=o.unstable_getCurrentPriorityLevel,ds=o.unstable_ImmediatePriority,fc=o.unstable_UserBlockingPriority,gl=o.unstable_NormalPriority,Sg=o.unstable_LowPriority,pc=o.unstable_IdlePriority,vl=null,Qt=null;function Cg(e){if(Qt&&typeof Qt.onCommitFiberRoot=="function")try{Qt.onCommitFiberRoot(vl,e,void 0,(e.current.flags&128)===128)}catch{}}var It=Math.clz32?Math.clz32:kg,Eg=Math.log,bg=Math.LN2;function kg(e){return e>>>=0,e===0?32:31-(Eg(e)/bg|0)|0}var yl=64,xl=4194304;function ho(e){switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194240;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return e&130023424;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return e}}function wl(e,t){var r=e.pendingLanes;if(r===0)return 0;var i=0,u=e.suspendedLanes,d=e.pingedLanes,v=r&268435455;if(v!==0){var b=v&~u;b!==0?i=ho(b):(d&=v,d!==0&&(i=ho(d)))}else v=r&~u,v!==0?i=ho(v):d!==0&&(i=ho(d));if(i===0)return 0;if(t!==0&&t!==i&&(t&u)===0&&(u=i&-i,d=t&-t,u>=d||u===16&&(d&4194240)!==0))return t;if((i&4)!==0&&(i|=r&16),t=e.entangledLanes,t!==0)for(e=e.entanglements,t&=i;0<t;)r=31-It(t),u=1<<r,i|=e[r],t&=~u;return i}function Pg(e,t){switch(e){case 1:case 2:case 4:return t+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return-1;case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function Ng(e,t){for(var r=e.suspendedLanes,i=e.pingedLanes,u=e.expirationTimes,d=e.pendingLanes;0<d;){var v=31-It(d),b=1<<v,R=u[v];R===-1?((b&r)===0||(b&i)!==0)&&(u[v]=Pg(b,t)):R<=t&&(e.expiredLanes|=b),d&=~b}}function fs(e){return e=e.pendingLanes&-1073741825,e!==0?e:e&1073741824?1073741824:0}function mc(){var e=yl;return yl<<=1,(yl&4194240)===0&&(yl=64),e}function ps(e){for(var t=[],r=0;31>r;r++)t.push(e);return t}function go(e,t,r){e.pendingLanes|=t,t!==536870912&&(e.suspendedLanes=0,e.pingedLanes=0),e=e.eventTimes,t=31-It(t),e[t]=r}function Rg(e,t){var r=e.pendingLanes&~t;e.pendingLanes=t,e.suspendedLanes=0,e.pingedLanes=0,e.expiredLanes&=t,e.mutableReadLanes&=t,e.entangledLanes&=t,t=e.entanglements;var i=e.eventTimes;for(e=e.expirationTimes;0<r;){var u=31-It(r),d=1<<u;t[u]=0,i[u]=-1,e[u]=-1,r&=~d}}function ms(e,t){var r=e.entangledLanes|=t;for(e=e.entanglements;r;){var i=31-It(r),u=1<<i;u&t|e[i]&t&&(e[i]|=t),r&=~u}}var _e=0;function hc(e){return e&=-e,1<e?4<e?(e&268435455)!==0?16:536870912:4:1}var gc,hs,vc,yc,xc,gs=!1,Sl=[],vn=null,yn=null,xn=null,vo=new Map,yo=new Map,wn=[],_g="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function wc(e,t){switch(e){case"focusin":case"focusout":vn=null;break;case"dragenter":case"dragleave":yn=null;break;case"mouseover":case"mouseout":xn=null;break;case"pointerover":case"pointerout":vo.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":yo.delete(t.pointerId)}}function xo(e,t,r,i,u,d){return e===null||e.nativeEvent!==d?(e={blockedOn:t,domEventName:r,eventSystemFlags:i,nativeEvent:d,targetContainers:[u]},t!==null&&(t=Ao(t),t!==null&&hs(t)),e):(e.eventSystemFlags|=i,t=e.targetContainers,u!==null&&t.indexOf(u)===-1&&t.push(u),e)}function Tg(e,t,r,i,u){switch(t){case"focusin":return vn=xo(vn,e,t,r,i,u),!0;case"dragenter":return yn=xo(yn,e,t,r,i,u),!0;case"mouseover":return xn=xo(xn,e,t,r,i,u),!0;case"pointerover":var d=u.pointerId;return vo.set(d,xo(vo.get(d)||null,e,t,r,i,u)),!0;case"gotpointercapture":return d=u.pointerId,yo.set(d,xo(yo.get(d)||null,e,t,r,i,u)),!0}return!1}function Sc(e){var t=Gn(e.target);if(t!==null){var r=Qn(t);if(r!==null){if(t=r.tag,t===13){if(t=ic(r),t!==null){e.blockedOn=t,xc(e.priority,function(){vc(r)});return}}else if(t===3&&r.stateNode.current.memoizedState.isDehydrated){e.blockedOn=r.tag===3?r.stateNode.containerInfo:null;return}}}e.blockedOn=null}function Cl(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var r=ys(e.domEventName,e.eventSystemFlags,t[0],e.nativeEvent);if(r===null){r=e.nativeEvent;var i=new r.constructor(r.type,r);ls=i,r.target.dispatchEvent(i),ls=null}else return t=Ao(r),t!==null&&hs(t),e.blockedOn=r,!1;t.shift()}return!0}function Cc(e,t,r){Cl(e)&&r.delete(t)}function jg(){gs=!1,vn!==null&&Cl(vn)&&(vn=null),yn!==null&&Cl(yn)&&(yn=null),xn!==null&&Cl(xn)&&(xn=null),vo.forEach(Cc),yo.forEach(Cc)}function wo(e,t){e.blockedOn===t&&(e.blockedOn=null,gs||(gs=!0,o.unstable_scheduleCallback(o.unstable_NormalPriority,jg)))}function So(e){function t(u){return wo(u,e)}if(0<Sl.length){wo(Sl[0],e);for(var r=1;r<Sl.length;r++){var i=Sl[r];i.blockedOn===e&&(i.blockedOn=null)}}for(vn!==null&&wo(vn,e),yn!==null&&wo(yn,e),xn!==null&&wo(xn,e),vo.forEach(t),yo.forEach(t),r=0;r<wn.length;r++)i=wn[r],i.blockedOn===e&&(i.blockedOn=null);for(;0<wn.length&&(r=wn[0],r.blockedOn===null);)Sc(r),r.blockedOn===null&&wn.shift()}var wr=O.ReactCurrentBatchConfig,El=!0;function zg(e,t,r,i){var u=_e,d=wr.transition;wr.transition=null;try{_e=1,vs(e,t,r,i)}finally{_e=u,wr.transition=d}}function Ag(e,t,r,i){var u=_e,d=wr.transition;wr.transition=null;try{_e=4,vs(e,t,r,i)}finally{_e=u,wr.transition=d}}function vs(e,t,r,i){if(El){var u=ys(e,t,r,i);if(u===null)Ms(e,t,i,bl,r),wc(e,i);else if(Tg(u,e,t,r,i))i.stopPropagation();else if(wc(e,i),t&4&&-1<_g.indexOf(e)){for(;u!==null;){var d=Ao(u);if(d!==null&&gc(d),d=ys(e,t,r,i),d===null&&Ms(e,t,i,bl,r),d===u)break;u=d}u!==null&&i.stopPropagation()}else Ms(e,t,i,null,r)}}var bl=null;function ys(e,t,r,i){if(bl=null,e=is(i),e=Gn(e),e!==null)if(t=Qn(e),t===null)e=null;else if(r=t.tag,r===13){if(e=ic(t),e!==null)return e;e=null}else if(r===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null);return bl=e,null}function Ec(e){switch(e){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(wg()){case ds:return 1;case fc:return 4;case gl:case Sg:return 16;case pc:return 536870912;default:return 16}default:return 16}}var Sn=null,xs=null,kl=null;function bc(){if(kl)return kl;var e,t=xs,r=t.length,i,u="value"in Sn?Sn.value:Sn.textContent,d=u.length;for(e=0;e<r&&t[e]===u[e];e++);var v=r-e;for(i=1;i<=v&&t[r-i]===u[d-i];i++);return kl=u.slice(e,1<i?1-i:void 0)}function Pl(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function Nl(){return!0}function kc(){return!1}function yt(e){function t(r,i,u,d,v){this._reactName=r,this._targetInst=u,this.type=i,this.nativeEvent=d,this.target=v,this.currentTarget=null;for(var b in e)e.hasOwnProperty(b)&&(r=e[b],this[b]=r?r(d):d[b]);return this.isDefaultPrevented=(d.defaultPrevented!=null?d.defaultPrevented:d.returnValue===!1)?Nl:kc,this.isPropagationStopped=kc,this}return G(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var r=this.nativeEvent;r&&(r.preventDefault?r.preventDefault():typeof r.returnValue!="unknown"&&(r.returnValue=!1),this.isDefaultPrevented=Nl)},stopPropagation:function(){var r=this.nativeEvent;r&&(r.stopPropagation?r.stopPropagation():typeof r.cancelBubble!="unknown"&&(r.cancelBubble=!0),this.isPropagationStopped=Nl)},persist:function(){},isPersistent:Nl}),t}var Sr={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},ws=yt(Sr),Co=G({},Sr,{view:0,detail:0}),Lg=yt(Co),Ss,Cs,Eo,Rl=G({},Co,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:bs,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==Eo&&(Eo&&e.type==="mousemove"?(Ss=e.screenX-Eo.screenX,Cs=e.screenY-Eo.screenY):Cs=Ss=0,Eo=e),Ss)},movementY:function(e){return"movementY"in e?e.movementY:Cs}}),Pc=yt(Rl),Mg=G({},Rl,{dataTransfer:0}),Ig=yt(Mg),Og=G({},Co,{relatedTarget:0}),Es=yt(Og),Dg=G({},Sr,{animationName:0,elapsedTime:0,pseudoElement:0}),$g=yt(Dg),Fg=G({},Sr,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),Wg=yt(Fg),Vg=G({},Sr,{data:0}),Nc=yt(Vg),Hg={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},Bg={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},Ug={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function Kg(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=Ug[e])?!!t[e]:!1}function bs(){return Kg}var Qg=G({},Co,{key:function(e){if(e.key){var t=Hg[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=Pl(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?Bg[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:bs,charCode:function(e){return e.type==="keypress"?Pl(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?Pl(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),Gg=yt(Qg),Yg=G({},Rl,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),Rc=yt(Yg),Xg=G({},Co,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:bs}),Zg=yt(Xg),qg=G({},Sr,{propertyName:0,elapsedTime:0,pseudoElement:0}),Jg=yt(qg),ev=G({},Rl,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),tv=yt(ev),nv=[9,13,27,32],ks=f&&"CompositionEvent"in window,bo=null;f&&"documentMode"in document&&(bo=document.documentMode);var rv=f&&"TextEvent"in window&&!bo,_c=f&&(!ks||bo&&8<bo&&11>=bo),Tc=" ",jc=!1;function zc(e,t){switch(e){case"keyup":return nv.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function Ac(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var Cr=!1;function ov(e,t){switch(e){case"compositionend":return Ac(t);case"keypress":return t.which!==32?null:(jc=!0,Tc);case"textInput":return e=t.data,e===Tc&&jc?null:e;default:return null}}function lv(e,t){if(Cr)return e==="compositionend"||!ks&&zc(e,t)?(e=bc(),kl=xs=Sn=null,Cr=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return _c&&t.locale!=="ko"?null:t.data;default:return null}}var iv={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function Lc(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!iv[e.type]:t==="textarea"}function Mc(e,t,r,i){tc(i),t=Al(t,"onChange"),0<t.length&&(r=new ws("onChange","change",null,r,i),e.push({event:r,listeners:t}))}var ko=null,Po=null;function sv(e){Jc(e,0)}function _l(e){var t=Nr(e);if(we(t))return e}function av(e,t){if(e==="change")return t}var Ic=!1;if(f){var Ps;if(f){var Ns="oninput"in document;if(!Ns){var Oc=document.createElement("div");Oc.setAttribute("oninput","return;"),Ns=typeof Oc.oninput=="function"}Ps=Ns}else Ps=!1;Ic=Ps&&(!document.documentMode||9<document.documentMode)}function Dc(){ko&&(ko.detachEvent("onpropertychange",$c),Po=ko=null)}function $c(e){if(e.propertyName==="value"&&_l(Po)){var t=[];Mc(t,Po,e,is(e)),lc(sv,t)}}function uv(e,t,r){e==="focusin"?(Dc(),ko=t,Po=r,ko.attachEvent("onpropertychange",$c)):e==="focusout"&&Dc()}function cv(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return _l(Po)}function dv(e,t){if(e==="click")return _l(t)}function fv(e,t){if(e==="input"||e==="change")return _l(t)}function pv(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var Ot=typeof Object.is=="function"?Object.is:pv;function No(e,t){if(Ot(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var r=Object.keys(e),i=Object.keys(t);if(r.length!==i.length)return!1;for(i=0;i<r.length;i++){var u=r[i];if(!m.call(t,u)||!Ot(e[u],t[u]))return!1}return!0}function Fc(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function Wc(e,t){var r=Fc(e);e=0;for(var i;r;){if(r.nodeType===3){if(i=e+r.textContent.length,e<=t&&i>=t)return{node:r,offset:t-e};e=i}e:{for(;r;){if(r.nextSibling){r=r.nextSibling;break e}r=r.parentNode}r=void 0}r=Fc(r)}}function Vc(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?Vc(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function Hc(){for(var e=window,t=Se();t instanceof e.HTMLIFrameElement;){try{var r=typeof t.contentWindow.location.href=="string"}catch{r=!1}if(r)e=t.contentWindow;else break;t=Se(e.document)}return t}function Rs(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}function mv(e){var t=Hc(),r=e.focusedElem,i=e.selectionRange;if(t!==r&&r&&r.ownerDocument&&Vc(r.ownerDocument.documentElement,r)){if(i!==null&&Rs(r)){if(t=i.start,e=i.end,e===void 0&&(e=t),"selectionStart"in r)r.selectionStart=t,r.selectionEnd=Math.min(e,r.value.length);else if(e=(t=r.ownerDocument||document)&&t.defaultView||window,e.getSelection){e=e.getSelection();var u=r.textContent.length,d=Math.min(i.start,u);i=i.end===void 0?d:Math.min(i.end,u),!e.extend&&d>i&&(u=i,i=d,d=u),u=Wc(r,d);var v=Wc(r,i);u&&v&&(e.rangeCount!==1||e.anchorNode!==u.node||e.anchorOffset!==u.offset||e.focusNode!==v.node||e.focusOffset!==v.offset)&&(t=t.createRange(),t.setStart(u.node,u.offset),e.removeAllRanges(),d>i?(e.addRange(t),e.extend(v.node,v.offset)):(t.setEnd(v.node,v.offset),e.addRange(t)))}}for(t=[],e=r;e=e.parentNode;)e.nodeType===1&&t.push({element:e,left:e.scrollLeft,top:e.scrollTop});for(typeof r.focus=="function"&&r.focus(),r=0;r<t.length;r++)e=t[r],e.element.scrollLeft=e.left,e.element.scrollTop=e.top}}var hv=f&&"documentMode"in document&&11>=document.documentMode,Er=null,_s=null,Ro=null,Ts=!1;function Bc(e,t,r){var i=r.window===r?r.document:r.nodeType===9?r:r.ownerDocument;Ts||Er==null||Er!==Se(i)||(i=Er,"selectionStart"in i&&Rs(i)?i={start:i.selectionStart,end:i.selectionEnd}:(i=(i.ownerDocument&&i.ownerDocument.defaultView||window).getSelection(),i={anchorNode:i.anchorNode,anchorOffset:i.anchorOffset,focusNode:i.focusNode,focusOffset:i.focusOffset}),Ro&&No(Ro,i)||(Ro=i,i=Al(_s,"onSelect"),0<i.length&&(t=new ws("onSelect","select",null,t,r),e.push({event:t,listeners:i}),t.target=Er)))}function Tl(e,t){var r={};return r[e.toLowerCase()]=t.toLowerCase(),r["Webkit"+e]="webkit"+t,r["Moz"+e]="moz"+t,r}var br={animationend:Tl("Animation","AnimationEnd"),animationiteration:Tl("Animation","AnimationIteration"),animationstart:Tl("Animation","AnimationStart"),transitionend:Tl("Transition","TransitionEnd")},js={},Uc={};f&&(Uc=document.createElement("div").style,"AnimationEvent"in window||(delete br.animationend.animation,delete br.animationiteration.animation,delete br.animationstart.animation),"TransitionEvent"in window||delete br.transitionend.transition);function jl(e){if(js[e])return js[e];if(!br[e])return e;var t=br[e],r;for(r in t)if(t.hasOwnProperty(r)&&r in Uc)return js[e]=t[r];return e}var Kc=jl("animationend"),Qc=jl("animationiteration"),Gc=jl("animationstart"),Yc=jl("transitionend"),Xc=new Map,Zc="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function Cn(e,t){Xc.set(e,t),c(t,[e])}for(var zs=0;zs<Zc.length;zs++){var As=Zc[zs],gv=As.toLowerCase(),vv=As[0].toUpperCase()+As.slice(1);Cn(gv,"on"+vv)}Cn(Kc,"onAnimationEnd"),Cn(Qc,"onAnimationIteration"),Cn(Gc,"onAnimationStart"),Cn("dblclick","onDoubleClick"),Cn("focusin","onFocus"),Cn("focusout","onBlur"),Cn(Yc,"onTransitionEnd"),p("onMouseEnter",["mouseout","mouseover"]),p("onMouseLeave",["mouseout","mouseover"]),p("onPointerEnter",["pointerout","pointerover"]),p("onPointerLeave",["pointerout","pointerover"]),c("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),c("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),c("onBeforeInput",["compositionend","keypress","textInput","paste"]),c("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),c("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),c("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var _o="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),yv=new Set("cancel close invalid load scroll toggle".split(" ").concat(_o));function qc(e,t,r){var i=e.type||"unknown-event";e.currentTarget=r,gg(i,t,void 0,e),e.currentTarget=null}function Jc(e,t){t=(t&4)!==0;for(var r=0;r<e.length;r++){var i=e[r],u=i.event;i=i.listeners;e:{var d=void 0;if(t)for(var v=i.length-1;0<=v;v--){var b=i[v],R=b.instance,M=b.currentTarget;if(b=b.listener,R!==d&&u.isPropagationStopped())break e;qc(u,b,M),d=R}else for(v=0;v<i.length;v++){if(b=i[v],R=b.instance,M=b.currentTarget,b=b.listener,R!==d&&u.isPropagationStopped())break e;qc(u,b,M),d=R}}}if(hl)throw e=cs,hl=!1,cs=null,e}function je(e,t){var r=t[Ws];r===void 0&&(r=t[Ws]=new Set);var i=e+"__bubble";r.has(i)||(ed(t,e,2,!1),r.add(i))}function Ls(e,t,r){var i=0;t&&(i|=4),ed(r,e,i,t)}var zl="_reactListening"+Math.random().toString(36).slice(2);function To(e){if(!e[zl]){e[zl]=!0,s.forEach(function(r){r!=="selectionchange"&&(yv.has(r)||Ls(r,!1,e),Ls(r,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[zl]||(t[zl]=!0,Ls("selectionchange",!1,t))}}function ed(e,t,r,i){switch(Ec(t)){case 1:var u=zg;break;case 4:u=Ag;break;default:u=vs}r=u.bind(null,t,r,e),u=void 0,!us||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(u=!0),i?u!==void 0?e.addEventListener(t,r,{capture:!0,passive:u}):e.addEventListener(t,r,!0):u!==void 0?e.addEventListener(t,r,{passive:u}):e.addEventListener(t,r,!1)}function Ms(e,t,r,i,u){var d=i;if((t&1)===0&&(t&2)===0&&i!==null)e:for(;;){if(i===null)return;var v=i.tag;if(v===3||v===4){var b=i.stateNode.containerInfo;if(b===u||b.nodeType===8&&b.parentNode===u)break;if(v===4)for(v=i.return;v!==null;){var R=v.tag;if((R===3||R===4)&&(R=v.stateNode.containerInfo,R===u||R.nodeType===8&&R.parentNode===u))return;v=v.return}for(;b!==null;){if(v=Gn(b),v===null)return;if(R=v.tag,R===5||R===6){i=d=v;continue e}b=b.parentNode}}i=i.return}lc(function(){var M=d,K=is(r),Q=[];e:{var U=Xc.get(e);if(U!==void 0){var ie=ws,ce=e;switch(e){case"keypress":if(Pl(r)===0)break e;case"keydown":case"keyup":ie=Gg;break;case"focusin":ce="focus",ie=Es;break;case"focusout":ce="blur",ie=Es;break;case"beforeblur":case"afterblur":ie=Es;break;case"click":if(r.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":ie=Pc;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":ie=Ig;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":ie=Zg;break;case Kc:case Qc:case Gc:ie=$g;break;case Yc:ie=Jg;break;case"scroll":ie=Lg;break;case"wheel":ie=tv;break;case"copy":case"cut":case"paste":ie=Wg;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":ie=Rc}var pe=(t&4)!==0,Fe=!pe&&e==="scroll",z=pe?U!==null?U+"Capture":null:U;pe=[];for(var T=M,L;T!==null;){L=T;var X=L.stateNode;if(L.tag===5&&X!==null&&(L=X,z!==null&&(X=fo(T,z),X!=null&&pe.push(jo(T,X,L)))),Fe)break;T=T.return}0<pe.length&&(U=new ie(U,ce,null,r,K),Q.push({event:U,listeners:pe}))}}if((t&7)===0){e:{if(U=e==="mouseover"||e==="pointerover",ie=e==="mouseout"||e==="pointerout",U&&r!==ls&&(ce=r.relatedTarget||r.fromElement)&&(Gn(ce)||ce[on]))break e;if((ie||U)&&(U=K.window===K?K:(U=K.ownerDocument)?U.defaultView||U.parentWindow:window,ie?(ce=r.relatedTarget||r.toElement,ie=M,ce=ce?Gn(ce):null,ce!==null&&(Fe=Qn(ce),ce!==Fe||ce.tag!==5&&ce.tag!==6)&&(ce=null)):(ie=null,ce=M),ie!==ce)){if(pe=Pc,X="onMouseLeave",z="onMouseEnter",T="mouse",(e==="pointerout"||e==="pointerover")&&(pe=Rc,X="onPointerLeave",z="onPointerEnter",T="pointer"),Fe=ie==null?U:Nr(ie),L=ce==null?U:Nr(ce),U=new pe(X,T+"leave",ie,r,K),U.target=Fe,U.relatedTarget=L,X=null,Gn(K)===M&&(pe=new pe(z,T+"enter",ce,r,K),pe.target=L,pe.relatedTarget=Fe,X=pe),Fe=X,ie&&ce)t:{for(pe=ie,z=ce,T=0,L=pe;L;L=kr(L))T++;for(L=0,X=z;X;X=kr(X))L++;for(;0<T-L;)pe=kr(pe),T--;for(;0<L-T;)z=kr(z),L--;for(;T--;){if(pe===z||z!==null&&pe===z.alternate)break t;pe=kr(pe),z=kr(z)}pe=null}else pe=null;ie!==null&&td(Q,U,ie,pe,!1),ce!==null&&Fe!==null&&td(Q,Fe,ce,pe,!0)}}e:{if(U=M?Nr(M):window,ie=U.nodeName&&U.nodeName.toLowerCase(),ie==="select"||ie==="input"&&U.type==="file")var he=av;else if(Lc(U))if(Ic)he=fv;else{he=cv;var ye=uv}else(ie=U.nodeName)&&ie.toLowerCase()==="input"&&(U.type==="checkbox"||U.type==="radio")&&(he=dv);if(he&&(he=he(e,M))){Mc(Q,he,r,K);break e}ye&&ye(e,U,M),e==="focusout"&&(ye=U._wrapperState)&&ye.controlled&&U.type==="number"&&Mt(U,"number",U.value)}switch(ye=M?Nr(M):window,e){case"focusin":(Lc(ye)||ye.contentEditable==="true")&&(Er=ye,_s=M,Ro=null);break;case"focusout":Ro=_s=Er=null;break;case"mousedown":Ts=!0;break;case"contextmenu":case"mouseup":case"dragend":Ts=!1,Bc(Q,r,K);break;case"selectionchange":if(hv)break;case"keydown":case"keyup":Bc(Q,r,K)}var xe;if(ks)e:{switch(e){case"compositionstart":var Ce="onCompositionStart";break e;case"compositionend":Ce="onCompositionEnd";break e;case"compositionupdate":Ce="onCompositionUpdate";break e}Ce=void 0}else Cr?zc(e,r)&&(Ce="onCompositionEnd"):e==="keydown"&&r.keyCode===229&&(Ce="onCompositionStart");Ce&&(_c&&r.locale!=="ko"&&(Cr||Ce!=="onCompositionStart"?Ce==="onCompositionEnd"&&Cr&&(xe=bc()):(Sn=K,xs="value"in Sn?Sn.value:Sn.textContent,Cr=!0)),ye=Al(M,Ce),0<ye.length&&(Ce=new Nc(Ce,e,null,r,K),Q.push({event:Ce,listeners:ye}),xe?Ce.data=xe:(xe=Ac(r),xe!==null&&(Ce.data=xe)))),(xe=rv?ov(e,r):lv(e,r))&&(M=Al(M,"onBeforeInput"),0<M.length&&(K=new Nc("onBeforeInput","beforeinput",null,r,K),Q.push({event:K,listeners:M}),K.data=xe))}Jc(Q,t)})}function jo(e,t,r){return{instance:e,listener:t,currentTarget:r}}function Al(e,t){for(var r=t+"Capture",i=[];e!==null;){var u=e,d=u.stateNode;u.tag===5&&d!==null&&(u=d,d=fo(e,r),d!=null&&i.unshift(jo(e,d,u)),d=fo(e,t),d!=null&&i.push(jo(e,d,u))),e=e.return}return i}function kr(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5);return e||null}function td(e,t,r,i,u){for(var d=t._reactName,v=[];r!==null&&r!==i;){var b=r,R=b.alternate,M=b.stateNode;if(R!==null&&R===i)break;b.tag===5&&M!==null&&(b=M,u?(R=fo(r,d),R!=null&&v.unshift(jo(r,R,b))):u||(R=fo(r,d),R!=null&&v.push(jo(r,R,b)))),r=r.return}v.length!==0&&e.push({event:t,listeners:v})}var xv=/\r\n?/g,wv=/\u0000|\uFFFD/g;function nd(e){return(typeof e=="string"?e:""+e).replace(xv,`
`).replace(wv,"")}function Ll(e,t,r){if(t=nd(t),nd(e)!==t&&r)throw Error(l(425))}function Ml(){}var Is=null,Os=null;function Ds(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var $s=typeof setTimeout=="function"?setTimeout:void 0,Sv=typeof clearTimeout=="function"?clearTimeout:void 0,rd=typeof Promise=="function"?Promise:void 0,Cv=typeof queueMicrotask=="function"?queueMicrotask:typeof rd<"u"?function(e){return rd.resolve(null).then(e).catch(Ev)}:$s;function Ev(e){setTimeout(function(){throw e})}function Fs(e,t){var r=t,i=0;do{var u=r.nextSibling;if(e.removeChild(r),u&&u.nodeType===8)if(r=u.data,r==="/$"){if(i===0){e.removeChild(u),So(t);return}i--}else r!=="$"&&r!=="$?"&&r!=="$!"||i++;r=u}while(r);So(t)}function En(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?")break;if(t==="/$")return null}}return e}function od(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var r=e.data;if(r==="$"||r==="$!"||r==="$?"){if(t===0)return e;t--}else r==="/$"&&t++}e=e.previousSibling}return null}var Pr=Math.random().toString(36).slice(2),Gt="__reactFiber$"+Pr,zo="__reactProps$"+Pr,on="__reactContainer$"+Pr,Ws="__reactEvents$"+Pr,bv="__reactListeners$"+Pr,kv="__reactHandles$"+Pr;function Gn(e){var t=e[Gt];if(t)return t;for(var r=e.parentNode;r;){if(t=r[on]||r[Gt]){if(r=t.alternate,t.child!==null||r!==null&&r.child!==null)for(e=od(e);e!==null;){if(r=e[Gt])return r;e=od(e)}return t}e=r,r=e.parentNode}return null}function Ao(e){return e=e[Gt]||e[on],!e||e.tag!==5&&e.tag!==6&&e.tag!==13&&e.tag!==3?null:e}function Nr(e){if(e.tag===5||e.tag===6)return e.stateNode;throw Error(l(33))}function Il(e){return e[zo]||null}var Vs=[],Rr=-1;function bn(e){return{current:e}}function ze(e){0>Rr||(e.current=Vs[Rr],Vs[Rr]=null,Rr--)}function Te(e,t){Rr++,Vs[Rr]=e.current,e.current=t}var kn={},nt=bn(kn),ct=bn(!1),Yn=kn;function _r(e,t){var r=e.type.contextTypes;if(!r)return kn;var i=e.stateNode;if(i&&i.__reactInternalMemoizedUnmaskedChildContext===t)return i.__reactInternalMemoizedMaskedChildContext;var u={},d;for(d in r)u[d]=t[d];return i&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=t,e.__reactInternalMemoizedMaskedChildContext=u),u}function dt(e){return e=e.childContextTypes,e!=null}function Ol(){ze(ct),ze(nt)}function ld(e,t,r){if(nt.current!==kn)throw Error(l(168));Te(nt,t),Te(ct,r)}function id(e,t,r){var i=e.stateNode;if(t=t.childContextTypes,typeof i.getChildContext!="function")return r;i=i.getChildContext();for(var u in i)if(!(u in t))throw Error(l(108,F(e)||"Unknown",u));return G({},r,i)}function Dl(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||kn,Yn=nt.current,Te(nt,e),Te(ct,ct.current),!0}function sd(e,t,r){var i=e.stateNode;if(!i)throw Error(l(169));r?(e=id(e,t,Yn),i.__reactInternalMemoizedMergedChildContext=e,ze(ct),ze(nt),Te(nt,e)):ze(ct),Te(ct,r)}var ln=null,$l=!1,Hs=!1;function ad(e){ln===null?ln=[e]:ln.push(e)}function Pv(e){$l=!0,ad(e)}function Pn(){if(!Hs&&ln!==null){Hs=!0;var e=0,t=_e;try{var r=ln;for(_e=1;e<r.length;e++){var i=r[e];do i=i(!0);while(i!==null)}ln=null,$l=!1}catch(u){throw ln!==null&&(ln=ln.slice(e+1)),cc(ds,Pn),u}finally{_e=t,Hs=!1}}return null}var Tr=[],jr=0,Fl=null,Wl=0,Pt=[],Nt=0,Xn=null,sn=1,an="";function Zn(e,t){Tr[jr++]=Wl,Tr[jr++]=Fl,Fl=e,Wl=t}function ud(e,t,r){Pt[Nt++]=sn,Pt[Nt++]=an,Pt[Nt++]=Xn,Xn=e;var i=sn;e=an;var u=32-It(i)-1;i&=~(1<<u),r+=1;var d=32-It(t)+u;if(30<d){var v=u-u%5;d=(i&(1<<v)-1).toString(32),i>>=v,u-=v,sn=1<<32-It(t)+u|r<<u|i,an=d+e}else sn=1<<d|r<<u|i,an=e}function Bs(e){e.return!==null&&(Zn(e,1),ud(e,1,0))}function Us(e){for(;e===Fl;)Fl=Tr[--jr],Tr[jr]=null,Wl=Tr[--jr],Tr[jr]=null;for(;e===Xn;)Xn=Pt[--Nt],Pt[Nt]=null,an=Pt[--Nt],Pt[Nt]=null,sn=Pt[--Nt],Pt[Nt]=null}var xt=null,wt=null,Le=!1,Dt=null;function cd(e,t){var r=jt(5,null,null,0);r.elementType="DELETED",r.stateNode=t,r.return=e,t=e.deletions,t===null?(e.deletions=[r],e.flags|=16):t.push(r)}function dd(e,t){switch(e.tag){case 5:var r=e.type;return t=t.nodeType!==1||r.toLowerCase()!==t.nodeName.toLowerCase()?null:t,t!==null?(e.stateNode=t,xt=e,wt=En(t.firstChild),!0):!1;case 6:return t=e.pendingProps===""||t.nodeType!==3?null:t,t!==null?(e.stateNode=t,xt=e,wt=null,!0):!1;case 13:return t=t.nodeType!==8?null:t,t!==null?(r=Xn!==null?{id:sn,overflow:an}:null,e.memoizedState={dehydrated:t,treeContext:r,retryLane:1073741824},r=jt(18,null,null,0),r.stateNode=t,r.return=e,e.child=r,xt=e,wt=null,!0):!1;default:return!1}}function Ks(e){return(e.mode&1)!==0&&(e.flags&128)===0}function Qs(e){if(Le){var t=wt;if(t){var r=t;if(!dd(e,t)){if(Ks(e))throw Error(l(418));t=En(r.nextSibling);var i=xt;t&&dd(e,t)?cd(i,r):(e.flags=e.flags&-4097|2,Le=!1,xt=e)}}else{if(Ks(e))throw Error(l(418));e.flags=e.flags&-4097|2,Le=!1,xt=e}}}function fd(e){for(e=e.return;e!==null&&e.tag!==5&&e.tag!==3&&e.tag!==13;)e=e.return;xt=e}function Vl(e){if(e!==xt)return!1;if(!Le)return fd(e),Le=!0,!1;var t;if((t=e.tag!==3)&&!(t=e.tag!==5)&&(t=e.type,t=t!=="head"&&t!=="body"&&!Ds(e.type,e.memoizedProps)),t&&(t=wt)){if(Ks(e))throw pd(),Error(l(418));for(;t;)cd(e,t),t=En(t.nextSibling)}if(fd(e),e.tag===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(l(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8){var r=e.data;if(r==="/$"){if(t===0){wt=En(e.nextSibling);break e}t--}else r!=="$"&&r!=="$!"&&r!=="$?"||t++}e=e.nextSibling}wt=null}}else wt=xt?En(e.stateNode.nextSibling):null;return!0}function pd(){for(var e=wt;e;)e=En(e.nextSibling)}function zr(){wt=xt=null,Le=!1}function Gs(e){Dt===null?Dt=[e]:Dt.push(e)}var Nv=O.ReactCurrentBatchConfig;function Lo(e,t,r){if(e=r.ref,e!==null&&typeof e!="function"&&typeof e!="object"){if(r._owner){if(r=r._owner,r){if(r.tag!==1)throw Error(l(309));var i=r.stateNode}if(!i)throw Error(l(147,e));var u=i,d=""+e;return t!==null&&t.ref!==null&&typeof t.ref=="function"&&t.ref._stringRef===d?t.ref:(t=function(v){var b=u.refs;v===null?delete b[d]:b[d]=v},t._stringRef=d,t)}if(typeof e!="string")throw Error(l(284));if(!r._owner)throw Error(l(290,e))}return e}function Hl(e,t){throw e=Object.prototype.toString.call(t),Error(l(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function md(e){var t=e._init;return t(e._payload)}function hd(e){function t(z,T){if(e){var L=z.deletions;L===null?(z.deletions=[T],z.flags|=16):L.push(T)}}function r(z,T){if(!e)return null;for(;T!==null;)t(z,T),T=T.sibling;return null}function i(z,T){for(z=new Map;T!==null;)T.key!==null?z.set(T.key,T):z.set(T.index,T),T=T.sibling;return z}function u(z,T){return z=Ln(z,T),z.index=0,z.sibling=null,z}function d(z,T,L){return z.index=L,e?(L=z.alternate,L!==null?(L=L.index,L<T?(z.flags|=2,T):L):(z.flags|=2,T)):(z.flags|=1048576,T)}function v(z){return e&&z.alternate===null&&(z.flags|=2),z}function b(z,T,L,X){return T===null||T.tag!==6?(T=$a(L,z.mode,X),T.return=z,T):(T=u(T,L),T.return=z,T)}function R(z,T,L,X){var he=L.type;return he===D?K(z,T,L.props.children,X,L.key):T!==null&&(T.elementType===he||typeof he=="object"&&he!==null&&he.$$typeof===le&&md(he)===T.type)?(X=u(T,L.props),X.ref=Lo(z,T,L),X.return=z,X):(X=pi(L.type,L.key,L.props,null,z.mode,X),X.ref=Lo(z,T,L),X.return=z,X)}function M(z,T,L,X){return T===null||T.tag!==4||T.stateNode.containerInfo!==L.containerInfo||T.stateNode.implementation!==L.implementation?(T=Fa(L,z.mode,X),T.return=z,T):(T=u(T,L.children||[]),T.return=z,T)}function K(z,T,L,X,he){return T===null||T.tag!==7?(T=lr(L,z.mode,X,he),T.return=z,T):(T=u(T,L),T.return=z,T)}function Q(z,T,L){if(typeof T=="string"&&T!==""||typeof T=="number")return T=$a(""+T,z.mode,L),T.return=z,T;if(typeof T=="object"&&T!==null){switch(T.$$typeof){case I:return L=pi(T.type,T.key,T.props,null,z.mode,L),L.ref=Lo(z,null,T),L.return=z,L;case B:return T=Fa(T,z.mode,L),T.return=z,T;case le:var X=T._init;return Q(z,X(T._payload),L)}if(Kn(T)||q(T))return T=lr(T,z.mode,L,null),T.return=z,T;Hl(z,T)}return null}function U(z,T,L,X){var he=T!==null?T.key:null;if(typeof L=="string"&&L!==""||typeof L=="number")return he!==null?null:b(z,T,""+L,X);if(typeof L=="object"&&L!==null){switch(L.$$typeof){case I:return L.key===he?R(z,T,L,X):null;case B:return L.key===he?M(z,T,L,X):null;case le:return he=L._init,U(z,T,he(L._payload),X)}if(Kn(L)||q(L))return he!==null?null:K(z,T,L,X,null);Hl(z,L)}return null}function ie(z,T,L,X,he){if(typeof X=="string"&&X!==""||typeof X=="number")return z=z.get(L)||null,b(T,z,""+X,he);if(typeof X=="object"&&X!==null){switch(X.$$typeof){case I:return z=z.get(X.key===null?L:X.key)||null,R(T,z,X,he);case B:return z=z.get(X.key===null?L:X.key)||null,M(T,z,X,he);case le:var ye=X._init;return ie(z,T,L,ye(X._payload),he)}if(Kn(X)||q(X))return z=z.get(L)||null,K(T,z,X,he,null);Hl(T,X)}return null}function ce(z,T,L,X){for(var he=null,ye=null,xe=T,Ce=T=0,Xe=null;xe!==null&&Ce<L.length;Ce++){xe.index>Ce?(Xe=xe,xe=null):Xe=xe.sibling;var Re=U(z,xe,L[Ce],X);if(Re===null){xe===null&&(xe=Xe);break}e&&xe&&Re.alternate===null&&t(z,xe),T=d(Re,T,Ce),ye===null?he=Re:ye.sibling=Re,ye=Re,xe=Xe}if(Ce===L.length)return r(z,xe),Le&&Zn(z,Ce),he;if(xe===null){for(;Ce<L.length;Ce++)xe=Q(z,L[Ce],X),xe!==null&&(T=d(xe,T,Ce),ye===null?he=xe:ye.sibling=xe,ye=xe);return Le&&Zn(z,Ce),he}for(xe=i(z,xe);Ce<L.length;Ce++)Xe=ie(xe,z,Ce,L[Ce],X),Xe!==null&&(e&&Xe.alternate!==null&&xe.delete(Xe.key===null?Ce:Xe.key),T=d(Xe,T,Ce),ye===null?he=Xe:ye.sibling=Xe,ye=Xe);return e&&xe.forEach(function(Mn){return t(z,Mn)}),Le&&Zn(z,Ce),he}function pe(z,T,L,X){var he=q(L);if(typeof he!="function")throw Error(l(150));if(L=he.call(L),L==null)throw Error(l(151));for(var ye=he=null,xe=T,Ce=T=0,Xe=null,Re=L.next();xe!==null&&!Re.done;Ce++,Re=L.next()){xe.index>Ce?(Xe=xe,xe=null):Xe=xe.sibling;var Mn=U(z,xe,Re.value,X);if(Mn===null){xe===null&&(xe=Xe);break}e&&xe&&Mn.alternate===null&&t(z,xe),T=d(Mn,T,Ce),ye===null?he=Mn:ye.sibling=Mn,ye=Mn,xe=Xe}if(Re.done)return r(z,xe),Le&&Zn(z,Ce),he;if(xe===null){for(;!Re.done;Ce++,Re=L.next())Re=Q(z,Re.value,X),Re!==null&&(T=d(Re,T,Ce),ye===null?he=Re:ye.sibling=Re,ye=Re);return Le&&Zn(z,Ce),he}for(xe=i(z,xe);!Re.done;Ce++,Re=L.next())Re=ie(xe,z,Ce,Re.value,X),Re!==null&&(e&&Re.alternate!==null&&xe.delete(Re.key===null?Ce:Re.key),T=d(Re,T,Ce),ye===null?he=Re:ye.sibling=Re,ye=Re);return e&&xe.forEach(function(iy){return t(z,iy)}),Le&&Zn(z,Ce),he}function Fe(z,T,L,X){if(typeof L=="object"&&L!==null&&L.type===D&&L.key===null&&(L=L.props.children),typeof L=="object"&&L!==null){switch(L.$$typeof){case I:e:{for(var he=L.key,ye=T;ye!==null;){if(ye.key===he){if(he=L.type,he===D){if(ye.tag===7){r(z,ye.sibling),T=u(ye,L.props.children),T.return=z,z=T;break e}}else if(ye.elementType===he||typeof he=="object"&&he!==null&&he.$$typeof===le&&md(he)===ye.type){r(z,ye.sibling),T=u(ye,L.props),T.ref=Lo(z,ye,L),T.return=z,z=T;break e}r(z,ye);break}else t(z,ye);ye=ye.sibling}L.type===D?(T=lr(L.props.children,z.mode,X,L.key),T.return=z,z=T):(X=pi(L.type,L.key,L.props,null,z.mode,X),X.ref=Lo(z,T,L),X.return=z,z=X)}return v(z);case B:e:{for(ye=L.key;T!==null;){if(T.key===ye)if(T.tag===4&&T.stateNode.containerInfo===L.containerInfo&&T.stateNode.implementation===L.implementation){r(z,T.sibling),T=u(T,L.children||[]),T.return=z,z=T;break e}else{r(z,T);break}else t(z,T);T=T.sibling}T=Fa(L,z.mode,X),T.return=z,z=T}return v(z);case le:return ye=L._init,Fe(z,T,ye(L._payload),X)}if(Kn(L))return ce(z,T,L,X);if(q(L))return pe(z,T,L,X);Hl(z,L)}return typeof L=="string"&&L!==""||typeof L=="number"?(L=""+L,T!==null&&T.tag===6?(r(z,T.sibling),T=u(T,L),T.return=z,z=T):(r(z,T),T=$a(L,z.mode,X),T.return=z,z=T),v(z)):r(z,T)}return Fe}var Ar=hd(!0),gd=hd(!1),Bl=bn(null),Ul=null,Lr=null,Ys=null;function Xs(){Ys=Lr=Ul=null}function Zs(e){var t=Bl.current;ze(Bl),e._currentValue=t}function qs(e,t,r){for(;e!==null;){var i=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,i!==null&&(i.childLanes|=t)):i!==null&&(i.childLanes&t)!==t&&(i.childLanes|=t),e===r)break;e=e.return}}function Mr(e,t){Ul=e,Ys=Lr=null,e=e.dependencies,e!==null&&e.firstContext!==null&&((e.lanes&t)!==0&&(ft=!0),e.firstContext=null)}function Rt(e){var t=e._currentValue;if(Ys!==e)if(e={context:e,memoizedValue:t,next:null},Lr===null){if(Ul===null)throw Error(l(308));Lr=e,Ul.dependencies={lanes:0,firstContext:e}}else Lr=Lr.next=e;return t}var qn=null;function Js(e){qn===null?qn=[e]:qn.push(e)}function vd(e,t,r,i){var u=t.interleaved;return u===null?(r.next=r,Js(t)):(r.next=u.next,u.next=r),t.interleaved=r,un(e,i)}function un(e,t){e.lanes|=t;var r=e.alternate;for(r!==null&&(r.lanes|=t),r=e,e=e.return;e!==null;)e.childLanes|=t,r=e.alternate,r!==null&&(r.childLanes|=t),r=e,e=e.return;return r.tag===3?r.stateNode:null}var Nn=!1;function ea(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function yd(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,effects:e.effects})}function cn(e,t){return{eventTime:e,lane:t,tag:0,payload:null,callback:null,next:null}}function Rn(e,t,r){var i=e.updateQueue;if(i===null)return null;if(i=i.shared,(Ne&2)!==0){var u=i.pending;return u===null?t.next=t:(t.next=u.next,u.next=t),i.pending=t,un(e,r)}return u=i.interleaved,u===null?(t.next=t,Js(i)):(t.next=u.next,u.next=t),i.interleaved=t,un(e,r)}function Kl(e,t,r){if(t=t.updateQueue,t!==null&&(t=t.shared,(r&4194240)!==0)){var i=t.lanes;i&=e.pendingLanes,r|=i,t.lanes=r,ms(e,r)}}function xd(e,t){var r=e.updateQueue,i=e.alternate;if(i!==null&&(i=i.updateQueue,r===i)){var u=null,d=null;if(r=r.firstBaseUpdate,r!==null){do{var v={eventTime:r.eventTime,lane:r.lane,tag:r.tag,payload:r.payload,callback:r.callback,next:null};d===null?u=d=v:d=d.next=v,r=r.next}while(r!==null);d===null?u=d=t:d=d.next=t}else u=d=t;r={baseState:i.baseState,firstBaseUpdate:u,lastBaseUpdate:d,shared:i.shared,effects:i.effects},e.updateQueue=r;return}e=r.lastBaseUpdate,e===null?r.firstBaseUpdate=t:e.next=t,r.lastBaseUpdate=t}function Ql(e,t,r,i){var u=e.updateQueue;Nn=!1;var d=u.firstBaseUpdate,v=u.lastBaseUpdate,b=u.shared.pending;if(b!==null){u.shared.pending=null;var R=b,M=R.next;R.next=null,v===null?d=M:v.next=M,v=R;var K=e.alternate;K!==null&&(K=K.updateQueue,b=K.lastBaseUpdate,b!==v&&(b===null?K.firstBaseUpdate=M:b.next=M,K.lastBaseUpdate=R))}if(d!==null){var Q=u.baseState;v=0,K=M=R=null,b=d;do{var U=b.lane,ie=b.eventTime;if((i&U)===U){K!==null&&(K=K.next={eventTime:ie,lane:0,tag:b.tag,payload:b.payload,callback:b.callback,next:null});e:{var ce=e,pe=b;switch(U=t,ie=r,pe.tag){case 1:if(ce=pe.payload,typeof ce=="function"){Q=ce.call(ie,Q,U);break e}Q=ce;break e;case 3:ce.flags=ce.flags&-65537|128;case 0:if(ce=pe.payload,U=typeof ce=="function"?ce.call(ie,Q,U):ce,U==null)break e;Q=G({},Q,U);break e;case 2:Nn=!0}}b.callback!==null&&b.lane!==0&&(e.flags|=64,U=u.effects,U===null?u.effects=[b]:U.push(b))}else ie={eventTime:ie,lane:U,tag:b.tag,payload:b.payload,callback:b.callback,next:null},K===null?(M=K=ie,R=Q):K=K.next=ie,v|=U;if(b=b.next,b===null){if(b=u.shared.pending,b===null)break;U=b,b=U.next,U.next=null,u.lastBaseUpdate=U,u.shared.pending=null}}while(!0);if(K===null&&(R=Q),u.baseState=R,u.firstBaseUpdate=M,u.lastBaseUpdate=K,t=u.shared.interleaved,t!==null){u=t;do v|=u.lane,u=u.next;while(u!==t)}else d===null&&(u.shared.lanes=0);tr|=v,e.lanes=v,e.memoizedState=Q}}function wd(e,t,r){if(e=t.effects,t.effects=null,e!==null)for(t=0;t<e.length;t++){var i=e[t],u=i.callback;if(u!==null){if(i.callback=null,i=r,typeof u!="function")throw Error(l(191,u));u.call(i)}}}var Mo={},Yt=bn(Mo),Io=bn(Mo),Oo=bn(Mo);function Jn(e){if(e===Mo)throw Error(l(174));return e}function ta(e,t){switch(Te(Oo,t),Te(Io,e),Te(Yt,Mo),e=t.nodeType,e){case 9:case 11:t=(t=t.documentElement)?t.namespaceURI:ns(null,"");break;default:e=e===8?t.parentNode:t,t=e.namespaceURI||null,e=e.tagName,t=ns(t,e)}ze(Yt),Te(Yt,t)}function Ir(){ze(Yt),ze(Io),ze(Oo)}function Sd(e){Jn(Oo.current);var t=Jn(Yt.current),r=ns(t,e.type);t!==r&&(Te(Io,e),Te(Yt,r))}function na(e){Io.current===e&&(ze(Yt),ze(Io))}var Me=bn(0);function Gl(e){for(var t=e;t!==null;){if(t.tag===13){var r=t.memoizedState;if(r!==null&&(r=r.dehydrated,r===null||r.data==="$?"||r.data==="$!"))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if((t.flags&128)!==0)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}var ra=[];function oa(){for(var e=0;e<ra.length;e++)ra[e]._workInProgressVersionPrimary=null;ra.length=0}var Yl=O.ReactCurrentDispatcher,la=O.ReactCurrentBatchConfig,er=0,Ie=null,Ke=null,Ge=null,Xl=!1,Do=!1,$o=0,Rv=0;function rt(){throw Error(l(321))}function ia(e,t){if(t===null)return!1;for(var r=0;r<t.length&&r<e.length;r++)if(!Ot(e[r],t[r]))return!1;return!0}function sa(e,t,r,i,u,d){if(er=d,Ie=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,Yl.current=e===null||e.memoizedState===null?zv:Av,e=r(i,u),Do){d=0;do{if(Do=!1,$o=0,25<=d)throw Error(l(301));d+=1,Ge=Ke=null,t.updateQueue=null,Yl.current=Lv,e=r(i,u)}while(Do)}if(Yl.current=Jl,t=Ke!==null&&Ke.next!==null,er=0,Ge=Ke=Ie=null,Xl=!1,t)throw Error(l(300));return e}function aa(){var e=$o!==0;return $o=0,e}function Xt(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return Ge===null?Ie.memoizedState=Ge=e:Ge=Ge.next=e,Ge}function _t(){if(Ke===null){var e=Ie.alternate;e=e!==null?e.memoizedState:null}else e=Ke.next;var t=Ge===null?Ie.memoizedState:Ge.next;if(t!==null)Ge=t,Ke=e;else{if(e===null)throw Error(l(310));Ke=e,e={memoizedState:Ke.memoizedState,baseState:Ke.baseState,baseQueue:Ke.baseQueue,queue:Ke.queue,next:null},Ge===null?Ie.memoizedState=Ge=e:Ge=Ge.next=e}return Ge}function Fo(e,t){return typeof t=="function"?t(e):t}function ua(e){var t=_t(),r=t.queue;if(r===null)throw Error(l(311));r.lastRenderedReducer=e;var i=Ke,u=i.baseQueue,d=r.pending;if(d!==null){if(u!==null){var v=u.next;u.next=d.next,d.next=v}i.baseQueue=u=d,r.pending=null}if(u!==null){d=u.next,i=i.baseState;var b=v=null,R=null,M=d;do{var K=M.lane;if((er&K)===K)R!==null&&(R=R.next={lane:0,action:M.action,hasEagerState:M.hasEagerState,eagerState:M.eagerState,next:null}),i=M.hasEagerState?M.eagerState:e(i,M.action);else{var Q={lane:K,action:M.action,hasEagerState:M.hasEagerState,eagerState:M.eagerState,next:null};R===null?(b=R=Q,v=i):R=R.next=Q,Ie.lanes|=K,tr|=K}M=M.next}while(M!==null&&M!==d);R===null?v=i:R.next=b,Ot(i,t.memoizedState)||(ft=!0),t.memoizedState=i,t.baseState=v,t.baseQueue=R,r.lastRenderedState=i}if(e=r.interleaved,e!==null){u=e;do d=u.lane,Ie.lanes|=d,tr|=d,u=u.next;while(u!==e)}else u===null&&(r.lanes=0);return[t.memoizedState,r.dispatch]}function ca(e){var t=_t(),r=t.queue;if(r===null)throw Error(l(311));r.lastRenderedReducer=e;var i=r.dispatch,u=r.pending,d=t.memoizedState;if(u!==null){r.pending=null;var v=u=u.next;do d=e(d,v.action),v=v.next;while(v!==u);Ot(d,t.memoizedState)||(ft=!0),t.memoizedState=d,t.baseQueue===null&&(t.baseState=d),r.lastRenderedState=d}return[d,i]}function Cd(){}function Ed(e,t){var r=Ie,i=_t(),u=t(),d=!Ot(i.memoizedState,u);if(d&&(i.memoizedState=u,ft=!0),i=i.queue,da(Pd.bind(null,r,i,e),[e]),i.getSnapshot!==t||d||Ge!==null&&Ge.memoizedState.tag&1){if(r.flags|=2048,Wo(9,kd.bind(null,r,i,u,t),void 0,null),Ye===null)throw Error(l(349));(er&30)!==0||bd(r,t,u)}return u}function bd(e,t,r){e.flags|=16384,e={getSnapshot:t,value:r},t=Ie.updateQueue,t===null?(t={lastEffect:null,stores:null},Ie.updateQueue=t,t.stores=[e]):(r=t.stores,r===null?t.stores=[e]:r.push(e))}function kd(e,t,r,i){t.value=r,t.getSnapshot=i,Nd(t)&&Rd(e)}function Pd(e,t,r){return r(function(){Nd(t)&&Rd(e)})}function Nd(e){var t=e.getSnapshot;e=e.value;try{var r=t();return!Ot(e,r)}catch{return!0}}function Rd(e){var t=un(e,1);t!==null&&Vt(t,e,1,-1)}function _d(e){var t=Xt();return typeof e=="function"&&(e=e()),t.memoizedState=t.baseState=e,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:Fo,lastRenderedState:e},t.queue=e,e=e.dispatch=jv.bind(null,Ie,e),[t.memoizedState,e]}function Wo(e,t,r,i){return e={tag:e,create:t,destroy:r,deps:i,next:null},t=Ie.updateQueue,t===null?(t={lastEffect:null,stores:null},Ie.updateQueue=t,t.lastEffect=e.next=e):(r=t.lastEffect,r===null?t.lastEffect=e.next=e:(i=r.next,r.next=e,e.next=i,t.lastEffect=e)),e}function Td(){return _t().memoizedState}function Zl(e,t,r,i){var u=Xt();Ie.flags|=e,u.memoizedState=Wo(1|t,r,void 0,i===void 0?null:i)}function ql(e,t,r,i){var u=_t();i=i===void 0?null:i;var d=void 0;if(Ke!==null){var v=Ke.memoizedState;if(d=v.destroy,i!==null&&ia(i,v.deps)){u.memoizedState=Wo(t,r,d,i);return}}Ie.flags|=e,u.memoizedState=Wo(1|t,r,d,i)}function jd(e,t){return Zl(8390656,8,e,t)}function da(e,t){return ql(2048,8,e,t)}function zd(e,t){return ql(4,2,e,t)}function Ad(e,t){return ql(4,4,e,t)}function Ld(e,t){if(typeof t=="function")return e=e(),t(e),function(){t(null)};if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function Md(e,t,r){return r=r!=null?r.concat([e]):null,ql(4,4,Ld.bind(null,t,e),r)}function fa(){}function Id(e,t){var r=_t();t=t===void 0?null:t;var i=r.memoizedState;return i!==null&&t!==null&&ia(t,i[1])?i[0]:(r.memoizedState=[e,t],e)}function Od(e,t){var r=_t();t=t===void 0?null:t;var i=r.memoizedState;return i!==null&&t!==null&&ia(t,i[1])?i[0]:(e=e(),r.memoizedState=[e,t],e)}function Dd(e,t,r){return(er&21)===0?(e.baseState&&(e.baseState=!1,ft=!0),e.memoizedState=r):(Ot(r,t)||(r=mc(),Ie.lanes|=r,tr|=r,e.baseState=!0),t)}function _v(e,t){var r=_e;_e=r!==0&&4>r?r:4,e(!0);var i=la.transition;la.transition={};try{e(!1),t()}finally{_e=r,la.transition=i}}function $d(){return _t().memoizedState}function Tv(e,t,r){var i=zn(e);if(r={lane:i,action:r,hasEagerState:!1,eagerState:null,next:null},Fd(e))Wd(t,r);else if(r=vd(e,t,r,i),r!==null){var u=ut();Vt(r,e,i,u),Vd(r,t,i)}}function jv(e,t,r){var i=zn(e),u={lane:i,action:r,hasEagerState:!1,eagerState:null,next:null};if(Fd(e))Wd(t,u);else{var d=e.alternate;if(e.lanes===0&&(d===null||d.lanes===0)&&(d=t.lastRenderedReducer,d!==null))try{var v=t.lastRenderedState,b=d(v,r);if(u.hasEagerState=!0,u.eagerState=b,Ot(b,v)){var R=t.interleaved;R===null?(u.next=u,Js(t)):(u.next=R.next,R.next=u),t.interleaved=u;return}}catch{}finally{}r=vd(e,t,u,i),r!==null&&(u=ut(),Vt(r,e,i,u),Vd(r,t,i))}}function Fd(e){var t=e.alternate;return e===Ie||t!==null&&t===Ie}function Wd(e,t){Do=Xl=!0;var r=e.pending;r===null?t.next=t:(t.next=r.next,r.next=t),e.pending=t}function Vd(e,t,r){if((r&4194240)!==0){var i=t.lanes;i&=e.pendingLanes,r|=i,t.lanes=r,ms(e,r)}}var Jl={readContext:Rt,useCallback:rt,useContext:rt,useEffect:rt,useImperativeHandle:rt,useInsertionEffect:rt,useLayoutEffect:rt,useMemo:rt,useReducer:rt,useRef:rt,useState:rt,useDebugValue:rt,useDeferredValue:rt,useTransition:rt,useMutableSource:rt,useSyncExternalStore:rt,useId:rt,unstable_isNewReconciler:!1},zv={readContext:Rt,useCallback:function(e,t){return Xt().memoizedState=[e,t===void 0?null:t],e},useContext:Rt,useEffect:jd,useImperativeHandle:function(e,t,r){return r=r!=null?r.concat([e]):null,Zl(4194308,4,Ld.bind(null,t,e),r)},useLayoutEffect:function(e,t){return Zl(4194308,4,e,t)},useInsertionEffect:function(e,t){return Zl(4,2,e,t)},useMemo:function(e,t){var r=Xt();return t=t===void 0?null:t,e=e(),r.memoizedState=[e,t],e},useReducer:function(e,t,r){var i=Xt();return t=r!==void 0?r(t):t,i.memoizedState=i.baseState=t,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:t},i.queue=e,e=e.dispatch=Tv.bind(null,Ie,e),[i.memoizedState,e]},useRef:function(e){var t=Xt();return e={current:e},t.memoizedState=e},useState:_d,useDebugValue:fa,useDeferredValue:function(e){return Xt().memoizedState=e},useTransition:function(){var e=_d(!1),t=e[0];return e=_v.bind(null,e[1]),Xt().memoizedState=e,[t,e]},useMutableSource:function(){},useSyncExternalStore:function(e,t,r){var i=Ie,u=Xt();if(Le){if(r===void 0)throw Error(l(407));r=r()}else{if(r=t(),Ye===null)throw Error(l(349));(er&30)!==0||bd(i,t,r)}u.memoizedState=r;var d={value:r,getSnapshot:t};return u.queue=d,jd(Pd.bind(null,i,d,e),[e]),i.flags|=2048,Wo(9,kd.bind(null,i,d,r,t),void 0,null),r},useId:function(){var e=Xt(),t=Ye.identifierPrefix;if(Le){var r=an,i=sn;r=(i&~(1<<32-It(i)-1)).toString(32)+r,t=":"+t+"R"+r,r=$o++,0<r&&(t+="H"+r.toString(32)),t+=":"}else r=Rv++,t=":"+t+"r"+r.toString(32)+":";return e.memoizedState=t},unstable_isNewReconciler:!1},Av={readContext:Rt,useCallback:Id,useContext:Rt,useEffect:da,useImperativeHandle:Md,useInsertionEffect:zd,useLayoutEffect:Ad,useMemo:Od,useReducer:ua,useRef:Td,useState:function(){return ua(Fo)},useDebugValue:fa,useDeferredValue:function(e){var t=_t();return Dd(t,Ke.memoizedState,e)},useTransition:function(){var e=ua(Fo)[0],t=_t().memoizedState;return[e,t]},useMutableSource:Cd,useSyncExternalStore:Ed,useId:$d,unstable_isNewReconciler:!1},Lv={readContext:Rt,useCallback:Id,useContext:Rt,useEffect:da,useImperativeHandle:Md,useInsertionEffect:zd,useLayoutEffect:Ad,useMemo:Od,useReducer:ca,useRef:Td,useState:function(){return ca(Fo)},useDebugValue:fa,useDeferredValue:function(e){var t=_t();return Ke===null?t.memoizedState=e:Dd(t,Ke.memoizedState,e)},useTransition:function(){var e=ca(Fo)[0],t=_t().memoizedState;return[e,t]},useMutableSource:Cd,useSyncExternalStore:Ed,useId:$d,unstable_isNewReconciler:!1};function $t(e,t){if(e&&e.defaultProps){t=G({},t),e=e.defaultProps;for(var r in e)t[r]===void 0&&(t[r]=e[r]);return t}return t}function pa(e,t,r,i){t=e.memoizedState,r=r(i,t),r=r==null?t:G({},t,r),e.memoizedState=r,e.lanes===0&&(e.updateQueue.baseState=r)}var ei={isMounted:function(e){return(e=e._reactInternals)?Qn(e)===e:!1},enqueueSetState:function(e,t,r){e=e._reactInternals;var i=ut(),u=zn(e),d=cn(i,u);d.payload=t,r!=null&&(d.callback=r),t=Rn(e,d,u),t!==null&&(Vt(t,e,u,i),Kl(t,e,u))},enqueueReplaceState:function(e,t,r){e=e._reactInternals;var i=ut(),u=zn(e),d=cn(i,u);d.tag=1,d.payload=t,r!=null&&(d.callback=r),t=Rn(e,d,u),t!==null&&(Vt(t,e,u,i),Kl(t,e,u))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var r=ut(),i=zn(e),u=cn(r,i);u.tag=2,t!=null&&(u.callback=t),t=Rn(e,u,i),t!==null&&(Vt(t,e,i,r),Kl(t,e,i))}};function Hd(e,t,r,i,u,d,v){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(i,d,v):t.prototype&&t.prototype.isPureReactComponent?!No(r,i)||!No(u,d):!0}function Bd(e,t,r){var i=!1,u=kn,d=t.contextType;return typeof d=="object"&&d!==null?d=Rt(d):(u=dt(t)?Yn:nt.current,i=t.contextTypes,d=(i=i!=null)?_r(e,u):kn),t=new t(r,d),e.memoizedState=t.state!==null&&t.state!==void 0?t.state:null,t.updater=ei,e.stateNode=t,t._reactInternals=e,i&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=u,e.__reactInternalMemoizedMaskedChildContext=d),t}function Ud(e,t,r,i){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(r,i),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(r,i),t.state!==e&&ei.enqueueReplaceState(t,t.state,null)}function ma(e,t,r,i){var u=e.stateNode;u.props=r,u.state=e.memoizedState,u.refs={},ea(e);var d=t.contextType;typeof d=="object"&&d!==null?u.context=Rt(d):(d=dt(t)?Yn:nt.current,u.context=_r(e,d)),u.state=e.memoizedState,d=t.getDerivedStateFromProps,typeof d=="function"&&(pa(e,t,d,r),u.state=e.memoizedState),typeof t.getDerivedStateFromProps=="function"||typeof u.getSnapshotBeforeUpdate=="function"||typeof u.UNSAFE_componentWillMount!="function"&&typeof u.componentWillMount!="function"||(t=u.state,typeof u.componentWillMount=="function"&&u.componentWillMount(),typeof u.UNSAFE_componentWillMount=="function"&&u.UNSAFE_componentWillMount(),t!==u.state&&ei.enqueueReplaceState(u,u.state,null),Ql(e,r,u,i),u.state=e.memoizedState),typeof u.componentDidMount=="function"&&(e.flags|=4194308)}function Or(e,t){try{var r="",i=t;do r+=ne(i),i=i.return;while(i);var u=r}catch(d){u=`
Error generating stack: `+d.message+`
`+d.stack}return{value:e,source:t,stack:u,digest:null}}function ha(e,t,r){return{value:e,source:null,stack:r??null,digest:t??null}}function ga(e,t){try{console.error(t.value)}catch(r){setTimeout(function(){throw r})}}var Mv=typeof WeakMap=="function"?WeakMap:Map;function Kd(e,t,r){r=cn(-1,r),r.tag=3,r.payload={element:null};var i=t.value;return r.callback=function(){si||(si=!0,ja=i),ga(e,t)},r}function Qd(e,t,r){r=cn(-1,r),r.tag=3;var i=e.type.getDerivedStateFromError;if(typeof i=="function"){var u=t.value;r.payload=function(){return i(u)},r.callback=function(){ga(e,t)}}var d=e.stateNode;return d!==null&&typeof d.componentDidCatch=="function"&&(r.callback=function(){ga(e,t),typeof i!="function"&&(Tn===null?Tn=new Set([this]):Tn.add(this));var v=t.stack;this.componentDidCatch(t.value,{componentStack:v!==null?v:""})}),r}function Gd(e,t,r){var i=e.pingCache;if(i===null){i=e.pingCache=new Mv;var u=new Set;i.set(t,u)}else u=i.get(t),u===void 0&&(u=new Set,i.set(t,u));u.has(r)||(u.add(r),e=Yv.bind(null,e,t,r),t.then(e,e))}function Yd(e){do{var t;if((t=e.tag===13)&&(t=e.memoizedState,t=t!==null?t.dehydrated!==null:!0),t)return e;e=e.return}while(e!==null);return null}function Xd(e,t,r,i,u){return(e.mode&1)===0?(e===t?e.flags|=65536:(e.flags|=128,r.flags|=131072,r.flags&=-52805,r.tag===1&&(r.alternate===null?r.tag=17:(t=cn(-1,1),t.tag=2,Rn(r,t,1))),r.lanes|=1),e):(e.flags|=65536,e.lanes=u,e)}var Iv=O.ReactCurrentOwner,ft=!1;function at(e,t,r,i){t.child=e===null?gd(t,null,r,i):Ar(t,e.child,r,i)}function Zd(e,t,r,i,u){r=r.render;var d=t.ref;return Mr(t,u),i=sa(e,t,r,i,d,u),r=aa(),e!==null&&!ft?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~u,dn(e,t,u)):(Le&&r&&Bs(t),t.flags|=1,at(e,t,i,u),t.child)}function qd(e,t,r,i,u){if(e===null){var d=r.type;return typeof d=="function"&&!Da(d)&&d.defaultProps===void 0&&r.compare===null&&r.defaultProps===void 0?(t.tag=15,t.type=d,Jd(e,t,d,i,u)):(e=pi(r.type,null,i,t,t.mode,u),e.ref=t.ref,e.return=t,t.child=e)}if(d=e.child,(e.lanes&u)===0){var v=d.memoizedProps;if(r=r.compare,r=r!==null?r:No,r(v,i)&&e.ref===t.ref)return dn(e,t,u)}return t.flags|=1,e=Ln(d,i),e.ref=t.ref,e.return=t,t.child=e}function Jd(e,t,r,i,u){if(e!==null){var d=e.memoizedProps;if(No(d,i)&&e.ref===t.ref)if(ft=!1,t.pendingProps=i=d,(e.lanes&u)!==0)(e.flags&131072)!==0&&(ft=!0);else return t.lanes=e.lanes,dn(e,t,u)}return va(e,t,r,i,u)}function ef(e,t,r){var i=t.pendingProps,u=i.children,d=e!==null?e.memoizedState:null;if(i.mode==="hidden")if((t.mode&1)===0)t.memoizedState={baseLanes:0,cachePool:null,transitions:null},Te($r,St),St|=r;else{if((r&1073741824)===0)return e=d!==null?d.baseLanes|r:r,t.lanes=t.childLanes=1073741824,t.memoizedState={baseLanes:e,cachePool:null,transitions:null},t.updateQueue=null,Te($r,St),St|=e,null;t.memoizedState={baseLanes:0,cachePool:null,transitions:null},i=d!==null?d.baseLanes:r,Te($r,St),St|=i}else d!==null?(i=d.baseLanes|r,t.memoizedState=null):i=r,Te($r,St),St|=i;return at(e,t,u,r),t.child}function tf(e,t){var r=t.ref;(e===null&&r!==null||e!==null&&e.ref!==r)&&(t.flags|=512,t.flags|=2097152)}function va(e,t,r,i,u){var d=dt(r)?Yn:nt.current;return d=_r(t,d),Mr(t,u),r=sa(e,t,r,i,d,u),i=aa(),e!==null&&!ft?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~u,dn(e,t,u)):(Le&&i&&Bs(t),t.flags|=1,at(e,t,r,u),t.child)}function nf(e,t,r,i,u){if(dt(r)){var d=!0;Dl(t)}else d=!1;if(Mr(t,u),t.stateNode===null)ni(e,t),Bd(t,r,i),ma(t,r,i,u),i=!0;else if(e===null){var v=t.stateNode,b=t.memoizedProps;v.props=b;var R=v.context,M=r.contextType;typeof M=="object"&&M!==null?M=Rt(M):(M=dt(r)?Yn:nt.current,M=_r(t,M));var K=r.getDerivedStateFromProps,Q=typeof K=="function"||typeof v.getSnapshotBeforeUpdate=="function";Q||typeof v.UNSAFE_componentWillReceiveProps!="function"&&typeof v.componentWillReceiveProps!="function"||(b!==i||R!==M)&&Ud(t,v,i,M),Nn=!1;var U=t.memoizedState;v.state=U,Ql(t,i,v,u),R=t.memoizedState,b!==i||U!==R||ct.current||Nn?(typeof K=="function"&&(pa(t,r,K,i),R=t.memoizedState),(b=Nn||Hd(t,r,b,i,U,R,M))?(Q||typeof v.UNSAFE_componentWillMount!="function"&&typeof v.componentWillMount!="function"||(typeof v.componentWillMount=="function"&&v.componentWillMount(),typeof v.UNSAFE_componentWillMount=="function"&&v.UNSAFE_componentWillMount()),typeof v.componentDidMount=="function"&&(t.flags|=4194308)):(typeof v.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=i,t.memoizedState=R),v.props=i,v.state=R,v.context=M,i=b):(typeof v.componentDidMount=="function"&&(t.flags|=4194308),i=!1)}else{v=t.stateNode,yd(e,t),b=t.memoizedProps,M=t.type===t.elementType?b:$t(t.type,b),v.props=M,Q=t.pendingProps,U=v.context,R=r.contextType,typeof R=="object"&&R!==null?R=Rt(R):(R=dt(r)?Yn:nt.current,R=_r(t,R));var ie=r.getDerivedStateFromProps;(K=typeof ie=="function"||typeof v.getSnapshotBeforeUpdate=="function")||typeof v.UNSAFE_componentWillReceiveProps!="function"&&typeof v.componentWillReceiveProps!="function"||(b!==Q||U!==R)&&Ud(t,v,i,R),Nn=!1,U=t.memoizedState,v.state=U,Ql(t,i,v,u);var ce=t.memoizedState;b!==Q||U!==ce||ct.current||Nn?(typeof ie=="function"&&(pa(t,r,ie,i),ce=t.memoizedState),(M=Nn||Hd(t,r,M,i,U,ce,R)||!1)?(K||typeof v.UNSAFE_componentWillUpdate!="function"&&typeof v.componentWillUpdate!="function"||(typeof v.componentWillUpdate=="function"&&v.componentWillUpdate(i,ce,R),typeof v.UNSAFE_componentWillUpdate=="function"&&v.UNSAFE_componentWillUpdate(i,ce,R)),typeof v.componentDidUpdate=="function"&&(t.flags|=4),typeof v.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof v.componentDidUpdate!="function"||b===e.memoizedProps&&U===e.memoizedState||(t.flags|=4),typeof v.getSnapshotBeforeUpdate!="function"||b===e.memoizedProps&&U===e.memoizedState||(t.flags|=1024),t.memoizedProps=i,t.memoizedState=ce),v.props=i,v.state=ce,v.context=R,i=M):(typeof v.componentDidUpdate!="function"||b===e.memoizedProps&&U===e.memoizedState||(t.flags|=4),typeof v.getSnapshotBeforeUpdate!="function"||b===e.memoizedProps&&U===e.memoizedState||(t.flags|=1024),i=!1)}return ya(e,t,r,i,d,u)}function ya(e,t,r,i,u,d){tf(e,t);var v=(t.flags&128)!==0;if(!i&&!v)return u&&sd(t,r,!1),dn(e,t,d);i=t.stateNode,Iv.current=t;var b=v&&typeof r.getDerivedStateFromError!="function"?null:i.render();return t.flags|=1,e!==null&&v?(t.child=Ar(t,e.child,null,d),t.child=Ar(t,null,b,d)):at(e,t,b,d),t.memoizedState=i.state,u&&sd(t,r,!0),t.child}function rf(e){var t=e.stateNode;t.pendingContext?ld(e,t.pendingContext,t.pendingContext!==t.context):t.context&&ld(e,t.context,!1),ta(e,t.containerInfo)}function of(e,t,r,i,u){return zr(),Gs(u),t.flags|=256,at(e,t,r,i),t.child}var xa={dehydrated:null,treeContext:null,retryLane:0};function wa(e){return{baseLanes:e,cachePool:null,transitions:null}}function lf(e,t,r){var i=t.pendingProps,u=Me.current,d=!1,v=(t.flags&128)!==0,b;if((b=v)||(b=e!==null&&e.memoizedState===null?!1:(u&2)!==0),b?(d=!0,t.flags&=-129):(e===null||e.memoizedState!==null)&&(u|=1),Te(Me,u&1),e===null)return Qs(t),e=t.memoizedState,e!==null&&(e=e.dehydrated,e!==null)?((t.mode&1)===0?t.lanes=1:e.data==="$!"?t.lanes=8:t.lanes=1073741824,null):(v=i.children,e=i.fallback,d?(i=t.mode,d=t.child,v={mode:"hidden",children:v},(i&1)===0&&d!==null?(d.childLanes=0,d.pendingProps=v):d=mi(v,i,0,null),e=lr(e,i,r,null),d.return=t,e.return=t,d.sibling=e,t.child=d,t.child.memoizedState=wa(r),t.memoizedState=xa,e):Sa(t,v));if(u=e.memoizedState,u!==null&&(b=u.dehydrated,b!==null))return Ov(e,t,v,i,b,u,r);if(d){d=i.fallback,v=t.mode,u=e.child,b=u.sibling;var R={mode:"hidden",children:i.children};return(v&1)===0&&t.child!==u?(i=t.child,i.childLanes=0,i.pendingProps=R,t.deletions=null):(i=Ln(u,R),i.subtreeFlags=u.subtreeFlags&14680064),b!==null?d=Ln(b,d):(d=lr(d,v,r,null),d.flags|=2),d.return=t,i.return=t,i.sibling=d,t.child=i,i=d,d=t.child,v=e.child.memoizedState,v=v===null?wa(r):{baseLanes:v.baseLanes|r,cachePool:null,transitions:v.transitions},d.memoizedState=v,d.childLanes=e.childLanes&~r,t.memoizedState=xa,i}return d=e.child,e=d.sibling,i=Ln(d,{mode:"visible",children:i.children}),(t.mode&1)===0&&(i.lanes=r),i.return=t,i.sibling=null,e!==null&&(r=t.deletions,r===null?(t.deletions=[e],t.flags|=16):r.push(e)),t.child=i,t.memoizedState=null,i}function Sa(e,t){return t=mi({mode:"visible",children:t},e.mode,0,null),t.return=e,e.child=t}function ti(e,t,r,i){return i!==null&&Gs(i),Ar(t,e.child,null,r),e=Sa(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function Ov(e,t,r,i,u,d,v){if(r)return t.flags&256?(t.flags&=-257,i=ha(Error(l(422))),ti(e,t,v,i)):t.memoizedState!==null?(t.child=e.child,t.flags|=128,null):(d=i.fallback,u=t.mode,i=mi({mode:"visible",children:i.children},u,0,null),d=lr(d,u,v,null),d.flags|=2,i.return=t,d.return=t,i.sibling=d,t.child=i,(t.mode&1)!==0&&Ar(t,e.child,null,v),t.child.memoizedState=wa(v),t.memoizedState=xa,d);if((t.mode&1)===0)return ti(e,t,v,null);if(u.data==="$!"){if(i=u.nextSibling&&u.nextSibling.dataset,i)var b=i.dgst;return i=b,d=Error(l(419)),i=ha(d,i,void 0),ti(e,t,v,i)}if(b=(v&e.childLanes)!==0,ft||b){if(i=Ye,i!==null){switch(v&-v){case 4:u=2;break;case 16:u=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:u=32;break;case 536870912:u=268435456;break;default:u=0}u=(u&(i.suspendedLanes|v))!==0?0:u,u!==0&&u!==d.retryLane&&(d.retryLane=u,un(e,u),Vt(i,e,u,-1))}return Oa(),i=ha(Error(l(421))),ti(e,t,v,i)}return u.data==="$?"?(t.flags|=128,t.child=e.child,t=Xv.bind(null,e),u._reactRetry=t,null):(e=d.treeContext,wt=En(u.nextSibling),xt=t,Le=!0,Dt=null,e!==null&&(Pt[Nt++]=sn,Pt[Nt++]=an,Pt[Nt++]=Xn,sn=e.id,an=e.overflow,Xn=t),t=Sa(t,i.children),t.flags|=4096,t)}function sf(e,t,r){e.lanes|=t;var i=e.alternate;i!==null&&(i.lanes|=t),qs(e.return,t,r)}function Ca(e,t,r,i,u){var d=e.memoizedState;d===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:i,tail:r,tailMode:u}:(d.isBackwards=t,d.rendering=null,d.renderingStartTime=0,d.last=i,d.tail=r,d.tailMode=u)}function af(e,t,r){var i=t.pendingProps,u=i.revealOrder,d=i.tail;if(at(e,t,i.children,r),i=Me.current,(i&2)!==0)i=i&1|2,t.flags|=128;else{if(e!==null&&(e.flags&128)!==0)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&sf(e,r,t);else if(e.tag===19)sf(e,r,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}i&=1}if(Te(Me,i),(t.mode&1)===0)t.memoizedState=null;else switch(u){case"forwards":for(r=t.child,u=null;r!==null;)e=r.alternate,e!==null&&Gl(e)===null&&(u=r),r=r.sibling;r=u,r===null?(u=t.child,t.child=null):(u=r.sibling,r.sibling=null),Ca(t,!1,u,r,d);break;case"backwards":for(r=null,u=t.child,t.child=null;u!==null;){if(e=u.alternate,e!==null&&Gl(e)===null){t.child=u;break}e=u.sibling,u.sibling=r,r=u,u=e}Ca(t,!0,r,null,d);break;case"together":Ca(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function ni(e,t){(t.mode&1)===0&&e!==null&&(e.alternate=null,t.alternate=null,t.flags|=2)}function dn(e,t,r){if(e!==null&&(t.dependencies=e.dependencies),tr|=t.lanes,(r&t.childLanes)===0)return null;if(e!==null&&t.child!==e.child)throw Error(l(153));if(t.child!==null){for(e=t.child,r=Ln(e,e.pendingProps),t.child=r,r.return=t;e.sibling!==null;)e=e.sibling,r=r.sibling=Ln(e,e.pendingProps),r.return=t;r.sibling=null}return t.child}function Dv(e,t,r){switch(t.tag){case 3:rf(t),zr();break;case 5:Sd(t);break;case 1:dt(t.type)&&Dl(t);break;case 4:ta(t,t.stateNode.containerInfo);break;case 10:var i=t.type._context,u=t.memoizedProps.value;Te(Bl,i._currentValue),i._currentValue=u;break;case 13:if(i=t.memoizedState,i!==null)return i.dehydrated!==null?(Te(Me,Me.current&1),t.flags|=128,null):(r&t.child.childLanes)!==0?lf(e,t,r):(Te(Me,Me.current&1),e=dn(e,t,r),e!==null?e.sibling:null);Te(Me,Me.current&1);break;case 19:if(i=(r&t.childLanes)!==0,(e.flags&128)!==0){if(i)return af(e,t,r);t.flags|=128}if(u=t.memoizedState,u!==null&&(u.rendering=null,u.tail=null,u.lastEffect=null),Te(Me,Me.current),i)break;return null;case 22:case 23:return t.lanes=0,ef(e,t,r)}return dn(e,t,r)}var uf,Ea,cf,df;uf=function(e,t){for(var r=t.child;r!==null;){if(r.tag===5||r.tag===6)e.appendChild(r.stateNode);else if(r.tag!==4&&r.child!==null){r.child.return=r,r=r.child;continue}if(r===t)break;for(;r.sibling===null;){if(r.return===null||r.return===t)return;r=r.return}r.sibling.return=r.return,r=r.sibling}},Ea=function(){},cf=function(e,t,r,i){var u=e.memoizedProps;if(u!==i){e=t.stateNode,Jn(Yt.current);var d=null;switch(r){case"input":u=Pe(e,u),i=Pe(e,i),d=[];break;case"select":u=G({},u,{value:void 0}),i=G({},i,{value:void 0}),d=[];break;case"textarea":u=vr(e,u),i=vr(e,i),d=[];break;default:typeof u.onClick!="function"&&typeof i.onClick=="function"&&(e.onclick=Ml)}rs(r,i);var v;r=null;for(M in u)if(!i.hasOwnProperty(M)&&u.hasOwnProperty(M)&&u[M]!=null)if(M==="style"){var b=u[M];for(v in b)b.hasOwnProperty(v)&&(r||(r={}),r[v]="")}else M!=="dangerouslySetInnerHTML"&&M!=="children"&&M!=="suppressContentEditableWarning"&&M!=="suppressHydrationWarning"&&M!=="autoFocus"&&(a.hasOwnProperty(M)?d||(d=[]):(d=d||[]).push(M,null));for(M in i){var R=i[M];if(b=u!=null?u[M]:void 0,i.hasOwnProperty(M)&&R!==b&&(R!=null||b!=null))if(M==="style")if(b){for(v in b)!b.hasOwnProperty(v)||R&&R.hasOwnProperty(v)||(r||(r={}),r[v]="");for(v in R)R.hasOwnProperty(v)&&b[v]!==R[v]&&(r||(r={}),r[v]=R[v])}else r||(d||(d=[]),d.push(M,r)),r=R;else M==="dangerouslySetInnerHTML"?(R=R?R.__html:void 0,b=b?b.__html:void 0,R!=null&&b!==R&&(d=d||[]).push(M,R)):M==="children"?typeof R!="string"&&typeof R!="number"||(d=d||[]).push(M,""+R):M!=="suppressContentEditableWarning"&&M!=="suppressHydrationWarning"&&(a.hasOwnProperty(M)?(R!=null&&M==="onScroll"&&je("scroll",e),d||b===R||(d=[])):(d=d||[]).push(M,R))}r&&(d=d||[]).push("style",r);var M=d;(t.updateQueue=M)&&(t.flags|=4)}},df=function(e,t,r,i){r!==i&&(t.flags|=4)};function Vo(e,t){if(!Le)switch(e.tailMode){case"hidden":t=e.tail;for(var r=null;t!==null;)t.alternate!==null&&(r=t),t=t.sibling;r===null?e.tail=null:r.sibling=null;break;case"collapsed":r=e.tail;for(var i=null;r!==null;)r.alternate!==null&&(i=r),r=r.sibling;i===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:i.sibling=null}}function ot(e){var t=e.alternate!==null&&e.alternate.child===e.child,r=0,i=0;if(t)for(var u=e.child;u!==null;)r|=u.lanes|u.childLanes,i|=u.subtreeFlags&14680064,i|=u.flags&14680064,u.return=e,u=u.sibling;else for(u=e.child;u!==null;)r|=u.lanes|u.childLanes,i|=u.subtreeFlags,i|=u.flags,u.return=e,u=u.sibling;return e.subtreeFlags|=i,e.childLanes=r,t}function $v(e,t,r){var i=t.pendingProps;switch(Us(t),t.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return ot(t),null;case 1:return dt(t.type)&&Ol(),ot(t),null;case 3:return i=t.stateNode,Ir(),ze(ct),ze(nt),oa(),i.pendingContext&&(i.context=i.pendingContext,i.pendingContext=null),(e===null||e.child===null)&&(Vl(t)?t.flags|=4:e===null||e.memoizedState.isDehydrated&&(t.flags&256)===0||(t.flags|=1024,Dt!==null&&(La(Dt),Dt=null))),Ea(e,t),ot(t),null;case 5:na(t);var u=Jn(Oo.current);if(r=t.type,e!==null&&t.stateNode!=null)cf(e,t,r,i,u),e.ref!==t.ref&&(t.flags|=512,t.flags|=2097152);else{if(!i){if(t.stateNode===null)throw Error(l(166));return ot(t),null}if(e=Jn(Yt.current),Vl(t)){i=t.stateNode,r=t.type;var d=t.memoizedProps;switch(i[Gt]=t,i[zo]=d,e=(t.mode&1)!==0,r){case"dialog":je("cancel",i),je("close",i);break;case"iframe":case"object":case"embed":je("load",i);break;case"video":case"audio":for(u=0;u<_o.length;u++)je(_o[u],i);break;case"source":je("error",i);break;case"img":case"image":case"link":je("error",i),je("load",i);break;case"details":je("toggle",i);break;case"input":tt(i,d),je("invalid",i);break;case"select":i._wrapperState={wasMultiple:!!d.multiple},je("invalid",i);break;case"textarea":fl(i,d),je("invalid",i)}rs(r,d),u=null;for(var v in d)if(d.hasOwnProperty(v)){var b=d[v];v==="children"?typeof b=="string"?i.textContent!==b&&(d.suppressHydrationWarning!==!0&&Ll(i.textContent,b,e),u=["children",b]):typeof b=="number"&&i.textContent!==""+b&&(d.suppressHydrationWarning!==!0&&Ll(i.textContent,b,e),u=["children",""+b]):a.hasOwnProperty(v)&&b!=null&&v==="onScroll"&&je("scroll",i)}switch(r){case"input":fe(i),Ue(i,d,!0);break;case"textarea":fe(i),Yu(i);break;case"select":case"option":break;default:typeof d.onClick=="function"&&(i.onclick=Ml)}i=u,t.updateQueue=i,i!==null&&(t.flags|=4)}else{v=u.nodeType===9?u:u.ownerDocument,e==="http://www.w3.org/1999/xhtml"&&(e=Xu(r)),e==="http://www.w3.org/1999/xhtml"?r==="script"?(e=v.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild)):typeof i.is=="string"?e=v.createElement(r,{is:i.is}):(e=v.createElement(r),r==="select"&&(v=e,i.multiple?v.multiple=!0:i.size&&(v.size=i.size))):e=v.createElementNS(e,r),e[Gt]=t,e[zo]=i,uf(e,t,!1,!1),t.stateNode=e;e:{switch(v=os(r,i),r){case"dialog":je("cancel",e),je("close",e),u=i;break;case"iframe":case"object":case"embed":je("load",e),u=i;break;case"video":case"audio":for(u=0;u<_o.length;u++)je(_o[u],e);u=i;break;case"source":je("error",e),u=i;break;case"img":case"image":case"link":je("error",e),je("load",e),u=i;break;case"details":je("toggle",e),u=i;break;case"input":tt(e,i),u=Pe(e,i),je("invalid",e);break;case"option":u=i;break;case"select":e._wrapperState={wasMultiple:!!i.multiple},u=G({},i,{value:void 0}),je("invalid",e);break;case"textarea":fl(e,i),u=vr(e,i),je("invalid",e);break;default:u=i}rs(r,u),b=u;for(d in b)if(b.hasOwnProperty(d)){var R=b[d];d==="style"?Ju(e,R):d==="dangerouslySetInnerHTML"?(R=R?R.__html:void 0,R!=null&&Zu(e,R)):d==="children"?typeof R=="string"?(r!=="textarea"||R!=="")&&uo(e,R):typeof R=="number"&&uo(e,""+R):d!=="suppressContentEditableWarning"&&d!=="suppressHydrationWarning"&&d!=="autoFocus"&&(a.hasOwnProperty(d)?R!=null&&d==="onScroll"&&je("scroll",e):R!=null&&A(e,d,R,v))}switch(r){case"input":fe(e),Ue(e,i,!1);break;case"textarea":fe(e),Yu(e);break;case"option":i.value!=null&&e.setAttribute("value",""+H(i.value));break;case"select":e.multiple=!!i.multiple,d=i.value,d!=null?kt(e,!!i.multiple,d,!1):i.defaultValue!=null&&kt(e,!!i.multiple,i.defaultValue,!0);break;default:typeof u.onClick=="function"&&(e.onclick=Ml)}switch(r){case"button":case"input":case"select":case"textarea":i=!!i.autoFocus;break e;case"img":i=!0;break e;default:i=!1}}i&&(t.flags|=4)}t.ref!==null&&(t.flags|=512,t.flags|=2097152)}return ot(t),null;case 6:if(e&&t.stateNode!=null)df(e,t,e.memoizedProps,i);else{if(typeof i!="string"&&t.stateNode===null)throw Error(l(166));if(r=Jn(Oo.current),Jn(Yt.current),Vl(t)){if(i=t.stateNode,r=t.memoizedProps,i[Gt]=t,(d=i.nodeValue!==r)&&(e=xt,e!==null))switch(e.tag){case 3:Ll(i.nodeValue,r,(e.mode&1)!==0);break;case 5:e.memoizedProps.suppressHydrationWarning!==!0&&Ll(i.nodeValue,r,(e.mode&1)!==0)}d&&(t.flags|=4)}else i=(r.nodeType===9?r:r.ownerDocument).createTextNode(i),i[Gt]=t,t.stateNode=i}return ot(t),null;case 13:if(ze(Me),i=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(Le&&wt!==null&&(t.mode&1)!==0&&(t.flags&128)===0)pd(),zr(),t.flags|=98560,d=!1;else if(d=Vl(t),i!==null&&i.dehydrated!==null){if(e===null){if(!d)throw Error(l(318));if(d=t.memoizedState,d=d!==null?d.dehydrated:null,!d)throw Error(l(317));d[Gt]=t}else zr(),(t.flags&128)===0&&(t.memoizedState=null),t.flags|=4;ot(t),d=!1}else Dt!==null&&(La(Dt),Dt=null),d=!0;if(!d)return t.flags&65536?t:null}return(t.flags&128)!==0?(t.lanes=r,t):(i=i!==null,i!==(e!==null&&e.memoizedState!==null)&&i&&(t.child.flags|=8192,(t.mode&1)!==0&&(e===null||(Me.current&1)!==0?Qe===0&&(Qe=3):Oa())),t.updateQueue!==null&&(t.flags|=4),ot(t),null);case 4:return Ir(),Ea(e,t),e===null&&To(t.stateNode.containerInfo),ot(t),null;case 10:return Zs(t.type._context),ot(t),null;case 17:return dt(t.type)&&Ol(),ot(t),null;case 19:if(ze(Me),d=t.memoizedState,d===null)return ot(t),null;if(i=(t.flags&128)!==0,v=d.rendering,v===null)if(i)Vo(d,!1);else{if(Qe!==0||e!==null&&(e.flags&128)!==0)for(e=t.child;e!==null;){if(v=Gl(e),v!==null){for(t.flags|=128,Vo(d,!1),i=v.updateQueue,i!==null&&(t.updateQueue=i,t.flags|=4),t.subtreeFlags=0,i=r,r=t.child;r!==null;)d=r,e=i,d.flags&=14680066,v=d.alternate,v===null?(d.childLanes=0,d.lanes=e,d.child=null,d.subtreeFlags=0,d.memoizedProps=null,d.memoizedState=null,d.updateQueue=null,d.dependencies=null,d.stateNode=null):(d.childLanes=v.childLanes,d.lanes=v.lanes,d.child=v.child,d.subtreeFlags=0,d.deletions=null,d.memoizedProps=v.memoizedProps,d.memoizedState=v.memoizedState,d.updateQueue=v.updateQueue,d.type=v.type,e=v.dependencies,d.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),r=r.sibling;return Te(Me,Me.current&1|2),t.child}e=e.sibling}d.tail!==null&&$e()>Fr&&(t.flags|=128,i=!0,Vo(d,!1),t.lanes=4194304)}else{if(!i)if(e=Gl(v),e!==null){if(t.flags|=128,i=!0,r=e.updateQueue,r!==null&&(t.updateQueue=r,t.flags|=4),Vo(d,!0),d.tail===null&&d.tailMode==="hidden"&&!v.alternate&&!Le)return ot(t),null}else 2*$e()-d.renderingStartTime>Fr&&r!==1073741824&&(t.flags|=128,i=!0,Vo(d,!1),t.lanes=4194304);d.isBackwards?(v.sibling=t.child,t.child=v):(r=d.last,r!==null?r.sibling=v:t.child=v,d.last=v)}return d.tail!==null?(t=d.tail,d.rendering=t,d.tail=t.sibling,d.renderingStartTime=$e(),t.sibling=null,r=Me.current,Te(Me,i?r&1|2:r&1),t):(ot(t),null);case 22:case 23:return Ia(),i=t.memoizedState!==null,e!==null&&e.memoizedState!==null!==i&&(t.flags|=8192),i&&(t.mode&1)!==0?(St&1073741824)!==0&&(ot(t),t.subtreeFlags&6&&(t.flags|=8192)):ot(t),null;case 24:return null;case 25:return null}throw Error(l(156,t.tag))}function Fv(e,t){switch(Us(t),t.tag){case 1:return dt(t.type)&&Ol(),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return Ir(),ze(ct),ze(nt),oa(),e=t.flags,(e&65536)!==0&&(e&128)===0?(t.flags=e&-65537|128,t):null;case 5:return na(t),null;case 13:if(ze(Me),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(l(340));zr()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return ze(Me),null;case 4:return Ir(),null;case 10:return Zs(t.type._context),null;case 22:case 23:return Ia(),null;case 24:return null;default:return null}}var ri=!1,lt=!1,Wv=typeof WeakSet=="function"?WeakSet:Set,ue=null;function Dr(e,t){var r=e.ref;if(r!==null)if(typeof r=="function")try{r(null)}catch(i){Oe(e,t,i)}else r.current=null}function ba(e,t,r){try{r()}catch(i){Oe(e,t,i)}}var ff=!1;function Vv(e,t){if(Is=El,e=Hc(),Rs(e)){if("selectionStart"in e)var r={start:e.selectionStart,end:e.selectionEnd};else e:{r=(r=e.ownerDocument)&&r.defaultView||window;var i=r.getSelection&&r.getSelection();if(i&&i.rangeCount!==0){r=i.anchorNode;var u=i.anchorOffset,d=i.focusNode;i=i.focusOffset;try{r.nodeType,d.nodeType}catch{r=null;break e}var v=0,b=-1,R=-1,M=0,K=0,Q=e,U=null;t:for(;;){for(var ie;Q!==r||u!==0&&Q.nodeType!==3||(b=v+u),Q!==d||i!==0&&Q.nodeType!==3||(R=v+i),Q.nodeType===3&&(v+=Q.nodeValue.length),(ie=Q.firstChild)!==null;)U=Q,Q=ie;for(;;){if(Q===e)break t;if(U===r&&++M===u&&(b=v),U===d&&++K===i&&(R=v),(ie=Q.nextSibling)!==null)break;Q=U,U=Q.parentNode}Q=ie}r=b===-1||R===-1?null:{start:b,end:R}}else r=null}r=r||{start:0,end:0}}else r=null;for(Os={focusedElem:e,selectionRange:r},El=!1,ue=t;ue!==null;)if(t=ue,e=t.child,(t.subtreeFlags&1028)!==0&&e!==null)e.return=t,ue=e;else for(;ue!==null;){t=ue;try{var ce=t.alternate;if((t.flags&1024)!==0)switch(t.tag){case 0:case 11:case 15:break;case 1:if(ce!==null){var pe=ce.memoizedProps,Fe=ce.memoizedState,z=t.stateNode,T=z.getSnapshotBeforeUpdate(t.elementType===t.type?pe:$t(t.type,pe),Fe);z.__reactInternalSnapshotBeforeUpdate=T}break;case 3:var L=t.stateNode.containerInfo;L.nodeType===1?L.textContent="":L.nodeType===9&&L.documentElement&&L.removeChild(L.documentElement);break;case 5:case 6:case 4:case 17:break;default:throw Error(l(163))}}catch(X){Oe(t,t.return,X)}if(e=t.sibling,e!==null){e.return=t.return,ue=e;break}ue=t.return}return ce=ff,ff=!1,ce}function Ho(e,t,r){var i=t.updateQueue;if(i=i!==null?i.lastEffect:null,i!==null){var u=i=i.next;do{if((u.tag&e)===e){var d=u.destroy;u.destroy=void 0,d!==void 0&&ba(t,r,d)}u=u.next}while(u!==i)}}function oi(e,t){if(t=t.updateQueue,t=t!==null?t.lastEffect:null,t!==null){var r=t=t.next;do{if((r.tag&e)===e){var i=r.create;r.destroy=i()}r=r.next}while(r!==t)}}function ka(e){var t=e.ref;if(t!==null){var r=e.stateNode;switch(e.tag){case 5:e=r;break;default:e=r}typeof t=="function"?t(e):t.current=e}}function pf(e){var t=e.alternate;t!==null&&(e.alternate=null,pf(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&(delete t[Gt],delete t[zo],delete t[Ws],delete t[bv],delete t[kv])),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}function mf(e){return e.tag===5||e.tag===3||e.tag===4}function hf(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||mf(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function Pa(e,t,r){var i=e.tag;if(i===5||i===6)e=e.stateNode,t?r.nodeType===8?r.parentNode.insertBefore(e,t):r.insertBefore(e,t):(r.nodeType===8?(t=r.parentNode,t.insertBefore(e,r)):(t=r,t.appendChild(e)),r=r._reactRootContainer,r!=null||t.onclick!==null||(t.onclick=Ml));else if(i!==4&&(e=e.child,e!==null))for(Pa(e,t,r),e=e.sibling;e!==null;)Pa(e,t,r),e=e.sibling}function Na(e,t,r){var i=e.tag;if(i===5||i===6)e=e.stateNode,t?r.insertBefore(e,t):r.appendChild(e);else if(i!==4&&(e=e.child,e!==null))for(Na(e,t,r),e=e.sibling;e!==null;)Na(e,t,r),e=e.sibling}var qe=null,Ft=!1;function _n(e,t,r){for(r=r.child;r!==null;)gf(e,t,r),r=r.sibling}function gf(e,t,r){if(Qt&&typeof Qt.onCommitFiberUnmount=="function")try{Qt.onCommitFiberUnmount(vl,r)}catch{}switch(r.tag){case 5:lt||Dr(r,t);case 6:var i=qe,u=Ft;qe=null,_n(e,t,r),qe=i,Ft=u,qe!==null&&(Ft?(e=qe,r=r.stateNode,e.nodeType===8?e.parentNode.removeChild(r):e.removeChild(r)):qe.removeChild(r.stateNode));break;case 18:qe!==null&&(Ft?(e=qe,r=r.stateNode,e.nodeType===8?Fs(e.parentNode,r):e.nodeType===1&&Fs(e,r),So(e)):Fs(qe,r.stateNode));break;case 4:i=qe,u=Ft,qe=r.stateNode.containerInfo,Ft=!0,_n(e,t,r),qe=i,Ft=u;break;case 0:case 11:case 14:case 15:if(!lt&&(i=r.updateQueue,i!==null&&(i=i.lastEffect,i!==null))){u=i=i.next;do{var d=u,v=d.destroy;d=d.tag,v!==void 0&&((d&2)!==0||(d&4)!==0)&&ba(r,t,v),u=u.next}while(u!==i)}_n(e,t,r);break;case 1:if(!lt&&(Dr(r,t),i=r.stateNode,typeof i.componentWillUnmount=="function"))try{i.props=r.memoizedProps,i.state=r.memoizedState,i.componentWillUnmount()}catch(b){Oe(r,t,b)}_n(e,t,r);break;case 21:_n(e,t,r);break;case 22:r.mode&1?(lt=(i=lt)||r.memoizedState!==null,_n(e,t,r),lt=i):_n(e,t,r);break;default:_n(e,t,r)}}function vf(e){var t=e.updateQueue;if(t!==null){e.updateQueue=null;var r=e.stateNode;r===null&&(r=e.stateNode=new Wv),t.forEach(function(i){var u=Zv.bind(null,e,i);r.has(i)||(r.add(i),i.then(u,u))})}}function Wt(e,t){var r=t.deletions;if(r!==null)for(var i=0;i<r.length;i++){var u=r[i];try{var d=e,v=t,b=v;e:for(;b!==null;){switch(b.tag){case 5:qe=b.stateNode,Ft=!1;break e;case 3:qe=b.stateNode.containerInfo,Ft=!0;break e;case 4:qe=b.stateNode.containerInfo,Ft=!0;break e}b=b.return}if(qe===null)throw Error(l(160));gf(d,v,u),qe=null,Ft=!1;var R=u.alternate;R!==null&&(R.return=null),u.return=null}catch(M){Oe(u,t,M)}}if(t.subtreeFlags&12854)for(t=t.child;t!==null;)yf(t,e),t=t.sibling}function yf(e,t){var r=e.alternate,i=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:if(Wt(t,e),Zt(e),i&4){try{Ho(3,e,e.return),oi(3,e)}catch(pe){Oe(e,e.return,pe)}try{Ho(5,e,e.return)}catch(pe){Oe(e,e.return,pe)}}break;case 1:Wt(t,e),Zt(e),i&512&&r!==null&&Dr(r,r.return);break;case 5:if(Wt(t,e),Zt(e),i&512&&r!==null&&Dr(r,r.return),e.flags&32){var u=e.stateNode;try{uo(u,"")}catch(pe){Oe(e,e.return,pe)}}if(i&4&&(u=e.stateNode,u!=null)){var d=e.memoizedProps,v=r!==null?r.memoizedProps:d,b=e.type,R=e.updateQueue;if(e.updateQueue=null,R!==null)try{b==="input"&&d.type==="radio"&&d.name!=null&&st(u,d),os(b,v);var M=os(b,d);for(v=0;v<R.length;v+=2){var K=R[v],Q=R[v+1];K==="style"?Ju(u,Q):K==="dangerouslySetInnerHTML"?Zu(u,Q):K==="children"?uo(u,Q):A(u,K,Q,M)}switch(b){case"input":Ze(u,d);break;case"textarea":Gu(u,d);break;case"select":var U=u._wrapperState.wasMultiple;u._wrapperState.wasMultiple=!!d.multiple;var ie=d.value;ie!=null?kt(u,!!d.multiple,ie,!1):U!==!!d.multiple&&(d.defaultValue!=null?kt(u,!!d.multiple,d.defaultValue,!0):kt(u,!!d.multiple,d.multiple?[]:"",!1))}u[zo]=d}catch(pe){Oe(e,e.return,pe)}}break;case 6:if(Wt(t,e),Zt(e),i&4){if(e.stateNode===null)throw Error(l(162));u=e.stateNode,d=e.memoizedProps;try{u.nodeValue=d}catch(pe){Oe(e,e.return,pe)}}break;case 3:if(Wt(t,e),Zt(e),i&4&&r!==null&&r.memoizedState.isDehydrated)try{So(t.containerInfo)}catch(pe){Oe(e,e.return,pe)}break;case 4:Wt(t,e),Zt(e);break;case 13:Wt(t,e),Zt(e),u=e.child,u.flags&8192&&(d=u.memoizedState!==null,u.stateNode.isHidden=d,!d||u.alternate!==null&&u.alternate.memoizedState!==null||(Ta=$e())),i&4&&vf(e);break;case 22:if(K=r!==null&&r.memoizedState!==null,e.mode&1?(lt=(M=lt)||K,Wt(t,e),lt=M):Wt(t,e),Zt(e),i&8192){if(M=e.memoizedState!==null,(e.stateNode.isHidden=M)&&!K&&(e.mode&1)!==0)for(ue=e,K=e.child;K!==null;){for(Q=ue=K;ue!==null;){switch(U=ue,ie=U.child,U.tag){case 0:case 11:case 14:case 15:Ho(4,U,U.return);break;case 1:Dr(U,U.return);var ce=U.stateNode;if(typeof ce.componentWillUnmount=="function"){i=U,r=U.return;try{t=i,ce.props=t.memoizedProps,ce.state=t.memoizedState,ce.componentWillUnmount()}catch(pe){Oe(i,r,pe)}}break;case 5:Dr(U,U.return);break;case 22:if(U.memoizedState!==null){Sf(Q);continue}}ie!==null?(ie.return=U,ue=ie):Sf(Q)}K=K.sibling}e:for(K=null,Q=e;;){if(Q.tag===5){if(K===null){K=Q;try{u=Q.stateNode,M?(d=u.style,typeof d.setProperty=="function"?d.setProperty("display","none","important"):d.display="none"):(b=Q.stateNode,R=Q.memoizedProps.style,v=R!=null&&R.hasOwnProperty("display")?R.display:null,b.style.display=qu("display",v))}catch(pe){Oe(e,e.return,pe)}}}else if(Q.tag===6){if(K===null)try{Q.stateNode.nodeValue=M?"":Q.memoizedProps}catch(pe){Oe(e,e.return,pe)}}else if((Q.tag!==22&&Q.tag!==23||Q.memoizedState===null||Q===e)&&Q.child!==null){Q.child.return=Q,Q=Q.child;continue}if(Q===e)break e;for(;Q.sibling===null;){if(Q.return===null||Q.return===e)break e;K===Q&&(K=null),Q=Q.return}K===Q&&(K=null),Q.sibling.return=Q.return,Q=Q.sibling}}break;case 19:Wt(t,e),Zt(e),i&4&&vf(e);break;case 21:break;default:Wt(t,e),Zt(e)}}function Zt(e){var t=e.flags;if(t&2){try{e:{for(var r=e.return;r!==null;){if(mf(r)){var i=r;break e}r=r.return}throw Error(l(160))}switch(i.tag){case 5:var u=i.stateNode;i.flags&32&&(uo(u,""),i.flags&=-33);var d=hf(e);Na(e,d,u);break;case 3:case 4:var v=i.stateNode.containerInfo,b=hf(e);Pa(e,b,v);break;default:throw Error(l(161))}}catch(R){Oe(e,e.return,R)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function Hv(e,t,r){ue=e,xf(e)}function xf(e,t,r){for(var i=(e.mode&1)!==0;ue!==null;){var u=ue,d=u.child;if(u.tag===22&&i){var v=u.memoizedState!==null||ri;if(!v){var b=u.alternate,R=b!==null&&b.memoizedState!==null||lt;b=ri;var M=lt;if(ri=v,(lt=R)&&!M)for(ue=u;ue!==null;)v=ue,R=v.child,v.tag===22&&v.memoizedState!==null?Cf(u):R!==null?(R.return=v,ue=R):Cf(u);for(;d!==null;)ue=d,xf(d),d=d.sibling;ue=u,ri=b,lt=M}wf(e)}else(u.subtreeFlags&8772)!==0&&d!==null?(d.return=u,ue=d):wf(e)}}function wf(e){for(;ue!==null;){var t=ue;if((t.flags&8772)!==0){var r=t.alternate;try{if((t.flags&8772)!==0)switch(t.tag){case 0:case 11:case 15:lt||oi(5,t);break;case 1:var i=t.stateNode;if(t.flags&4&&!lt)if(r===null)i.componentDidMount();else{var u=t.elementType===t.type?r.memoizedProps:$t(t.type,r.memoizedProps);i.componentDidUpdate(u,r.memoizedState,i.__reactInternalSnapshotBeforeUpdate)}var d=t.updateQueue;d!==null&&wd(t,d,i);break;case 3:var v=t.updateQueue;if(v!==null){if(r=null,t.child!==null)switch(t.child.tag){case 5:r=t.child.stateNode;break;case 1:r=t.child.stateNode}wd(t,v,r)}break;case 5:var b=t.stateNode;if(r===null&&t.flags&4){r=b;var R=t.memoizedProps;switch(t.type){case"button":case"input":case"select":case"textarea":R.autoFocus&&r.focus();break;case"img":R.src&&(r.src=R.src)}}break;case 6:break;case 4:break;case 12:break;case 13:if(t.memoizedState===null){var M=t.alternate;if(M!==null){var K=M.memoizedState;if(K!==null){var Q=K.dehydrated;Q!==null&&So(Q)}}}break;case 19:case 17:case 21:case 22:case 23:case 25:break;default:throw Error(l(163))}lt||t.flags&512&&ka(t)}catch(U){Oe(t,t.return,U)}}if(t===e){ue=null;break}if(r=t.sibling,r!==null){r.return=t.return,ue=r;break}ue=t.return}}function Sf(e){for(;ue!==null;){var t=ue;if(t===e){ue=null;break}var r=t.sibling;if(r!==null){r.return=t.return,ue=r;break}ue=t.return}}function Cf(e){for(;ue!==null;){var t=ue;try{switch(t.tag){case 0:case 11:case 15:var r=t.return;try{oi(4,t)}catch(R){Oe(t,r,R)}break;case 1:var i=t.stateNode;if(typeof i.componentDidMount=="function"){var u=t.return;try{i.componentDidMount()}catch(R){Oe(t,u,R)}}var d=t.return;try{ka(t)}catch(R){Oe(t,d,R)}break;case 5:var v=t.return;try{ka(t)}catch(R){Oe(t,v,R)}}}catch(R){Oe(t,t.return,R)}if(t===e){ue=null;break}var b=t.sibling;if(b!==null){b.return=t.return,ue=b;break}ue=t.return}}var Bv=Math.ceil,li=O.ReactCurrentDispatcher,Ra=O.ReactCurrentOwner,Tt=O.ReactCurrentBatchConfig,Ne=0,Ye=null,Ve=null,Je=0,St=0,$r=bn(0),Qe=0,Bo=null,tr=0,ii=0,_a=0,Uo=null,pt=null,Ta=0,Fr=1/0,fn=null,si=!1,ja=null,Tn=null,ai=!1,jn=null,ui=0,Ko=0,za=null,ci=-1,di=0;function ut(){return(Ne&6)!==0?$e():ci!==-1?ci:ci=$e()}function zn(e){return(e.mode&1)===0?1:(Ne&2)!==0&&Je!==0?Je&-Je:Nv.transition!==null?(di===0&&(di=mc()),di):(e=_e,e!==0||(e=window.event,e=e===void 0?16:Ec(e.type)),e)}function Vt(e,t,r,i){if(50<Ko)throw Ko=0,za=null,Error(l(185));go(e,r,i),((Ne&2)===0||e!==Ye)&&(e===Ye&&((Ne&2)===0&&(ii|=r),Qe===4&&An(e,Je)),mt(e,i),r===1&&Ne===0&&(t.mode&1)===0&&(Fr=$e()+500,$l&&Pn()))}function mt(e,t){var r=e.callbackNode;Ng(e,t);var i=wl(e,e===Ye?Je:0);if(i===0)r!==null&&dc(r),e.callbackNode=null,e.callbackPriority=0;else if(t=i&-i,e.callbackPriority!==t){if(r!=null&&dc(r),t===1)e.tag===0?Pv(bf.bind(null,e)):ad(bf.bind(null,e)),Cv(function(){(Ne&6)===0&&Pn()}),r=null;else{switch(hc(i)){case 1:r=ds;break;case 4:r=fc;break;case 16:r=gl;break;case 536870912:r=pc;break;default:r=gl}r=zf(r,Ef.bind(null,e))}e.callbackPriority=t,e.callbackNode=r}}function Ef(e,t){if(ci=-1,di=0,(Ne&6)!==0)throw Error(l(327));var r=e.callbackNode;if(Wr()&&e.callbackNode!==r)return null;var i=wl(e,e===Ye?Je:0);if(i===0)return null;if((i&30)!==0||(i&e.expiredLanes)!==0||t)t=fi(e,i);else{t=i;var u=Ne;Ne|=2;var d=Pf();(Ye!==e||Je!==t)&&(fn=null,Fr=$e()+500,rr(e,t));do try{Qv();break}catch(b){kf(e,b)}while(!0);Xs(),li.current=d,Ne=u,Ve!==null?t=0:(Ye=null,Je=0,t=Qe)}if(t!==0){if(t===2&&(u=fs(e),u!==0&&(i=u,t=Aa(e,u))),t===1)throw r=Bo,rr(e,0),An(e,i),mt(e,$e()),r;if(t===6)An(e,i);else{if(u=e.current.alternate,(i&30)===0&&!Uv(u)&&(t=fi(e,i),t===2&&(d=fs(e),d!==0&&(i=d,t=Aa(e,d))),t===1))throw r=Bo,rr(e,0),An(e,i),mt(e,$e()),r;switch(e.finishedWork=u,e.finishedLanes=i,t){case 0:case 1:throw Error(l(345));case 2:or(e,pt,fn);break;case 3:if(An(e,i),(i&130023424)===i&&(t=Ta+500-$e(),10<t)){if(wl(e,0)!==0)break;if(u=e.suspendedLanes,(u&i)!==i){ut(),e.pingedLanes|=e.suspendedLanes&u;break}e.timeoutHandle=$s(or.bind(null,e,pt,fn),t);break}or(e,pt,fn);break;case 4:if(An(e,i),(i&4194240)===i)break;for(t=e.eventTimes,u=-1;0<i;){var v=31-It(i);d=1<<v,v=t[v],v>u&&(u=v),i&=~d}if(i=u,i=$e()-i,i=(120>i?120:480>i?480:1080>i?1080:1920>i?1920:3e3>i?3e3:4320>i?4320:1960*Bv(i/1960))-i,10<i){e.timeoutHandle=$s(or.bind(null,e,pt,fn),i);break}or(e,pt,fn);break;case 5:or(e,pt,fn);break;default:throw Error(l(329))}}}return mt(e,$e()),e.callbackNode===r?Ef.bind(null,e):null}function Aa(e,t){var r=Uo;return e.current.memoizedState.isDehydrated&&(rr(e,t).flags|=256),e=fi(e,t),e!==2&&(t=pt,pt=r,t!==null&&La(t)),e}function La(e){pt===null?pt=e:pt.push.apply(pt,e)}function Uv(e){for(var t=e;;){if(t.flags&16384){var r=t.updateQueue;if(r!==null&&(r=r.stores,r!==null))for(var i=0;i<r.length;i++){var u=r[i],d=u.getSnapshot;u=u.value;try{if(!Ot(d(),u))return!1}catch{return!1}}}if(r=t.child,t.subtreeFlags&16384&&r!==null)r.return=t,t=r;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function An(e,t){for(t&=~_a,t&=~ii,e.suspendedLanes|=t,e.pingedLanes&=~t,e=e.expirationTimes;0<t;){var r=31-It(t),i=1<<r;e[r]=-1,t&=~i}}function bf(e){if((Ne&6)!==0)throw Error(l(327));Wr();var t=wl(e,0);if((t&1)===0)return mt(e,$e()),null;var r=fi(e,t);if(e.tag!==0&&r===2){var i=fs(e);i!==0&&(t=i,r=Aa(e,i))}if(r===1)throw r=Bo,rr(e,0),An(e,t),mt(e,$e()),r;if(r===6)throw Error(l(345));return e.finishedWork=e.current.alternate,e.finishedLanes=t,or(e,pt,fn),mt(e,$e()),null}function Ma(e,t){var r=Ne;Ne|=1;try{return e(t)}finally{Ne=r,Ne===0&&(Fr=$e()+500,$l&&Pn())}}function nr(e){jn!==null&&jn.tag===0&&(Ne&6)===0&&Wr();var t=Ne;Ne|=1;var r=Tt.transition,i=_e;try{if(Tt.transition=null,_e=1,e)return e()}finally{_e=i,Tt.transition=r,Ne=t,(Ne&6)===0&&Pn()}}function Ia(){St=$r.current,ze($r)}function rr(e,t){e.finishedWork=null,e.finishedLanes=0;var r=e.timeoutHandle;if(r!==-1&&(e.timeoutHandle=-1,Sv(r)),Ve!==null)for(r=Ve.return;r!==null;){var i=r;switch(Us(i),i.tag){case 1:i=i.type.childContextTypes,i!=null&&Ol();break;case 3:Ir(),ze(ct),ze(nt),oa();break;case 5:na(i);break;case 4:Ir();break;case 13:ze(Me);break;case 19:ze(Me);break;case 10:Zs(i.type._context);break;case 22:case 23:Ia()}r=r.return}if(Ye=e,Ve=e=Ln(e.current,null),Je=St=t,Qe=0,Bo=null,_a=ii=tr=0,pt=Uo=null,qn!==null){for(t=0;t<qn.length;t++)if(r=qn[t],i=r.interleaved,i!==null){r.interleaved=null;var u=i.next,d=r.pending;if(d!==null){var v=d.next;d.next=u,i.next=v}r.pending=i}qn=null}return e}function kf(e,t){do{var r=Ve;try{if(Xs(),Yl.current=Jl,Xl){for(var i=Ie.memoizedState;i!==null;){var u=i.queue;u!==null&&(u.pending=null),i=i.next}Xl=!1}if(er=0,Ge=Ke=Ie=null,Do=!1,$o=0,Ra.current=null,r===null||r.return===null){Qe=1,Bo=t,Ve=null;break}e:{var d=e,v=r.return,b=r,R=t;if(t=Je,b.flags|=32768,R!==null&&typeof R=="object"&&typeof R.then=="function"){var M=R,K=b,Q=K.tag;if((K.mode&1)===0&&(Q===0||Q===11||Q===15)){var U=K.alternate;U?(K.updateQueue=U.updateQueue,K.memoizedState=U.memoizedState,K.lanes=U.lanes):(K.updateQueue=null,K.memoizedState=null)}var ie=Yd(v);if(ie!==null){ie.flags&=-257,Xd(ie,v,b,d,t),ie.mode&1&&Gd(d,M,t),t=ie,R=M;var ce=t.updateQueue;if(ce===null){var pe=new Set;pe.add(R),t.updateQueue=pe}else ce.add(R);break e}else{if((t&1)===0){Gd(d,M,t),Oa();break e}R=Error(l(426))}}else if(Le&&b.mode&1){var Fe=Yd(v);if(Fe!==null){(Fe.flags&65536)===0&&(Fe.flags|=256),Xd(Fe,v,b,d,t),Gs(Or(R,b));break e}}d=R=Or(R,b),Qe!==4&&(Qe=2),Uo===null?Uo=[d]:Uo.push(d),d=v;do{switch(d.tag){case 3:d.flags|=65536,t&=-t,d.lanes|=t;var z=Kd(d,R,t);xd(d,z);break e;case 1:b=R;var T=d.type,L=d.stateNode;if((d.flags&128)===0&&(typeof T.getDerivedStateFromError=="function"||L!==null&&typeof L.componentDidCatch=="function"&&(Tn===null||!Tn.has(L)))){d.flags|=65536,t&=-t,d.lanes|=t;var X=Qd(d,b,t);xd(d,X);break e}}d=d.return}while(d!==null)}Rf(r)}catch(he){t=he,Ve===r&&r!==null&&(Ve=r=r.return);continue}break}while(!0)}function Pf(){var e=li.current;return li.current=Jl,e===null?Jl:e}function Oa(){(Qe===0||Qe===3||Qe===2)&&(Qe=4),Ye===null||(tr&268435455)===0&&(ii&268435455)===0||An(Ye,Je)}function fi(e,t){var r=Ne;Ne|=2;var i=Pf();(Ye!==e||Je!==t)&&(fn=null,rr(e,t));do try{Kv();break}catch(u){kf(e,u)}while(!0);if(Xs(),Ne=r,li.current=i,Ve!==null)throw Error(l(261));return Ye=null,Je=0,Qe}function Kv(){for(;Ve!==null;)Nf(Ve)}function Qv(){for(;Ve!==null&&!yg();)Nf(Ve)}function Nf(e){var t=jf(e.alternate,e,St);e.memoizedProps=e.pendingProps,t===null?Rf(e):Ve=t,Ra.current=null}function Rf(e){var t=e;do{var r=t.alternate;if(e=t.return,(t.flags&32768)===0){if(r=$v(r,t,St),r!==null){Ve=r;return}}else{if(r=Fv(r,t),r!==null){r.flags&=32767,Ve=r;return}if(e!==null)e.flags|=32768,e.subtreeFlags=0,e.deletions=null;else{Qe=6,Ve=null;return}}if(t=t.sibling,t!==null){Ve=t;return}Ve=t=e}while(t!==null);Qe===0&&(Qe=5)}function or(e,t,r){var i=_e,u=Tt.transition;try{Tt.transition=null,_e=1,Gv(e,t,r,i)}finally{Tt.transition=u,_e=i}return null}function Gv(e,t,r,i){do Wr();while(jn!==null);if((Ne&6)!==0)throw Error(l(327));r=e.finishedWork;var u=e.finishedLanes;if(r===null)return null;if(e.finishedWork=null,e.finishedLanes=0,r===e.current)throw Error(l(177));e.callbackNode=null,e.callbackPriority=0;var d=r.lanes|r.childLanes;if(Rg(e,d),e===Ye&&(Ve=Ye=null,Je=0),(r.subtreeFlags&2064)===0&&(r.flags&2064)===0||ai||(ai=!0,zf(gl,function(){return Wr(),null})),d=(r.flags&15990)!==0,(r.subtreeFlags&15990)!==0||d){d=Tt.transition,Tt.transition=null;var v=_e;_e=1;var b=Ne;Ne|=4,Ra.current=null,Vv(e,r),yf(r,e),mv(Os),El=!!Is,Os=Is=null,e.current=r,Hv(r),xg(),Ne=b,_e=v,Tt.transition=d}else e.current=r;if(ai&&(ai=!1,jn=e,ui=u),d=e.pendingLanes,d===0&&(Tn=null),Cg(r.stateNode),mt(e,$e()),t!==null)for(i=e.onRecoverableError,r=0;r<t.length;r++)u=t[r],i(u.value,{componentStack:u.stack,digest:u.digest});if(si)throw si=!1,e=ja,ja=null,e;return(ui&1)!==0&&e.tag!==0&&Wr(),d=e.pendingLanes,(d&1)!==0?e===za?Ko++:(Ko=0,za=e):Ko=0,Pn(),null}function Wr(){if(jn!==null){var e=hc(ui),t=Tt.transition,r=_e;try{if(Tt.transition=null,_e=16>e?16:e,jn===null)var i=!1;else{if(e=jn,jn=null,ui=0,(Ne&6)!==0)throw Error(l(331));var u=Ne;for(Ne|=4,ue=e.current;ue!==null;){var d=ue,v=d.child;if((ue.flags&16)!==0){var b=d.deletions;if(b!==null){for(var R=0;R<b.length;R++){var M=b[R];for(ue=M;ue!==null;){var K=ue;switch(K.tag){case 0:case 11:case 15:Ho(8,K,d)}var Q=K.child;if(Q!==null)Q.return=K,ue=Q;else for(;ue!==null;){K=ue;var U=K.sibling,ie=K.return;if(pf(K),K===M){ue=null;break}if(U!==null){U.return=ie,ue=U;break}ue=ie}}}var ce=d.alternate;if(ce!==null){var pe=ce.child;if(pe!==null){ce.child=null;do{var Fe=pe.sibling;pe.sibling=null,pe=Fe}while(pe!==null)}}ue=d}}if((d.subtreeFlags&2064)!==0&&v!==null)v.return=d,ue=v;else e:for(;ue!==null;){if(d=ue,(d.flags&2048)!==0)switch(d.tag){case 0:case 11:case 15:Ho(9,d,d.return)}var z=d.sibling;if(z!==null){z.return=d.return,ue=z;break e}ue=d.return}}var T=e.current;for(ue=T;ue!==null;){v=ue;var L=v.child;if((v.subtreeFlags&2064)!==0&&L!==null)L.return=v,ue=L;else e:for(v=T;ue!==null;){if(b=ue,(b.flags&2048)!==0)try{switch(b.tag){case 0:case 11:case 15:oi(9,b)}}catch(he){Oe(b,b.return,he)}if(b===v){ue=null;break e}var X=b.sibling;if(X!==null){X.return=b.return,ue=X;break e}ue=b.return}}if(Ne=u,Pn(),Qt&&typeof Qt.onPostCommitFiberRoot=="function")try{Qt.onPostCommitFiberRoot(vl,e)}catch{}i=!0}return i}finally{_e=r,Tt.transition=t}}return!1}function _f(e,t,r){t=Or(r,t),t=Kd(e,t,1),e=Rn(e,t,1),t=ut(),e!==null&&(go(e,1,t),mt(e,t))}function Oe(e,t,r){if(e.tag===3)_f(e,e,r);else for(;t!==null;){if(t.tag===3){_f(t,e,r);break}else if(t.tag===1){var i=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof i.componentDidCatch=="function"&&(Tn===null||!Tn.has(i))){e=Or(r,e),e=Qd(t,e,1),t=Rn(t,e,1),e=ut(),t!==null&&(go(t,1,e),mt(t,e));break}}t=t.return}}function Yv(e,t,r){var i=e.pingCache;i!==null&&i.delete(t),t=ut(),e.pingedLanes|=e.suspendedLanes&r,Ye===e&&(Je&r)===r&&(Qe===4||Qe===3&&(Je&130023424)===Je&&500>$e()-Ta?rr(e,0):_a|=r),mt(e,t)}function Tf(e,t){t===0&&((e.mode&1)===0?t=1:(t=xl,xl<<=1,(xl&130023424)===0&&(xl=4194304)));var r=ut();e=un(e,t),e!==null&&(go(e,t,r),mt(e,r))}function Xv(e){var t=e.memoizedState,r=0;t!==null&&(r=t.retryLane),Tf(e,r)}function Zv(e,t){var r=0;switch(e.tag){case 13:var i=e.stateNode,u=e.memoizedState;u!==null&&(r=u.retryLane);break;case 19:i=e.stateNode;break;default:throw Error(l(314))}i!==null&&i.delete(t),Tf(e,r)}var jf;jf=function(e,t,r){if(e!==null)if(e.memoizedProps!==t.pendingProps||ct.current)ft=!0;else{if((e.lanes&r)===0&&(t.flags&128)===0)return ft=!1,Dv(e,t,r);ft=(e.flags&131072)!==0}else ft=!1,Le&&(t.flags&1048576)!==0&&ud(t,Wl,t.index);switch(t.lanes=0,t.tag){case 2:var i=t.type;ni(e,t),e=t.pendingProps;var u=_r(t,nt.current);Mr(t,r),u=sa(null,t,i,e,u,r);var d=aa();return t.flags|=1,typeof u=="object"&&u!==null&&typeof u.render=="function"&&u.$$typeof===void 0?(t.tag=1,t.memoizedState=null,t.updateQueue=null,dt(i)?(d=!0,Dl(t)):d=!1,t.memoizedState=u.state!==null&&u.state!==void 0?u.state:null,ea(t),u.updater=ei,t.stateNode=u,u._reactInternals=t,ma(t,i,e,r),t=ya(null,t,i,!0,d,r)):(t.tag=0,Le&&d&&Bs(t),at(null,t,u,r),t=t.child),t;case 16:i=t.elementType;e:{switch(ni(e,t),e=t.pendingProps,u=i._init,i=u(i._payload),t.type=i,u=t.tag=Jv(i),e=$t(i,e),u){case 0:t=va(null,t,i,e,r);break e;case 1:t=nf(null,t,i,e,r);break e;case 11:t=Zd(null,t,i,e,r);break e;case 14:t=qd(null,t,i,$t(i.type,e),r);break e}throw Error(l(306,i,""))}return t;case 0:return i=t.type,u=t.pendingProps,u=t.elementType===i?u:$t(i,u),va(e,t,i,u,r);case 1:return i=t.type,u=t.pendingProps,u=t.elementType===i?u:$t(i,u),nf(e,t,i,u,r);case 3:e:{if(rf(t),e===null)throw Error(l(387));i=t.pendingProps,d=t.memoizedState,u=d.element,yd(e,t),Ql(t,i,null,r);var v=t.memoizedState;if(i=v.element,d.isDehydrated)if(d={element:i,isDehydrated:!1,cache:v.cache,pendingSuspenseBoundaries:v.pendingSuspenseBoundaries,transitions:v.transitions},t.updateQueue.baseState=d,t.memoizedState=d,t.flags&256){u=Or(Error(l(423)),t),t=of(e,t,i,r,u);break e}else if(i!==u){u=Or(Error(l(424)),t),t=of(e,t,i,r,u);break e}else for(wt=En(t.stateNode.containerInfo.firstChild),xt=t,Le=!0,Dt=null,r=gd(t,null,i,r),t.child=r;r;)r.flags=r.flags&-3|4096,r=r.sibling;else{if(zr(),i===u){t=dn(e,t,r);break e}at(e,t,i,r)}t=t.child}return t;case 5:return Sd(t),e===null&&Qs(t),i=t.type,u=t.pendingProps,d=e!==null?e.memoizedProps:null,v=u.children,Ds(i,u)?v=null:d!==null&&Ds(i,d)&&(t.flags|=32),tf(e,t),at(e,t,v,r),t.child;case 6:return e===null&&Qs(t),null;case 13:return lf(e,t,r);case 4:return ta(t,t.stateNode.containerInfo),i=t.pendingProps,e===null?t.child=Ar(t,null,i,r):at(e,t,i,r),t.child;case 11:return i=t.type,u=t.pendingProps,u=t.elementType===i?u:$t(i,u),Zd(e,t,i,u,r);case 7:return at(e,t,t.pendingProps,r),t.child;case 8:return at(e,t,t.pendingProps.children,r),t.child;case 12:return at(e,t,t.pendingProps.children,r),t.child;case 10:e:{if(i=t.type._context,u=t.pendingProps,d=t.memoizedProps,v=u.value,Te(Bl,i._currentValue),i._currentValue=v,d!==null)if(Ot(d.value,v)){if(d.children===u.children&&!ct.current){t=dn(e,t,r);break e}}else for(d=t.child,d!==null&&(d.return=t);d!==null;){var b=d.dependencies;if(b!==null){v=d.child;for(var R=b.firstContext;R!==null;){if(R.context===i){if(d.tag===1){R=cn(-1,r&-r),R.tag=2;var M=d.updateQueue;if(M!==null){M=M.shared;var K=M.pending;K===null?R.next=R:(R.next=K.next,K.next=R),M.pending=R}}d.lanes|=r,R=d.alternate,R!==null&&(R.lanes|=r),qs(d.return,r,t),b.lanes|=r;break}R=R.next}}else if(d.tag===10)v=d.type===t.type?null:d.child;else if(d.tag===18){if(v=d.return,v===null)throw Error(l(341));v.lanes|=r,b=v.alternate,b!==null&&(b.lanes|=r),qs(v,r,t),v=d.sibling}else v=d.child;if(v!==null)v.return=d;else for(v=d;v!==null;){if(v===t){v=null;break}if(d=v.sibling,d!==null){d.return=v.return,v=d;break}v=v.return}d=v}at(e,t,u.children,r),t=t.child}return t;case 9:return u=t.type,i=t.pendingProps.children,Mr(t,r),u=Rt(u),i=i(u),t.flags|=1,at(e,t,i,r),t.child;case 14:return i=t.type,u=$t(i,t.pendingProps),u=$t(i.type,u),qd(e,t,i,u,r);case 15:return Jd(e,t,t.type,t.pendingProps,r);case 17:return i=t.type,u=t.pendingProps,u=t.elementType===i?u:$t(i,u),ni(e,t),t.tag=1,dt(i)?(e=!0,Dl(t)):e=!1,Mr(t,r),Bd(t,i,u),ma(t,i,u,r),ya(null,t,i,!0,e,r);case 19:return af(e,t,r);case 22:return ef(e,t,r)}throw Error(l(156,t.tag))};function zf(e,t){return cc(e,t)}function qv(e,t,r,i){this.tag=e,this.key=r,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=i,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function jt(e,t,r,i){return new qv(e,t,r,i)}function Da(e){return e=e.prototype,!(!e||!e.isReactComponent)}function Jv(e){if(typeof e=="function")return Da(e)?1:0;if(e!=null){if(e=e.$$typeof,e===Z)return 11;if(e===ve)return 14}return 2}function Ln(e,t){var r=e.alternate;return r===null?(r=jt(e.tag,t,e.key,e.mode),r.elementType=e.elementType,r.type=e.type,r.stateNode=e.stateNode,r.alternate=e,e.alternate=r):(r.pendingProps=t,r.type=e.type,r.flags=0,r.subtreeFlags=0,r.deletions=null),r.flags=e.flags&14680064,r.childLanes=e.childLanes,r.lanes=e.lanes,r.child=e.child,r.memoizedProps=e.memoizedProps,r.memoizedState=e.memoizedState,r.updateQueue=e.updateQueue,t=e.dependencies,r.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},r.sibling=e.sibling,r.index=e.index,r.ref=e.ref,r}function pi(e,t,r,i,u,d){var v=2;if(i=e,typeof e=="function")Da(e)&&(v=1);else if(typeof e=="string")v=5;else e:switch(e){case D:return lr(r.children,u,d,t);case oe:v=8,u|=8;break;case re:return e=jt(12,r,t,u|2),e.elementType=re,e.lanes=d,e;case ge:return e=jt(13,r,t,u),e.elementType=ge,e.lanes=d,e;case se:return e=jt(19,r,t,u),e.elementType=se,e.lanes=d,e;case de:return mi(r,u,d,t);default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case ae:v=10;break e;case me:v=9;break e;case Z:v=11;break e;case ve:v=14;break e;case le:v=16,i=null;break e}throw Error(l(130,e==null?e:typeof e,""))}return t=jt(v,r,t,u),t.elementType=e,t.type=i,t.lanes=d,t}function lr(e,t,r,i){return e=jt(7,e,i,t),e.lanes=r,e}function mi(e,t,r,i){return e=jt(22,e,i,t),e.elementType=de,e.lanes=r,e.stateNode={isHidden:!1},e}function $a(e,t,r){return e=jt(6,e,null,t),e.lanes=r,e}function Fa(e,t,r){return t=jt(4,e.children!==null?e.children:[],e.key,t),t.lanes=r,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function ey(e,t,r,i,u){this.tag=t,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=ps(0),this.expirationTimes=ps(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=ps(0),this.identifierPrefix=i,this.onRecoverableError=u,this.mutableSourceEagerHydrationData=null}function Wa(e,t,r,i,u,d,v,b,R){return e=new ey(e,t,r,b,R),t===1?(t=1,d===!0&&(t|=8)):t=0,d=jt(3,null,null,t),e.current=d,d.stateNode=e,d.memoizedState={element:i,isDehydrated:r,cache:null,transitions:null,pendingSuspenseBoundaries:null},ea(d),e}function ty(e,t,r){var i=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:B,key:i==null?null:""+i,children:e,containerInfo:t,implementation:r}}function Af(e){if(!e)return kn;e=e._reactInternals;e:{if(Qn(e)!==e||e.tag!==1)throw Error(l(170));var t=e;do{switch(t.tag){case 3:t=t.stateNode.context;break e;case 1:if(dt(t.type)){t=t.stateNode.__reactInternalMemoizedMergedChildContext;break e}}t=t.return}while(t!==null);throw Error(l(171))}if(e.tag===1){var r=e.type;if(dt(r))return id(e,r,t)}return t}function Lf(e,t,r,i,u,d,v,b,R){return e=Wa(r,i,!0,e,u,d,v,b,R),e.context=Af(null),r=e.current,i=ut(),u=zn(r),d=cn(i,u),d.callback=t??null,Rn(r,d,u),e.current.lanes=u,go(e,u,i),mt(e,i),e}function hi(e,t,r,i){var u=t.current,d=ut(),v=zn(u);return r=Af(r),t.context===null?t.context=r:t.pendingContext=r,t=cn(d,v),t.payload={element:e},i=i===void 0?null:i,i!==null&&(t.callback=i),e=Rn(u,t,v),e!==null&&(Vt(e,u,v,d),Kl(e,u,v)),v}function gi(e){if(e=e.current,!e.child)return null;switch(e.child.tag){case 5:return e.child.stateNode;default:return e.child.stateNode}}function Mf(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var r=e.retryLane;e.retryLane=r!==0&&r<t?r:t}}function Va(e,t){Mf(e,t),(e=e.alternate)&&Mf(e,t)}function ny(){return null}var If=typeof reportError=="function"?reportError:function(e){console.error(e)};function Ha(e){this._internalRoot=e}vi.prototype.render=Ha.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(l(409));hi(e,t,null,null)},vi.prototype.unmount=Ha.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;nr(function(){hi(null,e,null,null)}),t[on]=null}};function vi(e){this._internalRoot=e}vi.prototype.unstable_scheduleHydration=function(e){if(e){var t=yc();e={blockedOn:null,target:e,priority:t};for(var r=0;r<wn.length&&t!==0&&t<wn[r].priority;r++);wn.splice(r,0,e),r===0&&Sc(e)}};function Ba(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function yi(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11&&(e.nodeType!==8||e.nodeValue!==" react-mount-point-unstable "))}function Of(){}function ry(e,t,r,i,u){if(u){if(typeof i=="function"){var d=i;i=function(){var M=gi(v);d.call(M)}}var v=Lf(t,i,e,0,null,!1,!1,"",Of);return e._reactRootContainer=v,e[on]=v.current,To(e.nodeType===8?e.parentNode:e),nr(),v}for(;u=e.lastChild;)e.removeChild(u);if(typeof i=="function"){var b=i;i=function(){var M=gi(R);b.call(M)}}var R=Wa(e,0,!1,null,null,!1,!1,"",Of);return e._reactRootContainer=R,e[on]=R.current,To(e.nodeType===8?e.parentNode:e),nr(function(){hi(t,R,r,i)}),R}function xi(e,t,r,i,u){var d=r._reactRootContainer;if(d){var v=d;if(typeof u=="function"){var b=u;u=function(){var R=gi(v);b.call(R)}}hi(t,v,e,u)}else v=ry(r,t,e,u,i);return gi(v)}gc=function(e){switch(e.tag){case 3:var t=e.stateNode;if(t.current.memoizedState.isDehydrated){var r=ho(t.pendingLanes);r!==0&&(ms(t,r|1),mt(t,$e()),(Ne&6)===0&&(Fr=$e()+500,Pn()))}break;case 13:nr(function(){var i=un(e,1);if(i!==null){var u=ut();Vt(i,e,1,u)}}),Va(e,1)}},hs=function(e){if(e.tag===13){var t=un(e,134217728);if(t!==null){var r=ut();Vt(t,e,134217728,r)}Va(e,134217728)}},vc=function(e){if(e.tag===13){var t=zn(e),r=un(e,t);if(r!==null){var i=ut();Vt(r,e,t,i)}Va(e,t)}},yc=function(){return _e},xc=function(e,t){var r=_e;try{return _e=e,t()}finally{_e=r}},ss=function(e,t,r){switch(t){case"input":if(Ze(e,r),t=r.name,r.type==="radio"&&t!=null){for(r=e;r.parentNode;)r=r.parentNode;for(r=r.querySelectorAll("input[name="+JSON.stringify(""+t)+'][type="radio"]'),t=0;t<r.length;t++){var i=r[t];if(i!==e&&i.form===e.form){var u=Il(i);if(!u)throw Error(l(90));we(i),Ze(i,u)}}}break;case"textarea":Gu(e,r);break;case"select":t=r.value,t!=null&&kt(e,!!r.multiple,t,!1)}},rc=Ma,oc=nr;var oy={usingClientEntryPoint:!1,Events:[Ao,Nr,Il,tc,nc,Ma]},Qo={findFiberByHostInstance:Gn,bundleType:0,version:"18.3.1",rendererPackageName:"react-dom"},ly={bundleType:Qo.bundleType,version:Qo.version,rendererPackageName:Qo.rendererPackageName,rendererConfig:Qo.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:O.ReactCurrentDispatcher,findHostInstanceByFiber:function(e){return e=ac(e),e===null?null:e.stateNode},findFiberByHostInstance:Qo.findFiberByHostInstance||ny,findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.3.1-next-f1338f8080-20240426"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var wi=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!wi.isDisabled&&wi.supportsFiber)try{vl=wi.inject(ly),Qt=wi}catch{}}return ht.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=oy,ht.createPortal=function(e,t){var r=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!Ba(t))throw Error(l(200));return ty(e,t,null,r)},ht.createRoot=function(e,t){if(!Ba(e))throw Error(l(299));var r=!1,i="",u=If;return t!=null&&(t.unstable_strictMode===!0&&(r=!0),t.identifierPrefix!==void 0&&(i=t.identifierPrefix),t.onRecoverableError!==void 0&&(u=t.onRecoverableError)),t=Wa(e,1,!1,null,null,r,!1,i,u),e[on]=t.current,To(e.nodeType===8?e.parentNode:e),new Ha(t)},ht.findDOMNode=function(e){if(e==null)return null;if(e.nodeType===1)return e;var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(l(188)):(e=Object.keys(e).join(","),Error(l(268,e)));return e=ac(t),e=e===null?null:e.stateNode,e},ht.flushSync=function(e){return nr(e)},ht.hydrate=function(e,t,r){if(!yi(t))throw Error(l(200));return xi(null,e,t,!0,r)},ht.hydrateRoot=function(e,t,r){if(!Ba(e))throw Error(l(405));var i=r!=null&&r.hydratedSources||null,u=!1,d="",v=If;if(r!=null&&(r.unstable_strictMode===!0&&(u=!0),r.identifierPrefix!==void 0&&(d=r.identifierPrefix),r.onRecoverableError!==void 0&&(v=r.onRecoverableError)),t=Lf(t,null,e,1,r??null,u,!1,d,v),e[on]=t.current,To(e),i)for(e=0;e<i.length;e++)r=i[e],u=r._getVersion,u=u(r._source),t.mutableSourceEagerHydrationData==null?t.mutableSourceEagerHydrationData=[r,u]:t.mutableSourceEagerHydrationData.push(r,u);return new vi(t)},ht.render=function(e,t,r){if(!yi(t))throw Error(l(200));return xi(null,e,t,!1,r)},ht.unmountComponentAtNode=function(e){if(!yi(e))throw Error(l(40));return e._reactRootContainer?(nr(function(){xi(null,null,e,!1,function(){e._reactRootContainer=null,e[on]=null})}),!0):!1},ht.unstable_batchedUpdates=Ma,ht.unstable_renderSubtreeIntoContainer=function(e,t,r,i){if(!yi(r))throw Error(l(200));if(e==null||e._reactInternals===void 0)throw Error(l(38));return xi(e,t,r,!1,i)},ht.version="18.3.1-next-f1338f8080-20240426",ht}var Uf;function Qp(){if(Uf)return Qa.exports;Uf=1;function n(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(n)}catch(o){console.error(o)}}return n(),Qa.exports=py(),Qa.exports}var Kf;function my(){if(Kf)return Si;Kf=1;var n=Qp();return Si.createRoot=n.createRoot,Si.hydrateRoot=n.hydrateRoot,Si}var hy=my(),sl=Qp();const gy=Kp(sl);function Qf(n,o){if(typeof n=="function")return n(o);n!=null&&(n.current=o)}function Gp(...n){return o=>{let l=!1;const s=n.map(a=>{const c=Qf(a,o);return!l&&typeof c=="function"&&(l=!0),c});if(l)return()=>{for(let a=0;a<s.length;a++){const c=s[a];typeof c=="function"?c():Qf(n[a],null)}}}}function oo(...n){return h.useCallback(Gp(...n),n)}function vy(n){const o=yy(n),l=h.forwardRef((s,a)=>{const{children:c,...p}=s,f=h.Children.toArray(c),m=f.find(wy);if(m){const y=m.props.children,w=f.map(x=>x===m?h.Children.count(y)>1?h.Children.only(null):h.isValidElement(y)?y.props.children:null:x);return g.jsx(o,{...p,ref:a,children:h.isValidElement(y)?h.cloneElement(y,void 0,w):null})}return g.jsx(o,{...p,ref:a,children:c})});return l.displayName=`${n}.Slot`,l}function yy(n){const o=h.forwardRef((l,s)=>{const{children:a,...c}=l;if(h.isValidElement(a)){const p=Cy(a),f=Sy(c,a.props);return a.type!==h.Fragment&&(f.ref=s?Gp(s,p):p),h.cloneElement(a,f)}return h.Children.count(a)>1?h.Children.only(null):null});return o.displayName=`${n}.SlotClone`,o}var xy=Symbol("radix.slottable");function wy(n){return h.isValidElement(n)&&typeof n.type=="function"&&"__radixId"in n.type&&n.type.__radixId===xy}function Sy(n,o){const l={...o};for(const s in o){const a=n[s],c=o[s];/^on[A-Z]/.test(s)?a&&c?l[s]=(...f)=>{const m=c(...f);return a(...f),m}:a&&(l[s]=a):s==="style"?l[s]={...a,...c}:s==="className"&&(l[s]=[a,c].filter(Boolean).join(" "))}return{...n,...l}}function Cy(n){var s,a;let o=(s=Object.getOwnPropertyDescriptor(n.props,"ref"))==null?void 0:s.get,l=o&&"isReactWarning"in o&&o.isReactWarning;return l?n.ref:(o=(a=Object.getOwnPropertyDescriptor(n,"ref"))==null?void 0:a.get,l=o&&"isReactWarning"in o&&o.isReactWarning,l?n.props.ref:n.props.ref||n.ref)}var Ey=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"],al=Ey.reduce((n,o)=>{const l=vy(`Primitive.${o}`),s=h.forwardRef((a,c)=>{const{asChild:p,...f}=a,m=p?l:o;return typeof window<"u"&&(window[Symbol.for("radix-ui")]=!0),g.jsx(m,{...f,ref:c})});return s.displayName=`Primitive.${o}`,{...n,[o]:s}},{});function Gf(n,o){if(typeof n=="function")return n(o);n!=null&&(n.current=o)}function by(...n){return o=>{let l=!1;const s=n.map(a=>{const c=Gf(a,o);return!l&&typeof c=="function"&&(l=!0),c});if(l)return()=>{for(let a=0;a<s.length;a++){const c=s[a];typeof c=="function"?c():Gf(n[a],null)}}}}function ky(...n){return h.useCallback(by(...n),n)}var nl=globalThis!=null&&globalThis.document?h.useLayoutEffect:()=>{};function Py(n,o){return h.useReducer((l,s)=>o[l][s]??l,n)}var ul=n=>{const{present:o,children:l}=n,s=Ny(o),a=typeof l=="function"?l({present:s.isPresent}):h.Children.only(l),c=ky(s.ref,Ry(a));return typeof l=="function"||s.isPresent?h.cloneElement(a,{ref:c}):null};ul.displayName="Presence";function Ny(n){const[o,l]=h.useState(),s=h.useRef(null),a=h.useRef(n),c=h.useRef("none"),p=n?"mounted":"unmounted",[f,m]=Py(p,{mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}});return h.useEffect(()=>{const y=Ci(s.current);c.current=f==="mounted"?y:"none"},[f]),nl(()=>{const y=s.current,w=a.current;if(w!==n){const S=c.current,E=Ci(y);n?m("MOUNT"):E==="none"||(y==null?void 0:y.display)==="none"?m("UNMOUNT"):m(w&&S!==E?"ANIMATION_OUT":"UNMOUNT"),a.current=n}},[n,m]),nl(()=>{if(o){let y;const w=o.ownerDocument.defaultView??window,x=E=>{const C=Ci(s.current).includes(E.animationName);if(E.target===o&&C&&(m("ANIMATION_END"),!a.current)){const k=o.style.animationFillMode;o.style.animationFillMode="forwards",y=w.setTimeout(()=>{o.style.animationFillMode==="forwards"&&(o.style.animationFillMode=k)})}},S=E=>{E.target===o&&(c.current=Ci(s.current))};return o.addEventListener("animationstart",S),o.addEventListener("animationcancel",x),o.addEventListener("animationend",x),()=>{w.clearTimeout(y),o.removeEventListener("animationstart",S),o.removeEventListener("animationcancel",x),o.removeEventListener("animationend",x)}}else m("ANIMATION_END")},[o,m]),{isPresent:["mounted","unmountSuspended"].includes(f),ref:h.useCallback(y=>{s.current=y?getComputedStyle(y):null,l(y)},[])}}function Ci(n){return(n==null?void 0:n.animationName)||"none"}function Ry(n){var s,a;let o=(s=Object.getOwnPropertyDescriptor(n.props,"ref"))==null?void 0:s.get,l=o&&"isReactWarning"in o&&o.isReactWarning;return l?n.ref:(o=(a=Object.getOwnPropertyDescriptor(n,"ref"))==null?void 0:a.get,l=o&&"isReactWarning"in o&&o.isReactWarning,l?n.props.ref:n.props.ref||n.ref)}function _y(n,o=[]){let l=[];function s(c,p){const f=h.createContext(p),m=l.length;l=[...l,p];const y=x=>{var N;const{scope:S,children:E,...P}=x,C=((N=S==null?void 0:S[n])==null?void 0:N[m])||f,k=h.useMemo(()=>P,Object.values(P));return g.jsx(C.Provider,{value:k,children:E})};y.displayName=c+"Provider";function w(x,S){var C;const E=((C=S==null?void 0:S[n])==null?void 0:C[m])||f,P=h.useContext(E);if(P)return P;if(p!==void 0)return p;throw new Error(`\`${x}\` must be used within \`${c}\``)}return[y,w]}const a=()=>{const c=l.map(p=>h.createContext(p));return function(f){const m=(f==null?void 0:f[n])||c;return h.useMemo(()=>({[`__scope${n}`]:{...f,[n]:m}}),[f,m])}};return a.scopeName=n,[s,Ty(a,...o)]}function Ty(...n){const o=n[0];if(n.length===1)return o;const l=()=>{const s=n.map(a=>({useScope:a(),scopeName:a.scopeName}));return function(c){const p=s.reduce((f,{useScope:m,scopeName:y})=>{const x=m(c)[`__scope${y}`];return{...f,...x}},{});return h.useMemo(()=>({[`__scope${o.scopeName}`]:p}),[p])}};return l.scopeName=o.scopeName,l}function sr(n){const o=h.useRef(n);return h.useEffect(()=>{o.current=n}),h.useMemo(()=>(...l)=>{var s;return(s=o.current)==null?void 0:s.call(o,...l)},[])}var jy=h.createContext(void 0);function zy(n){const o=h.useContext(jy);return n||o||"ltr"}function Ay(n,[o,l]){return Math.min(l,Math.max(o,n))}function ur(n,o,{checkForDefaultPrevented:l=!0}={}){return function(a){if(n==null||n(a),l===!1||!a.defaultPrevented)return o==null?void 0:o(a)}}function Ly(n,o){return h.useReducer((l,s)=>o[l][s]??l,n)}var bu="ScrollArea",[Yp,eE]=_y(bu),[My,Lt]=Yp(bu),Xp=h.forwardRef((n,o)=>{const{__scopeScrollArea:l,type:s="hover",dir:a,scrollHideDelay:c=600,...p}=n,[f,m]=h.useState(null),[y,w]=h.useState(null),[x,S]=h.useState(null),[E,P]=h.useState(null),[C,k]=h.useState(null),[N,j]=h.useState(0),[A,O]=h.useState(0),[I,B]=h.useState(!1),[D,oe]=h.useState(!1),re=oo(o,me=>m(me)),ae=zy(a);return g.jsx(My,{scope:l,type:s,dir:ae,scrollHideDelay:c,scrollArea:f,viewport:y,onViewportChange:w,content:x,onContentChange:S,scrollbarX:E,onScrollbarXChange:P,scrollbarXEnabled:I,onScrollbarXEnabledChange:B,scrollbarY:C,onScrollbarYChange:k,scrollbarYEnabled:D,onScrollbarYEnabledChange:oe,onCornerWidthChange:j,onCornerHeightChange:O,children:g.jsx(al.div,{dir:ae,...p,ref:re,style:{position:"relative","--radix-scroll-area-corner-width":N+"px","--radix-scroll-area-corner-height":A+"px",...n.style}})})});Xp.displayName=bu;var Zp="ScrollAreaViewport",qp=h.forwardRef((n,o)=>{const{__scopeScrollArea:l,children:s,nonce:a,...c}=n,p=Lt(Zp,l),f=h.useRef(null),m=oo(o,f,p.onViewportChange);return g.jsxs(g.Fragment,{children:[g.jsx("style",{dangerouslySetInnerHTML:{__html:"[data-radix-scroll-area-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-scroll-area-viewport]::-webkit-scrollbar{display:none}"},nonce:a}),g.jsx(al.div,{"data-radix-scroll-area-viewport":"",...c,ref:m,style:{overflowX:p.scrollbarXEnabled?"scroll":"hidden",overflowY:p.scrollbarYEnabled?"scroll":"hidden",...n.style},children:g.jsx("div",{ref:p.onContentChange,style:{minWidth:"100%",display:"table"},children:s})})]})});qp.displayName=Zp;var nn="ScrollAreaScrollbar",Jp=h.forwardRef((n,o)=>{const{forceMount:l,...s}=n,a=Lt(nn,n.__scopeScrollArea),{onScrollbarXEnabledChange:c,onScrollbarYEnabledChange:p}=a,f=n.orientation==="horizontal";return h.useEffect(()=>(f?c(!0):p(!0),()=>{f?c(!1):p(!1)}),[f,c,p]),a.type==="hover"?g.jsx(Iy,{...s,ref:o,forceMount:l}):a.type==="scroll"?g.jsx(Oy,{...s,ref:o,forceMount:l}):a.type==="auto"?g.jsx(em,{...s,ref:o,forceMount:l}):a.type==="always"?g.jsx(ku,{...s,ref:o}):null});Jp.displayName=nn;var Iy=h.forwardRef((n,o)=>{const{forceMount:l,...s}=n,a=Lt(nn,n.__scopeScrollArea),[c,p]=h.useState(!1);return h.useEffect(()=>{const f=a.scrollArea;let m=0;if(f){const y=()=>{window.clearTimeout(m),p(!0)},w=()=>{m=window.setTimeout(()=>p(!1),a.scrollHideDelay)};return f.addEventListener("pointerenter",y),f.addEventListener("pointerleave",w),()=>{window.clearTimeout(m),f.removeEventListener("pointerenter",y),f.removeEventListener("pointerleave",w)}}},[a.scrollArea,a.scrollHideDelay]),g.jsx(ul,{present:l||c,children:g.jsx(em,{"data-state":c?"visible":"hidden",...s,ref:o})})}),Oy=h.forwardRef((n,o)=>{const{forceMount:l,...s}=n,a=Lt(nn,n.__scopeScrollArea),c=n.orientation==="horizontal",p=Hi(()=>m("SCROLL_END"),100),[f,m]=Ly("hidden",{hidden:{SCROLL:"scrolling"},scrolling:{SCROLL_END:"idle",POINTER_ENTER:"interacting"},interacting:{SCROLL:"interacting",POINTER_LEAVE:"idle"},idle:{HIDE:"hidden",SCROLL:"scrolling",POINTER_ENTER:"interacting"}});return h.useEffect(()=>{if(f==="idle"){const y=window.setTimeout(()=>m("HIDE"),a.scrollHideDelay);return()=>window.clearTimeout(y)}},[f,a.scrollHideDelay,m]),h.useEffect(()=>{const y=a.viewport,w=c?"scrollLeft":"scrollTop";if(y){let x=y[w];const S=()=>{const E=y[w];x!==E&&(m("SCROLL"),p()),x=E};return y.addEventListener("scroll",S),()=>y.removeEventListener("scroll",S)}},[a.viewport,c,m,p]),g.jsx(ul,{present:l||f!=="hidden",children:g.jsx(ku,{"data-state":f==="hidden"?"hidden":"visible",...s,ref:o,onPointerEnter:ur(n.onPointerEnter,()=>m("POINTER_ENTER")),onPointerLeave:ur(n.onPointerLeave,()=>m("POINTER_LEAVE"))})})}),em=h.forwardRef((n,o)=>{const l=Lt(nn,n.__scopeScrollArea),{forceMount:s,...a}=n,[c,p]=h.useState(!1),f=n.orientation==="horizontal",m=Hi(()=>{if(l.viewport){const y=l.viewport.offsetWidth<l.viewport.scrollWidth,w=l.viewport.offsetHeight<l.viewport.scrollHeight;p(f?y:w)}},10);return to(l.viewport,m),to(l.content,m),g.jsx(ul,{present:s||c,children:g.jsx(ku,{"data-state":c?"visible":"hidden",...a,ref:o})})}),ku=h.forwardRef((n,o)=>{const{orientation:l="vertical",...s}=n,a=Lt(nn,n.__scopeScrollArea),c=h.useRef(null),p=h.useRef(0),[f,m]=h.useState({content:0,viewport:0,scrollbar:{size:0,paddingStart:0,paddingEnd:0}}),y=lm(f.viewport,f.content),w={...s,sizes:f,onSizesChange:m,hasThumb:y>0&&y<1,onThumbChange:S=>c.current=S,onThumbPointerUp:()=>p.current=0,onThumbPointerDown:S=>p.current=S};function x(S,E){return Hy(S,p.current,f,E)}return l==="horizontal"?g.jsx(Dy,{...w,ref:o,onThumbPositionChange:()=>{if(a.viewport&&c.current){const S=a.viewport.scrollLeft,E=Yf(S,f,a.dir);c.current.style.transform=`translate3d(${E}px, 0, 0)`}},onWheelScroll:S=>{a.viewport&&(a.viewport.scrollLeft=S)},onDragScroll:S=>{a.viewport&&(a.viewport.scrollLeft=x(S,a.dir))}}):l==="vertical"?g.jsx($y,{...w,ref:o,onThumbPositionChange:()=>{if(a.viewport&&c.current){const S=a.viewport.scrollTop,E=Yf(S,f);c.current.style.transform=`translate3d(0, ${E}px, 0)`}},onWheelScroll:S=>{a.viewport&&(a.viewport.scrollTop=S)},onDragScroll:S=>{a.viewport&&(a.viewport.scrollTop=x(S))}}):null}),Dy=h.forwardRef((n,o)=>{const{sizes:l,onSizesChange:s,...a}=n,c=Lt(nn,n.__scopeScrollArea),[p,f]=h.useState(),m=h.useRef(null),y=oo(o,m,c.onScrollbarXChange);return h.useEffect(()=>{m.current&&f(getComputedStyle(m.current))},[m]),g.jsx(nm,{"data-orientation":"horizontal",...a,ref:y,sizes:l,style:{bottom:0,left:c.dir==="rtl"?"var(--radix-scroll-area-corner-width)":0,right:c.dir==="ltr"?"var(--radix-scroll-area-corner-width)":0,"--radix-scroll-area-thumb-width":Vi(l)+"px",...n.style},onThumbPointerDown:w=>n.onThumbPointerDown(w.x),onDragScroll:w=>n.onDragScroll(w.x),onWheelScroll:(w,x)=>{if(c.viewport){const S=c.viewport.scrollLeft+w.deltaX;n.onWheelScroll(S),sm(S,x)&&w.preventDefault()}},onResize:()=>{m.current&&c.viewport&&p&&s({content:c.viewport.scrollWidth,viewport:c.viewport.offsetWidth,scrollbar:{size:m.current.clientWidth,paddingStart:Ai(p.paddingLeft),paddingEnd:Ai(p.paddingRight)}})}})}),$y=h.forwardRef((n,o)=>{const{sizes:l,onSizesChange:s,...a}=n,c=Lt(nn,n.__scopeScrollArea),[p,f]=h.useState(),m=h.useRef(null),y=oo(o,m,c.onScrollbarYChange);return h.useEffect(()=>{m.current&&f(getComputedStyle(m.current))},[m]),g.jsx(nm,{"data-orientation":"vertical",...a,ref:y,sizes:l,style:{top:0,right:c.dir==="ltr"?0:void 0,left:c.dir==="rtl"?0:void 0,bottom:"var(--radix-scroll-area-corner-height)","--radix-scroll-area-thumb-height":Vi(l)+"px",...n.style},onThumbPointerDown:w=>n.onThumbPointerDown(w.y),onDragScroll:w=>n.onDragScroll(w.y),onWheelScroll:(w,x)=>{if(c.viewport){const S=c.viewport.scrollTop+w.deltaY;n.onWheelScroll(S),sm(S,x)&&w.preventDefault()}},onResize:()=>{m.current&&c.viewport&&p&&s({content:c.viewport.scrollHeight,viewport:c.viewport.offsetHeight,scrollbar:{size:m.current.clientHeight,paddingStart:Ai(p.paddingTop),paddingEnd:Ai(p.paddingBottom)}})}})}),[Fy,tm]=Yp(nn),nm=h.forwardRef((n,o)=>{const{__scopeScrollArea:l,sizes:s,hasThumb:a,onThumbChange:c,onThumbPointerUp:p,onThumbPointerDown:f,onThumbPositionChange:m,onDragScroll:y,onWheelScroll:w,onResize:x,...S}=n,E=Lt(nn,l),[P,C]=h.useState(null),k=oo(o,re=>C(re)),N=h.useRef(null),j=h.useRef(""),A=E.viewport,O=s.content-s.viewport,I=sr(w),B=sr(m),D=Hi(x,10);function oe(re){if(N.current){const ae=re.clientX-N.current.left,me=re.clientY-N.current.top;y({x:ae,y:me})}}return h.useEffect(()=>{const re=ae=>{const me=ae.target;(P==null?void 0:P.contains(me))&&I(ae,O)};return document.addEventListener("wheel",re,{passive:!1}),()=>document.removeEventListener("wheel",re,{passive:!1})},[A,P,O,I]),h.useEffect(B,[s,B]),to(P,D),to(E.content,D),g.jsx(Fy,{scope:l,scrollbar:P,hasThumb:a,onThumbChange:sr(c),onThumbPointerUp:sr(p),onThumbPositionChange:B,onThumbPointerDown:sr(f),children:g.jsx(al.div,{...S,ref:k,style:{position:"absolute",...S.style},onPointerDown:ur(n.onPointerDown,re=>{re.button===0&&(re.target.setPointerCapture(re.pointerId),N.current=P.getBoundingClientRect(),j.current=document.body.style.webkitUserSelect,document.body.style.webkitUserSelect="none",E.viewport&&(E.viewport.style.scrollBehavior="auto"),oe(re))}),onPointerMove:ur(n.onPointerMove,oe),onPointerUp:ur(n.onPointerUp,re=>{const ae=re.target;ae.hasPointerCapture(re.pointerId)&&ae.releasePointerCapture(re.pointerId),document.body.style.webkitUserSelect=j.current,E.viewport&&(E.viewport.style.scrollBehavior=""),N.current=null})})})}),zi="ScrollAreaThumb",rm=h.forwardRef((n,o)=>{const{forceMount:l,...s}=n,a=tm(zi,n.__scopeScrollArea);return g.jsx(ul,{present:l||a.hasThumb,children:g.jsx(Wy,{ref:o,...s})})}),Wy=h.forwardRef((n,o)=>{const{__scopeScrollArea:l,style:s,...a}=n,c=Lt(zi,l),p=tm(zi,l),{onThumbPositionChange:f}=p,m=oo(o,x=>p.onThumbChange(x)),y=h.useRef(void 0),w=Hi(()=>{y.current&&(y.current(),y.current=void 0)},100);return h.useEffect(()=>{const x=c.viewport;if(x){const S=()=>{if(w(),!y.current){const E=By(x,f);y.current=E,f()}};return f(),x.addEventListener("scroll",S),()=>x.removeEventListener("scroll",S)}},[c.viewport,w,f]),g.jsx(al.div,{"data-state":p.hasThumb?"visible":"hidden",...a,ref:m,style:{width:"var(--radix-scroll-area-thumb-width)",height:"var(--radix-scroll-area-thumb-height)",...s},onPointerDownCapture:ur(n.onPointerDownCapture,x=>{const E=x.target.getBoundingClientRect(),P=x.clientX-E.left,C=x.clientY-E.top;p.onThumbPointerDown({x:P,y:C})}),onPointerUp:ur(n.onPointerUp,p.onThumbPointerUp)})});rm.displayName=zi;var Pu="ScrollAreaCorner",om=h.forwardRef((n,o)=>{const l=Lt(Pu,n.__scopeScrollArea),s=!!(l.scrollbarX&&l.scrollbarY);return l.type!=="scroll"&&s?g.jsx(Vy,{...n,ref:o}):null});om.displayName=Pu;var Vy=h.forwardRef((n,o)=>{const{__scopeScrollArea:l,...s}=n,a=Lt(Pu,l),[c,p]=h.useState(0),[f,m]=h.useState(0),y=!!(c&&f);return to(a.scrollbarX,()=>{var x;const w=((x=a.scrollbarX)==null?void 0:x.offsetHeight)||0;a.onCornerHeightChange(w),m(w)}),to(a.scrollbarY,()=>{var x;const w=((x=a.scrollbarY)==null?void 0:x.offsetWidth)||0;a.onCornerWidthChange(w),p(w)}),y?g.jsx(al.div,{...s,ref:o,style:{width:c,height:f,position:"absolute",right:a.dir==="ltr"?0:void 0,left:a.dir==="rtl"?0:void 0,bottom:0,...n.style}}):null});function Ai(n){return n?parseInt(n,10):0}function lm(n,o){const l=n/o;return isNaN(l)?0:l}function Vi(n){const o=lm(n.viewport,n.content),l=n.scrollbar.paddingStart+n.scrollbar.paddingEnd,s=(n.scrollbar.size-l)*o;return Math.max(s,18)}function Hy(n,o,l,s="ltr"){const a=Vi(l),c=a/2,p=o||c,f=a-p,m=l.scrollbar.paddingStart+p,y=l.scrollbar.size-l.scrollbar.paddingEnd-f,w=l.content-l.viewport,x=s==="ltr"?[0,w]:[w*-1,0];return im([m,y],x)(n)}function Yf(n,o,l="ltr"){const s=Vi(o),a=o.scrollbar.paddingStart+o.scrollbar.paddingEnd,c=o.scrollbar.size-a,p=o.content-o.viewport,f=c-s,m=l==="ltr"?[0,p]:[p*-1,0],y=Ay(n,m);return im([0,p],[0,f])(y)}function im(n,o){return l=>{if(n[0]===n[1]||o[0]===o[1])return o[0];const s=(o[1]-o[0])/(n[1]-n[0]);return o[0]+s*(l-n[0])}}function sm(n,o){return n>0&&n<o}var By=(n,o=()=>{})=>{let l={left:n.scrollLeft,top:n.scrollTop},s=0;return function a(){const c={left:n.scrollLeft,top:n.scrollTop},p=l.left!==c.left,f=l.top!==c.top;(p||f)&&o(),l=c,s=window.requestAnimationFrame(a)}(),()=>window.cancelAnimationFrame(s)};function Hi(n,o){const l=sr(n),s=h.useRef(0);return h.useEffect(()=>()=>window.clearTimeout(s.current),[]),h.useCallback(()=>{window.clearTimeout(s.current),s.current=window.setTimeout(l,o)},[l,o])}function to(n,o){const l=sr(o);nl(()=>{let s=0;if(n){const a=new ResizeObserver(()=>{cancelAnimationFrame(s),s=window.requestAnimationFrame(l)});return a.observe(n),()=>{window.cancelAnimationFrame(s),a.unobserve(n)}}},[n,l])}var Uy=Xp,Ky=qp,Qy=om;function am(n){var o,l,s="";if(typeof n=="string"||typeof n=="number")s+=n;else if(typeof n=="object")if(Array.isArray(n)){var a=n.length;for(o=0;o<a;o++)n[o]&&(l=am(n[o]))&&(s&&(s+=" "),s+=l)}else for(l in n)n[l]&&(s&&(s+=" "),s+=l);return s}function um(){for(var n,o,l=0,s="",a=arguments.length;l<a;l++)(n=arguments[l])&&(o=am(n))&&(s&&(s+=" "),s+=o);return s}const Nu="-",Gy=n=>{const o=Xy(n),{conflictingClassGroups:l,conflictingClassGroupModifiers:s}=n;return{getClassGroupId:p=>{const f=p.split(Nu);return f[0]===""&&f.length!==1&&f.shift(),cm(f,o)||Yy(p)},getConflictingClassGroupIds:(p,f)=>{const m=l[p]||[];return f&&s[p]?[...m,...s[p]]:m}}},cm=(n,o)=>{var p;if(n.length===0)return o.classGroupId;const l=n[0],s=o.nextPart.get(l),a=s?cm(n.slice(1),s):void 0;if(a)return a;if(o.validators.length===0)return;const c=n.join(Nu);return(p=o.validators.find(({validator:f})=>f(c)))==null?void 0:p.classGroupId},Xf=/^\[(.+)\]$/,Yy=n=>{if(Xf.test(n)){const o=Xf.exec(n)[1],l=o==null?void 0:o.substring(0,o.indexOf(":"));if(l)return"arbitrary.."+l}},Xy=n=>{const{theme:o,prefix:l}=n,s={nextPart:new Map,validators:[]};return qy(Object.entries(n.classGroups),l).forEach(([c,p])=>{au(p,s,c,o)}),s},au=(n,o,l,s)=>{n.forEach(a=>{if(typeof a=="string"){const c=a===""?o:Zf(o,a);c.classGroupId=l;return}if(typeof a=="function"){if(Zy(a)){au(a(s),o,l,s);return}o.validators.push({validator:a,classGroupId:l});return}Object.entries(a).forEach(([c,p])=>{au(p,Zf(o,c),l,s)})})},Zf=(n,o)=>{let l=n;return o.split(Nu).forEach(s=>{l.nextPart.has(s)||l.nextPart.set(s,{nextPart:new Map,validators:[]}),l=l.nextPart.get(s)}),l},Zy=n=>n.isThemeGetter,qy=(n,o)=>o?n.map(([l,s])=>{const a=s.map(c=>typeof c=="string"?o+c:typeof c=="object"?Object.fromEntries(Object.entries(c).map(([p,f])=>[o+p,f])):c);return[l,a]}):n,Jy=n=>{if(n<1)return{get:()=>{},set:()=>{}};let o=0,l=new Map,s=new Map;const a=(c,p)=>{l.set(c,p),o++,o>n&&(o=0,s=l,l=new Map)};return{get(c){let p=l.get(c);if(p!==void 0)return p;if((p=s.get(c))!==void 0)return a(c,p),p},set(c,p){l.has(c)?l.set(c,p):a(c,p)}}},dm="!",e0=n=>{const{separator:o,experimentalParseClassName:l}=n,s=o.length===1,a=o[0],c=o.length,p=f=>{const m=[];let y=0,w=0,x;for(let k=0;k<f.length;k++){let N=f[k];if(y===0){if(N===a&&(s||f.slice(k,k+c)===o)){m.push(f.slice(w,k)),w=k+c;continue}if(N==="/"){x=k;continue}}N==="["?y++:N==="]"&&y--}const S=m.length===0?f:f.substring(w),E=S.startsWith(dm),P=E?S.substring(1):S,C=x&&x>w?x-w:void 0;return{modifiers:m,hasImportantModifier:E,baseClassName:P,maybePostfixModifierPosition:C}};return l?f=>l({className:f,parseClassName:p}):p},t0=n=>{if(n.length<=1)return n;const o=[];let l=[];return n.forEach(s=>{s[0]==="["?(o.push(...l.sort(),s),l=[]):l.push(s)}),o.push(...l.sort()),o},n0=n=>({cache:Jy(n.cacheSize),parseClassName:e0(n),...Gy(n)}),r0=/\s+/,o0=(n,o)=>{const{parseClassName:l,getClassGroupId:s,getConflictingClassGroupIds:a}=o,c=[],p=n.trim().split(r0);let f="";for(let m=p.length-1;m>=0;m-=1){const y=p[m],{modifiers:w,hasImportantModifier:x,baseClassName:S,maybePostfixModifierPosition:E}=l(y);let P=!!E,C=s(P?S.substring(0,E):S);if(!C){if(!P){f=y+(f.length>0?" "+f:f);continue}if(C=s(S),!C){f=y+(f.length>0?" "+f:f);continue}P=!1}const k=t0(w).join(":"),N=x?k+dm:k,j=N+C;if(c.includes(j))continue;c.push(j);const A=a(C,P);for(let O=0;O<A.length;++O){const I=A[O];c.push(N+I)}f=y+(f.length>0?" "+f:f)}return f};function l0(){let n=0,o,l,s="";for(;n<arguments.length;)(o=arguments[n++])&&(l=fm(o))&&(s&&(s+=" "),s+=l);return s}const fm=n=>{if(typeof n=="string")return n;let o,l="";for(let s=0;s<n.length;s++)n[s]&&(o=fm(n[s]))&&(l&&(l+=" "),l+=o);return l};function i0(n,...o){let l,s,a,c=p;function p(m){const y=o.reduce((w,x)=>x(w),n());return l=n0(y),s=l.cache.get,a=l.cache.set,c=f,f(m)}function f(m){const y=s(m);if(y)return y;const w=o0(m,l);return a(m,w),w}return function(){return c(l0.apply(null,arguments))}}const Ae=n=>{const o=l=>l[n]||[];return o.isThemeGetter=!0,o},pm=/^\[(?:([a-z-]+):)?(.+)\]$/i,s0=/^\d+\/\d+$/,a0=new Set(["px","full","screen"]),u0=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,c0=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,d0=/^(rgba?|hsla?|hwb|(ok)?(lab|lch))\(.+\)$/,f0=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,p0=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,pn=n=>Yr(n)||a0.has(n)||s0.test(n),In=n=>lo(n,"length",S0),Yr=n=>!!n&&!Number.isNaN(Number(n)),Xa=n=>lo(n,"number",Yr),Yo=n=>!!n&&Number.isInteger(Number(n)),m0=n=>n.endsWith("%")&&Yr(n.slice(0,-1)),Ee=n=>pm.test(n),On=n=>u0.test(n),h0=new Set(["length","size","percentage"]),g0=n=>lo(n,h0,mm),v0=n=>lo(n,"position",mm),y0=new Set(["image","url"]),x0=n=>lo(n,y0,E0),w0=n=>lo(n,"",C0),Xo=()=>!0,lo=(n,o,l)=>{const s=pm.exec(n);return s?s[1]?typeof o=="string"?s[1]===o:o.has(s[1]):l(s[2]):!1},S0=n=>c0.test(n)&&!d0.test(n),mm=()=>!1,C0=n=>f0.test(n),E0=n=>p0.test(n),b0=()=>{const n=Ae("colors"),o=Ae("spacing"),l=Ae("blur"),s=Ae("brightness"),a=Ae("borderColor"),c=Ae("borderRadius"),p=Ae("borderSpacing"),f=Ae("borderWidth"),m=Ae("contrast"),y=Ae("grayscale"),w=Ae("hueRotate"),x=Ae("invert"),S=Ae("gap"),E=Ae("gradientColorStops"),P=Ae("gradientColorStopPositions"),C=Ae("inset"),k=Ae("margin"),N=Ae("opacity"),j=Ae("padding"),A=Ae("saturate"),O=Ae("scale"),I=Ae("sepia"),B=Ae("skew"),D=Ae("space"),oe=Ae("translate"),re=()=>["auto","contain","none"],ae=()=>["auto","hidden","clip","visible","scroll"],me=()=>["auto",Ee,o],Z=()=>[Ee,o],ge=()=>["",pn,In],se=()=>["auto",Yr,Ee],ve=()=>["bottom","center","left","left-bottom","left-top","right","right-bottom","right-top","top"],le=()=>["solid","dashed","dotted","double","none"],de=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],$=()=>["start","end","center","between","around","evenly","stretch"],q=()=>["","0",Ee],G=()=>["auto","avoid","all","avoid-page","page","left","right","column"],_=()=>[Yr,Ee];return{cacheSize:500,separator:":",theme:{colors:[Xo],spacing:[pn,In],blur:["none","",On,Ee],brightness:_(),borderColor:[n],borderRadius:["none","","full",On,Ee],borderSpacing:Z(),borderWidth:ge(),contrast:_(),grayscale:q(),hueRotate:_(),invert:q(),gap:Z(),gradientColorStops:[n],gradientColorStopPositions:[m0,In],inset:me(),margin:me(),opacity:_(),padding:Z(),saturate:_(),scale:_(),sepia:q(),skew:_(),space:Z(),translate:Z()},classGroups:{aspect:[{aspect:["auto","square","video",Ee]}],container:["container"],columns:[{columns:[On]}],"break-after":[{"break-after":G()}],"break-before":[{"break-before":G()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:[...ve(),Ee]}],overflow:[{overflow:ae()}],"overflow-x":[{"overflow-x":ae()}],"overflow-y":[{"overflow-y":ae()}],overscroll:[{overscroll:re()}],"overscroll-x":[{"overscroll-x":re()}],"overscroll-y":[{"overscroll-y":re()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:[C]}],"inset-x":[{"inset-x":[C]}],"inset-y":[{"inset-y":[C]}],start:[{start:[C]}],end:[{end:[C]}],top:[{top:[C]}],right:[{right:[C]}],bottom:[{bottom:[C]}],left:[{left:[C]}],visibility:["visible","invisible","collapse"],z:[{z:["auto",Yo,Ee]}],basis:[{basis:me()}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["wrap","wrap-reverse","nowrap"]}],flex:[{flex:["1","auto","initial","none",Ee]}],grow:[{grow:q()}],shrink:[{shrink:q()}],order:[{order:["first","last","none",Yo,Ee]}],"grid-cols":[{"grid-cols":[Xo]}],"col-start-end":[{col:["auto",{span:["full",Yo,Ee]},Ee]}],"col-start":[{"col-start":se()}],"col-end":[{"col-end":se()}],"grid-rows":[{"grid-rows":[Xo]}],"row-start-end":[{row:["auto",{span:[Yo,Ee]},Ee]}],"row-start":[{"row-start":se()}],"row-end":[{"row-end":se()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":["auto","min","max","fr",Ee]}],"auto-rows":[{"auto-rows":["auto","min","max","fr",Ee]}],gap:[{gap:[S]}],"gap-x":[{"gap-x":[S]}],"gap-y":[{"gap-y":[S]}],"justify-content":[{justify:["normal",...$()]}],"justify-items":[{"justify-items":["start","end","center","stretch"]}],"justify-self":[{"justify-self":["auto","start","end","center","stretch"]}],"align-content":[{content:["normal",...$(),"baseline"]}],"align-items":[{items:["start","end","center","baseline","stretch"]}],"align-self":[{self:["auto","start","end","center","stretch","baseline"]}],"place-content":[{"place-content":[...$(),"baseline"]}],"place-items":[{"place-items":["start","end","center","baseline","stretch"]}],"place-self":[{"place-self":["auto","start","end","center","stretch"]}],p:[{p:[j]}],px:[{px:[j]}],py:[{py:[j]}],ps:[{ps:[j]}],pe:[{pe:[j]}],pt:[{pt:[j]}],pr:[{pr:[j]}],pb:[{pb:[j]}],pl:[{pl:[j]}],m:[{m:[k]}],mx:[{mx:[k]}],my:[{my:[k]}],ms:[{ms:[k]}],me:[{me:[k]}],mt:[{mt:[k]}],mr:[{mr:[k]}],mb:[{mb:[k]}],ml:[{ml:[k]}],"space-x":[{"space-x":[D]}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":[D]}],"space-y-reverse":["space-y-reverse"],w:[{w:["auto","min","max","fit","svw","lvw","dvw",Ee,o]}],"min-w":[{"min-w":[Ee,o,"min","max","fit"]}],"max-w":[{"max-w":[Ee,o,"none","full","min","max","fit","prose",{screen:[On]},On]}],h:[{h:[Ee,o,"auto","min","max","fit","svh","lvh","dvh"]}],"min-h":[{"min-h":[Ee,o,"min","max","fit","svh","lvh","dvh"]}],"max-h":[{"max-h":[Ee,o,"min","max","fit","svh","lvh","dvh"]}],size:[{size:[Ee,o,"auto","min","max","fit"]}],"font-size":[{text:["base",On,In]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:["thin","extralight","light","normal","medium","semibold","bold","extrabold","black",Xa]}],"font-family":[{font:[Xo]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:["tighter","tight","normal","wide","wider","widest",Ee]}],"line-clamp":[{"line-clamp":["none",Yr,Xa]}],leading:[{leading:["none","tight","snug","normal","relaxed","loose",pn,Ee]}],"list-image":[{"list-image":["none",Ee]}],"list-style-type":[{list:["none","disc","decimal",Ee]}],"list-style-position":[{list:["inside","outside"]}],"placeholder-color":[{placeholder:[n]}],"placeholder-opacity":[{"placeholder-opacity":[N]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"text-color":[{text:[n]}],"text-opacity":[{"text-opacity":[N]}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...le(),"wavy"]}],"text-decoration-thickness":[{decoration:["auto","from-font",pn,In]}],"underline-offset":[{"underline-offset":["auto",pn,Ee]}],"text-decoration-color":[{decoration:[n]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:Z()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",Ee]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",Ee]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-opacity":[{"bg-opacity":[N]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:[...ve(),v0]}],"bg-repeat":[{bg:["no-repeat",{repeat:["","x","y","round","space"]}]}],"bg-size":[{bg:["auto","cover","contain",g0]}],"bg-image":[{bg:["none",{"gradient-to":["t","tr","r","br","b","bl","l","tl"]},x0]}],"bg-color":[{bg:[n]}],"gradient-from-pos":[{from:[P]}],"gradient-via-pos":[{via:[P]}],"gradient-to-pos":[{to:[P]}],"gradient-from":[{from:[E]}],"gradient-via":[{via:[E]}],"gradient-to":[{to:[E]}],rounded:[{rounded:[c]}],"rounded-s":[{"rounded-s":[c]}],"rounded-e":[{"rounded-e":[c]}],"rounded-t":[{"rounded-t":[c]}],"rounded-r":[{"rounded-r":[c]}],"rounded-b":[{"rounded-b":[c]}],"rounded-l":[{"rounded-l":[c]}],"rounded-ss":[{"rounded-ss":[c]}],"rounded-se":[{"rounded-se":[c]}],"rounded-ee":[{"rounded-ee":[c]}],"rounded-es":[{"rounded-es":[c]}],"rounded-tl":[{"rounded-tl":[c]}],"rounded-tr":[{"rounded-tr":[c]}],"rounded-br":[{"rounded-br":[c]}],"rounded-bl":[{"rounded-bl":[c]}],"border-w":[{border:[f]}],"border-w-x":[{"border-x":[f]}],"border-w-y":[{"border-y":[f]}],"border-w-s":[{"border-s":[f]}],"border-w-e":[{"border-e":[f]}],"border-w-t":[{"border-t":[f]}],"border-w-r":[{"border-r":[f]}],"border-w-b":[{"border-b":[f]}],"border-w-l":[{"border-l":[f]}],"border-opacity":[{"border-opacity":[N]}],"border-style":[{border:[...le(),"hidden"]}],"divide-x":[{"divide-x":[f]}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":[f]}],"divide-y-reverse":["divide-y-reverse"],"divide-opacity":[{"divide-opacity":[N]}],"divide-style":[{divide:le()}],"border-color":[{border:[a]}],"border-color-x":[{"border-x":[a]}],"border-color-y":[{"border-y":[a]}],"border-color-s":[{"border-s":[a]}],"border-color-e":[{"border-e":[a]}],"border-color-t":[{"border-t":[a]}],"border-color-r":[{"border-r":[a]}],"border-color-b":[{"border-b":[a]}],"border-color-l":[{"border-l":[a]}],"divide-color":[{divide:[a]}],"outline-style":[{outline:["",...le()]}],"outline-offset":[{"outline-offset":[pn,Ee]}],"outline-w":[{outline:[pn,In]}],"outline-color":[{outline:[n]}],"ring-w":[{ring:ge()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:[n]}],"ring-opacity":[{"ring-opacity":[N]}],"ring-offset-w":[{"ring-offset":[pn,In]}],"ring-offset-color":[{"ring-offset":[n]}],shadow:[{shadow:["","inner","none",On,w0]}],"shadow-color":[{shadow:[Xo]}],opacity:[{opacity:[N]}],"mix-blend":[{"mix-blend":[...de(),"plus-lighter","plus-darker"]}],"bg-blend":[{"bg-blend":de()}],filter:[{filter:["","none"]}],blur:[{blur:[l]}],brightness:[{brightness:[s]}],contrast:[{contrast:[m]}],"drop-shadow":[{"drop-shadow":["","none",On,Ee]}],grayscale:[{grayscale:[y]}],"hue-rotate":[{"hue-rotate":[w]}],invert:[{invert:[x]}],saturate:[{saturate:[A]}],sepia:[{sepia:[I]}],"backdrop-filter":[{"backdrop-filter":["","none"]}],"backdrop-blur":[{"backdrop-blur":[l]}],"backdrop-brightness":[{"backdrop-brightness":[s]}],"backdrop-contrast":[{"backdrop-contrast":[m]}],"backdrop-grayscale":[{"backdrop-grayscale":[y]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[w]}],"backdrop-invert":[{"backdrop-invert":[x]}],"backdrop-opacity":[{"backdrop-opacity":[N]}],"backdrop-saturate":[{"backdrop-saturate":[A]}],"backdrop-sepia":[{"backdrop-sepia":[I]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":[p]}],"border-spacing-x":[{"border-spacing-x":[p]}],"border-spacing-y":[{"border-spacing-y":[p]}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["none","all","","colors","opacity","shadow","transform",Ee]}],duration:[{duration:_()}],ease:[{ease:["linear","in","out","in-out",Ee]}],delay:[{delay:_()}],animate:[{animate:["none","spin","ping","pulse","bounce",Ee]}],transform:[{transform:["","gpu","none"]}],scale:[{scale:[O]}],"scale-x":[{"scale-x":[O]}],"scale-y":[{"scale-y":[O]}],rotate:[{rotate:[Yo,Ee]}],"translate-x":[{"translate-x":[oe]}],"translate-y":[{"translate-y":[oe]}],"skew-x":[{"skew-x":[B]}],"skew-y":[{"skew-y":[B]}],"transform-origin":[{origin:["center","top","top-right","right","bottom-right","bottom","bottom-left","left","top-left",Ee]}],accent:[{accent:["auto",n]}],appearance:[{appearance:["none","auto"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",Ee]}],"caret-color":[{caret:[n]}],"pointer-events":[{"pointer-events":["none","auto"]}],resize:[{resize:["none","y","x",""]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":Z()}],"scroll-mx":[{"scroll-mx":Z()}],"scroll-my":[{"scroll-my":Z()}],"scroll-ms":[{"scroll-ms":Z()}],"scroll-me":[{"scroll-me":Z()}],"scroll-mt":[{"scroll-mt":Z()}],"scroll-mr":[{"scroll-mr":Z()}],"scroll-mb":[{"scroll-mb":Z()}],"scroll-ml":[{"scroll-ml":Z()}],"scroll-p":[{"scroll-p":Z()}],"scroll-px":[{"scroll-px":Z()}],"scroll-py":[{"scroll-py":Z()}],"scroll-ps":[{"scroll-ps":Z()}],"scroll-pe":[{"scroll-pe":Z()}],"scroll-pt":[{"scroll-pt":Z()}],"scroll-pr":[{"scroll-pr":Z()}],"scroll-pb":[{"scroll-pb":Z()}],"scroll-pl":[{"scroll-pl":Z()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",Ee]}],fill:[{fill:[n,"none"]}],"stroke-w":[{stroke:[pn,In,Xa]}],stroke:[{stroke:[n,"none"]}],sr:["sr-only","not-sr-only"],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]}}},k0=i0(b0);function De(...n){return k0(um(n))}function hm({className:n,children:o,...l}){return g.jsxs(Uy,{"data-slot":"scroll-area",className:De("relative",n),...l,children:[g.jsx(Ky,{"data-slot":"scroll-area-viewport",className:"focus-visible:ring-ring/50 size-full rounded-[inherit] transition-[color,box-shadow] outline-none focus-visible:ring-[3px] focus-visible:outline-1",children:o}),g.jsx(P0,{}),g.jsx(Qy,{})]})}function P0({className:n,orientation:o="vertical",...l}){return g.jsx(Jp,{"data-slot":"scroll-area-scrollbar",orientation:o,className:De("flex touch-none p-px transition-colors select-none",o==="vertical"&&"h-full w-2.5 border-l border-l-transparent",o==="horizontal"&&"h-2.5 flex-col border-t border-t-transparent",n),...l,children:g.jsx(rm,{"data-slot":"scroll-area-thumb",className:"bg-border relative flex-1 rounded-full"})})}function N0(n,o,{checkForDefaultPrevented:l=!0}={}){return function(a){if(n==null||n(a),l===!1||!a.defaultPrevented)return o==null?void 0:o(a)}}function qf(n,o){if(typeof n=="function")return n(o);n!=null&&(n.current=o)}function gm(...n){return o=>{let l=!1;const s=n.map(a=>{const c=qf(a,o);return!l&&typeof c=="function"&&(l=!0),c});if(l)return()=>{for(let a=0;a<s.length;a++){const c=s[a];typeof c=="function"?c():qf(n[a],null)}}}}function vm(...n){return h.useCallback(gm(...n),n)}function R0(n,o=[]){let l=[];function s(c,p){const f=h.createContext(p),m=l.length;l=[...l,p];const y=x=>{var N;const{scope:S,children:E,...P}=x,C=((N=S==null?void 0:S[n])==null?void 0:N[m])||f,k=h.useMemo(()=>P,Object.values(P));return g.jsx(C.Provider,{value:k,children:E})};y.displayName=c+"Provider";function w(x,S){var C;const E=((C=S==null?void 0:S[n])==null?void 0:C[m])||f,P=h.useContext(E);if(P)return P;if(p!==void 0)return p;throw new Error(`\`${x}\` must be used within \`${c}\``)}return[y,w]}const a=()=>{const c=l.map(p=>h.createContext(p));return function(f){const m=(f==null?void 0:f[n])||c;return h.useMemo(()=>({[`__scope${n}`]:{...f,[n]:m}}),[f,m])}};return a.scopeName=n,[s,_0(a,...o)]}function _0(...n){const o=n[0];if(n.length===1)return o;const l=()=>{const s=n.map(a=>({useScope:a(),scopeName:a.scopeName}));return function(c){const p=s.reduce((f,{useScope:m,scopeName:y})=>{const x=m(c)[`__scope${y}`];return{...f,...x}},{});return h.useMemo(()=>({[`__scope${o.scopeName}`]:p}),[p])}};return l.scopeName=o.scopeName,l}var T0=Wi[" useInsertionEffect ".trim().toString()]||nl;function j0({prop:n,defaultProp:o,onChange:l=()=>{},caller:s}){const[a,c,p]=z0({defaultProp:o,onChange:l}),f=n!==void 0,m=f?n:a;{const w=h.useRef(n!==void 0);h.useEffect(()=>{const x=w.current;x!==f&&console.warn(`${s} is changing from ${x?"controlled":"uncontrolled"} to ${f?"controlled":"uncontrolled"}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`),w.current=f},[f,s])}const y=h.useCallback(w=>{var x;if(f){const S=A0(w)?w(n):w;S!==n&&((x=p.current)==null||x.call(p,S))}else c(w)},[f,n,c,p]);return[m,y]}function z0({defaultProp:n,onChange:o}){const[l,s]=h.useState(n),a=h.useRef(l),c=h.useRef(o);return T0(()=>{c.current=o},[o]),h.useEffect(()=>{var p;a.current!==l&&((p=c.current)==null||p.call(c,l),a.current=l)},[l,a]),[l,s,c]}function A0(n){return typeof n=="function"}function L0(n){const o=h.useRef({value:n,previous:n});return h.useMemo(()=>(o.current.value!==n&&(o.current.previous=o.current.value,o.current.value=n),o.current.previous),[n])}function M0(n){const[o,l]=h.useState(void 0);return nl(()=>{if(n){l({width:n.offsetWidth,height:n.offsetHeight});const s=new ResizeObserver(a=>{if(!Array.isArray(a)||!a.length)return;const c=a[0];let p,f;if("borderBoxSize"in c){const m=c.borderBoxSize,y=Array.isArray(m)?m[0]:m;p=y.inlineSize,f=y.blockSize}else p=n.offsetWidth,f=n.offsetHeight;l({width:p,height:f})});return s.observe(n,{box:"border-box"}),()=>s.unobserve(n)}else l(void 0)},[n]),o}function I0(n){const o=O0(n),l=h.forwardRef((s,a)=>{const{children:c,...p}=s,f=h.Children.toArray(c),m=f.find($0);if(m){const y=m.props.children,w=f.map(x=>x===m?h.Children.count(y)>1?h.Children.only(null):h.isValidElement(y)?y.props.children:null:x);return g.jsx(o,{...p,ref:a,children:h.isValidElement(y)?h.cloneElement(y,void 0,w):null})}return g.jsx(o,{...p,ref:a,children:c})});return l.displayName=`${n}.Slot`,l}function O0(n){const o=h.forwardRef((l,s)=>{const{children:a,...c}=l;if(h.isValidElement(a)){const p=W0(a),f=F0(c,a.props);return a.type!==h.Fragment&&(f.ref=s?gm(s,p):p),h.cloneElement(a,f)}return h.Children.count(a)>1?h.Children.only(null):null});return o.displayName=`${n}.SlotClone`,o}var D0=Symbol("radix.slottable");function $0(n){return h.isValidElement(n)&&typeof n.type=="function"&&"__radixId"in n.type&&n.type.__radixId===D0}function F0(n,o){const l={...o};for(const s in o){const a=n[s],c=o[s];/^on[A-Z]/.test(s)?a&&c?l[s]=(...f)=>{const m=c(...f);return a(...f),m}:a&&(l[s]=a):s==="style"?l[s]={...a,...c}:s==="className"&&(l[s]=[a,c].filter(Boolean).join(" "))}return{...n,...l}}function W0(n){var s,a;let o=(s=Object.getOwnPropertyDescriptor(n.props,"ref"))==null?void 0:s.get,l=o&&"isReactWarning"in o&&o.isReactWarning;return l?n.ref:(o=(a=Object.getOwnPropertyDescriptor(n,"ref"))==null?void 0:a.get,l=o&&"isReactWarning"in o&&o.isReactWarning,l?n.props.ref:n.props.ref||n.ref)}var V0=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"],ym=V0.reduce((n,o)=>{const l=I0(`Primitive.${o}`),s=h.forwardRef((a,c)=>{const{asChild:p,...f}=a,m=p?l:o;return typeof window<"u"&&(window[Symbol.for("radix-ui")]=!0),g.jsx(m,{...f,ref:c})});return s.displayName=`Primitive.${o}`,{...n,[o]:s}},{}),Bi="Switch",[H0,tE]=R0(Bi),[B0,U0]=H0(Bi),xm=h.forwardRef((n,o)=>{const{__scopeSwitch:l,name:s,checked:a,defaultChecked:c,required:p,disabled:f,value:m="on",onCheckedChange:y,form:w,...x}=n,[S,E]=h.useState(null),P=vm(o,A=>E(A)),C=h.useRef(!1),k=S?w||!!S.closest("form"):!0,[N,j]=j0({prop:a,defaultProp:c??!1,onChange:y,caller:Bi});return g.jsxs(B0,{scope:l,checked:N,disabled:f,children:[g.jsx(ym.button,{type:"button",role:"switch","aria-checked":N,"aria-required":p,"data-state":Em(N),"data-disabled":f?"":void 0,disabled:f,value:m,...x,ref:P,onClick:N0(n.onClick,A=>{j(O=>!O),k&&(C.current=A.isPropagationStopped(),C.current||A.stopPropagation())})}),k&&g.jsx(Cm,{control:S,bubbles:!C.current,name:s,value:m,checked:N,required:p,disabled:f,form:w,style:{transform:"translateX(-100%)"}})]})});xm.displayName=Bi;var wm="SwitchThumb",Sm=h.forwardRef((n,o)=>{const{__scopeSwitch:l,...s}=n,a=U0(wm,l);return g.jsx(ym.span,{"data-state":Em(a.checked),"data-disabled":a.disabled?"":void 0,...s,ref:o})});Sm.displayName=wm;var K0="SwitchBubbleInput",Cm=h.forwardRef(({__scopeSwitch:n,control:o,checked:l,bubbles:s=!0,...a},c)=>{const p=h.useRef(null),f=vm(p,c),m=L0(l),y=M0(o);return h.useEffect(()=>{const w=p.current;if(!w)return;const x=window.HTMLInputElement.prototype,E=Object.getOwnPropertyDescriptor(x,"checked").set;if(m!==l&&E){const P=new Event("click",{bubbles:s});E.call(w,l),w.dispatchEvent(P)}},[m,l,s]),g.jsx("input",{type:"checkbox","aria-hidden":!0,defaultChecked:l,...a,tabIndex:-1,ref:f,style:{...a.style,...y,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})});Cm.displayName=K0;function Em(n){return n?"checked":"unchecked"}var Q0=xm,G0=Sm;function tl({className:n,...o}){return g.jsx(Q0,{"data-slot":"switch",className:De("peer inline-flex h-[1.15rem] w-8 shrink-0 items-center rounded-full transition-all outline-none disabled:cursor-not-allowed disabled:opacity-50","data-[state=unchecked]:bg-gray-200 data-[state=unchecked]:border-2 data-[state=unchecked]:border-gray-300 data-[state=unchecked]:shadow-inner","dark:data-[state=unchecked]:bg-gray-700 dark:data-[state=unchecked]:border-gray-600","data-[state=checked]:bg-primary data-[state=checked]:border data-[state=checked]:border-primary-foreground/20","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]",n),...o,children:g.jsx(G0,{"data-slot":"switch-thumb",className:De("pointer-events-none block size-4 rounded-full ring-0 transition-transform shadow-sm border border-gray-300","data-[state=unchecked]:translate-x-0 data-[state=unchecked]:bg-white data-[state=unchecked]:shadow-md","dark:data-[state=unchecked]:bg-gray-200 dark:data-[state=unchecked]:border-gray-500","data-[state=checked]:translate-x-4 data-[state=checked]:bg-white data-[state=checked]:border-gray-400","dark:data-[state=checked]:bg-primary-foreground dark:data-[state=checked]:border-gray-300")})})}function Jf(n,o){if(typeof n=="function")return n(o);n!=null&&(n.current=o)}function Y0(...n){return o=>{let l=!1;const s=n.map(a=>{const c=Jf(a,o);return!l&&typeof c=="function"&&(l=!0),c});if(l)return()=>{for(let a=0;a<s.length;a++){const c=s[a];typeof c=="function"?c():Jf(n[a],null)}}}}function X0(n){const o=Z0(n),l=h.forwardRef((s,a)=>{const{children:c,...p}=s,f=h.Children.toArray(c),m=f.find(J0);if(m){const y=m.props.children,w=f.map(x=>x===m?h.Children.count(y)>1?h.Children.only(null):h.isValidElement(y)?y.props.children:null:x);return g.jsx(o,{...p,ref:a,children:h.isValidElement(y)?h.cloneElement(y,void 0,w):null})}return g.jsx(o,{...p,ref:a,children:c})});return l.displayName=`${n}.Slot`,l}function Z0(n){const o=h.forwardRef((l,s)=>{const{children:a,...c}=l;if(h.isValidElement(a)){const p=tx(a),f=ex(c,a.props);return a.type!==h.Fragment&&(f.ref=s?Y0(s,p):p),h.cloneElement(a,f)}return h.Children.count(a)>1?h.Children.only(null):null});return o.displayName=`${n}.SlotClone`,o}var q0=Symbol("radix.slottable");function J0(n){return h.isValidElement(n)&&typeof n.type=="function"&&"__radixId"in n.type&&n.type.__radixId===q0}function ex(n,o){const l={...o};for(const s in o){const a=n[s],c=o[s];/^on[A-Z]/.test(s)?a&&c?l[s]=(...f)=>{const m=c(...f);return a(...f),m}:a&&(l[s]=a):s==="style"?l[s]={...a,...c}:s==="className"&&(l[s]=[a,c].filter(Boolean).join(" "))}return{...n,...l}}function tx(n){var s,a;let o=(s=Object.getOwnPropertyDescriptor(n.props,"ref"))==null?void 0:s.get,l=o&&"isReactWarning"in o&&o.isReactWarning;return l?n.ref:(o=(a=Object.getOwnPropertyDescriptor(n,"ref"))==null?void 0:a.get,l=o&&"isReactWarning"in o&&o.isReactWarning,l?n.props.ref:n.props.ref||n.ref)}var nx=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"],rx=nx.reduce((n,o)=>{const l=X0(`Primitive.${o}`),s=h.forwardRef((a,c)=>{const{asChild:p,...f}=a,m=p?l:o;return typeof window<"u"&&(window[Symbol.for("radix-ui")]=!0),g.jsx(m,{...f,ref:c})});return s.displayName=`Primitive.${o}`,{...n,[o]:s}},{}),ox="Label",bm=h.forwardRef((n,o)=>g.jsx(rx.label,{...n,ref:o,onMouseDown:l=>{var a;l.target.closest("button, input, select, textarea")||((a=n.onMouseDown)==null||a.call(n,l),!l.defaultPrevented&&l.detail>1&&l.preventDefault())}}));bm.displayName=ox;var km=bm;const ep=n=>typeof n=="boolean"?`${n}`:n===0?"0":n,tp=um,Ru=(n,o)=>l=>{var s;if((o==null?void 0:o.variants)==null)return tp(n,l==null?void 0:l.class,l==null?void 0:l.className);const{variants:a,defaultVariants:c}=o,p=Object.keys(a).map(y=>{const w=l==null?void 0:l[y],x=c==null?void 0:c[y];if(w===null)return null;const S=ep(w)||ep(x);return a[y][S]}),f=l&&Object.entries(l).reduce((y,w)=>{let[x,S]=w;return S===void 0||(y[x]=S),y},{}),m=o==null||(s=o.compoundVariants)===null||s===void 0?void 0:s.reduce((y,w)=>{let{class:x,className:S,...E}=w;return Object.entries(E).every(P=>{let[C,k]=P;return Array.isArray(k)?k.includes({...c,...f}[C]):{...c,...f}[C]===k})?[...y,x,S]:y},[]);return tp(n,p,m,l==null?void 0:l.class,l==null?void 0:l.className)},lx=Ru("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),We=h.forwardRef(({className:n,...o},l)=>g.jsx(km,{"data-slot":"label",ref:l,className:De(lx(),n),...o}));We.displayName=km.displayName;function np(n,o){if(typeof n=="function")return n(o);n!=null&&(n.current=o)}function Pm(...n){return o=>{let l=!1;const s=n.map(a=>{const c=np(a,o);return!l&&typeof c=="function"&&(l=!0),c});if(l)return()=>{for(let a=0;a<s.length;a++){const c=s[a];typeof c=="function"?c():np(n[a],null)}}}}function et(...n){return h.useCallback(Pm(...n),n)}var fr=h.forwardRef((n,o)=>{const{children:l,...s}=n,a=h.Children.toArray(l),c=a.find(sx);if(c){const p=c.props.children,f=a.map(m=>m===c?h.Children.count(p)>1?h.Children.only(null):h.isValidElement(p)?p.props.children:null:m);return g.jsx(uu,{...s,ref:o,children:h.isValidElement(p)?h.cloneElement(p,void 0,f):null})}return g.jsx(uu,{...s,ref:o,children:l})});fr.displayName="Slot";var uu=h.forwardRef((n,o)=>{const{children:l,...s}=n;if(h.isValidElement(l)){const a=ux(l),c=ax(s,l.props);return l.type!==h.Fragment&&(c.ref=o?Pm(o,a):a),h.cloneElement(l,c)}return h.Children.count(l)>1?h.Children.only(null):null});uu.displayName="SlotClone";var ix=({children:n})=>g.jsx(g.Fragment,{children:n});function sx(n){return h.isValidElement(n)&&n.type===ix}function ax(n,o){const l={...o};for(const s in o){const a=n[s],c=o[s];/^on[A-Z]/.test(s)?a&&c?l[s]=(...f)=>{c(...f),a(...f)}:a&&(l[s]=a):s==="style"?l[s]={...a,...c}:s==="className"&&(l[s]=[a,c].filter(Boolean).join(" "))}return{...n,...l}}function ux(n){var s,a;let o=(s=Object.getOwnPropertyDescriptor(n.props,"ref"))==null?void 0:s.get,l=o&&"isReactWarning"in o&&o.isReactWarning;return l?n.ref:(o=(a=Object.getOwnPropertyDescriptor(n,"ref"))==null?void 0:a.get,l=o&&"isReactWarning"in o&&o.isReactWarning,l?n.props.ref:n.props.ref||n.ref)}const cx=Ru("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-white hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9 rounded-md"}},defaultVariants:{variant:"default",size:"default"}});function zt({className:n,variant:o,size:l,asChild:s=!1,...a}){const c=s?fr:"button";return g.jsx(c,{"data-slot":"button",className:De(cx({variant:o,size:l,className:n})),...a})}/**
 * @license lucide-react v0.487.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const dx=n=>n.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),fx=n=>n.replace(/^([A-Z])|[\s-_]+(\w)/g,(o,l,s)=>s?s.toUpperCase():l.toLowerCase()),rp=n=>{const o=fx(n);return o.charAt(0).toUpperCase()+o.slice(1)},Nm=(...n)=>n.filter((o,l,s)=>!!o&&o.trim()!==""&&s.indexOf(o)===l).join(" ").trim();/**
 * @license lucide-react v0.487.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var px={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.487.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const mx=h.forwardRef(({color:n="currentColor",size:o=24,strokeWidth:l=2,absoluteStrokeWidth:s,className:a="",children:c,iconNode:p,...f},m)=>h.createElement("svg",{ref:m,...px,width:o,height:o,stroke:n,strokeWidth:s?Number(l)*24/Number(o):l,className:Nm("lucide",a),...f},[...p.map(([y,w])=>h.createElement(y,w)),...Array.isArray(c)?c:[c]]));/**
 * @license lucide-react v0.487.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const it=(n,o)=>{const l=h.forwardRef(({className:s,...a},c)=>h.createElement(mx,{ref:c,iconNode:o,className:Nm(`lucide-${dx(rp(n))}`,`lucide-${n}`,s),...a}));return l.displayName=rp(n),l};/**
 * @license lucide-react v0.487.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const hx=[["path",{d:"m8 2 1.88 1.88",key:"fmnt4t"}],["path",{d:"M14.12 3.88 16 2",key:"qol33r"}],["path",{d:"M9 7.13v-1a3.003 3.003 0 1 1 6 0v1",key:"d7y7pr"}],["path",{d:"M12 20c-3.3 0-6-2.7-6-6v-3a4 4 0 0 1 4-4h4a4 4 0 0 1 4 4v3c0 3.3-2.7 6-6 6",key:"xs1cw7"}],["path",{d:"M12 20v-9",key:"1qisl0"}],["path",{d:"M6.53 9C4.6 8.8 3 7.1 3 5",key:"32zzws"}],["path",{d:"M6 13H2",key:"82j7cp"}],["path",{d:"M3 21c0-2.1 1.7-3.9 3.8-4",key:"4p0ekp"}],["path",{d:"M20.97 5c0 2.1-1.6 3.8-3.5 4",key:"18gb23"}],["path",{d:"M22 13h-4",key:"1jl80f"}],["path",{d:"M17.2 17c2.1.1 3.8 1.9 3.8 4",key:"k3fwyw"}]],gx=it("bug",hx);/**
 * @license lucide-react v0.487.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const vx=[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]],yx=it("check",vx);/**
 * @license lucide-react v0.487.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const xx=[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]],_u=it("chevron-down",xx);/**
 * @license lucide-react v0.487.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const wx=[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]],Sx=it("chevron-up",wx);/**
 * @license lucide-react v0.487.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Cx=[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]],Rm=it("clock",Cx);/**
 * @license lucide-react v0.487.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ex=[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]],bx=it("file-text",Ex);/**
 * @license lucide-react v0.487.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const kx=[["circle",{cx:"9",cy:"12",r:"1",key:"1vctgf"}],["circle",{cx:"9",cy:"5",r:"1",key:"hp0tcf"}],["circle",{cx:"9",cy:"19",r:"1",key:"fkjjf6"}],["circle",{cx:"15",cy:"12",r:"1",key:"1tmaij"}],["circle",{cx:"15",cy:"5",r:"1",key:"19l28e"}],["circle",{cx:"15",cy:"19",r:"1",key:"f4zoj3"}]],Px=it("grip-vertical",kx);/**
 * @license lucide-react v0.487.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Nx=[["path",{d:"m5 8 6 6",key:"1wu5hv"}],["path",{d:"m4 14 6-6 2-3",key:"1k1g8d"}],["path",{d:"M2 5h12",key:"or177f"}],["path",{d:"M7 2h1",key:"1t2jsx"}],["path",{d:"m22 22-5-10-5 10",key:"don7ne"}],["path",{d:"M14 18h6",key:"1m8k6r"}]],cu=it("languages",Nx);/**
 * @license lucide-react v0.487.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Rx=[["path",{d:"M15 14c.2-1 .7-1.7 1.5-2.5 1-.9 1.5-2.2 1.5-3.5A6 6 0 0 0 6 8c0 1 .2 2.2 1.5 ******* 1.3 1.5 1.5 2.5",key:"1gvzjb"}],["path",{d:"M9 18h6",key:"x1upvd"}],["path",{d:"M10 22h4",key:"ceow96"}]],_x=it("lightbulb",Rx);/**
 * @license lucide-react v0.487.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Tx=[["path",{d:"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z",key:"1lielz"}]],jx=it("message-square",Tx);/**
 * @license lucide-react v0.487.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const zx=[["line",{x1:"2",x2:"22",y1:"2",y2:"22",key:"a6p6uj"}],["path",{d:"M18.89 13.23A7.12 7.12 0 0 0 19 12v-2",key:"80xlxr"}],["path",{d:"M5 10v2a7 7 0 0 0 12 5",key:"p2k8kg"}],["path",{d:"M15 9.34V5a3 3 0 0 0-5.68-1.33",key:"1gzdoj"}],["path",{d:"M9 9v3a3 3 0 0 0 5.12 2.12",key:"r2i35w"}],["line",{x1:"12",x2:"12",y1:"19",y2:"22",key:"x3vr5v"}]],Ax=it("mic-off",zx);/**
 * @license lucide-react v0.487.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Lx=[["path",{d:"M12 2a3 3 0 0 0-3 3v7a3 3 0 0 0 6 0V5a3 3 0 0 0-3-3Z",key:"131961"}],["path",{d:"M19 10v2a7 7 0 0 1-14 0v-2",key:"1vc78b"}],["line",{x1:"12",x2:"12",y1:"19",y2:"22",key:"x3vr5v"}]],Mx=it("mic",Lx);/**
 * @license lucide-react v0.487.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ix=[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]],_m=it("plus",Ix);/**
 * @license lucide-react v0.487.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ox=[["path",{d:"M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8",key:"1357e3"}],["path",{d:"M3 3v5h5",key:"1xhq8a"}]],Dx=it("rotate-ccw",Ox);/**
 * @license lucide-react v0.487.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const $x=[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]],Tm=it("trash-2",$x);/**
 * @license lucide-react v0.487.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Fx=[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]],Wx=it("x",Fx);function Vx({id:n,timestamp:o,originalText:l,translatedText:s,originalLang:a,targetLang:c,confidence:p,isDebugLatest:f=!1,onPlay:m}){const y={high:"border-l-green-500 bg-green-50",medium:"border-l-yellow-500 bg-yellow-50",low:"border-l-orange-500 bg-orange-50"},w=!l.trim()&&!s.trim(),x=n.startsWith("debug-"),S=a.toLowerCase()===c.toLowerCase(),E=()=>x?f?"border-l-yellow-500 bg-yellow-100":"border-l-green-500 bg-green-100":y[p];return g.jsxs("div",{className:`p-4 mb-4 border-l-4 rounded-lg ${E()} hover:shadow-sm transition-shadow cursor-pointer ${w?"opacity-60":""}`,onClick:()=>m==null?void 0:m(n),children:[g.jsx("div",{className:"flex items-center justify-between mb-3",children:g.jsxs("div",{className:"flex items-center gap-2",children:[g.jsx(Rm,{size:14,className:"text-muted-foreground"}),g.jsx("span",{className:"text-xs text-muted-foreground",children:o}),x&&g.jsx("span",{className:`text-xs px-1.5 py-0.5 rounded text-center ${f?"bg-yellow-200 text-yellow-800":"bg-green-200 text-green-800"}`,children:f?"DEBUG (최신)":"DEBUG"})]})}),g.jsx("div",{className:"space-y-2",children:S?g.jsxs("div",{children:[g.jsxs("div",{className:"flex items-center gap-2 mb-1.5",children:[g.jsx("span",{className:"text-xs px-2 py-1 bg-blue-100 text-blue-800 rounded uppercase",children:a}),g.jsx("span",{className:"text-xs text-muted-foreground",children:"원문"})]}),g.jsx("p",{className:"leading-relaxed",children:l||g.jsx("span",{className:"text-muted-foreground italic",children:"음성 인식 대기 중..."})})]}):g.jsxs(g.Fragment,{children:[g.jsxs("div",{children:[g.jsxs("div",{className:"flex items-center gap-2 mb-1.5",children:[g.jsx("span",{className:"text-xs px-2 py-1 bg-blue-100 text-blue-800 rounded uppercase",children:a}),g.jsx("span",{className:"text-xs text-muted-foreground",children:"원문"})]}),g.jsx("p",{className:"text-sm text-gray-700 leading-relaxed mb-2",children:l||g.jsx("span",{className:"text-muted-foreground italic",children:"음성 인식 대기 중..."})})]}),g.jsxs("div",{children:[g.jsxs("div",{className:"flex items-center gap-2 mb-1.5",children:[g.jsx("span",{className:"text-xs px-2 py-1 bg-green-100 text-green-800 rounded uppercase",children:c}),g.jsx("span",{className:"text-xs text-muted-foreground",children:"번역"})]}),g.jsx("p",{className:"leading-relaxed",children:s||g.jsx("span",{className:"text-muted-foreground italic",children:"번역 대기 중..."})})]})]})})]})}function op({translations:n,isSummaryPanelVisible:o,onSummaryPanelToggle:l,isQuestionHelperVisible:s,onQuestionHelperToggle:a,scrollToBottom:c,onScrolledToBottom:p}){const f=h.useRef(null),m=h.useRef(null),[y,w]=h.useState(!1),x=P=>{const C=n.find(k=>k.id===P);C&&console.log(`Playing text: ${C.originalText} in language: ${C.originalLang}`)},S=h.useCallback(()=>{if(!m.current)return;let P=null,C=m.current.parentElement,k=0;for(;C&&k<5;){if(C.scrollHeight>C.clientHeight){P=C;break}C=C.parentElement,k++}if(P){const{scrollTop:N,scrollHeight:j,clientHeight:A}=P,O=N+A>=j-10,B=j>A&&!O;w(B)}else w(!1)},[]),E=h.useCallback(()=>{f.current&&f.current.scrollIntoView({behavior:"smooth",block:"end"})},[]);return h.useEffect(()=>{c&&f.current&&(f.current.scrollIntoView({behavior:"smooth",block:"end"}),setTimeout(()=>{p==null||p()},500))},[c,p]),h.useEffect(()=>{const P=setInterval(()=>{S()},1e3);return S(),()=>{clearInterval(P)}},[S]),h.useEffect(()=>{const P=setTimeout(S,100);return()=>clearTimeout(P)},[n,S]),g.jsxs("div",{className:"flex-1 pt-4 px-4 flex flex-col h-full relative",children:[g.jsx("div",{className:"mb-4 shrink-0",children:g.jsxs("div",{className:"flex items-start justify-between",children:[g.jsxs("div",{children:[g.jsx("h2",{className:"text-lg mb-1",children:"실시간 통역"}),g.jsx("p",{className:"text-sm text-muted-foreground",children:"음성이 실시간으로 문단 단위로 번역되어 표시됩니다"})]}),g.jsx("div",{className:"bg-gray-50 rounded-lg p-3 border border-gray-200 shadow-sm",children:g.jsxs("div",{className:"flex flex-col gap-2.5",children:[g.jsxs("div",{className:"flex items-center gap-3",children:[g.jsx(We,{className:"text-xs text-muted-foreground w-20 text-right",children:"요약 패널:"}),g.jsxs("div",{className:"flex items-center gap-1",children:[g.jsx(We,{className:"text-xs text-muted-foreground",children:"숨김"}),g.jsx(tl,{checked:o,onCheckedChange:l,className:"data-[state=checked]:bg-blue-600 scale-75"}),g.jsx(We,{className:"text-xs text-muted-foreground",children:"표시"})]})]}),g.jsxs("div",{className:"flex items-center gap-3",children:[g.jsx(We,{className:"text-xs text-muted-foreground w-20 text-right",children:"질문 도우미:"}),g.jsxs("div",{className:"flex items-center gap-1",children:[g.jsx(We,{className:"text-xs text-muted-foreground",children:"숨김"}),g.jsx(tl,{checked:s,onCheckedChange:a,className:"data-[state=checked]:bg-purple-600 scale-75"}),g.jsx(We,{className:"text-xs text-muted-foreground",children:"표시"})]})]})]})})]})}),g.jsx(hm,{className:"flex-1 min-h-0",children:g.jsx("div",{ref:m,className:"space-y-4 pr-2",children:n.length===0?g.jsxs("div",{className:"text-center py-12 text-muted-foreground",children:[g.jsx("p",{children:"마이크를 켜고 대화를 시작해주세요"}),g.jsx("p",{className:"text-xs mt-1",children:"문단 단위로 통역 결과가 표시됩니다"})]}):g.jsxs(g.Fragment,{children:[n.map(P=>g.jsx(Vx,{...P,onPlay:x},P.id)),g.jsx("div",{ref:f})]})})}),y&&g.jsx(zt,{onClick:E,className:"absolute bottom-4 right-4 h-10 w-10 rounded-full shadow-lg bg-blue-600/80 hover:bg-blue-700/90 text-white border-0 transition-all duration-200 hover:scale-105 z-50 backdrop-blur-sm",size:"icon",title:"맨 아래로 스크롤",children:g.jsx(_u,{className:"h-4 w-4"})})]})}function Tu({className:n,...o}){return g.jsx("div",{"data-slot":"card",className:De("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border",n),...o})}function Hx({className:n,...o}){return g.jsx("div",{"data-slot":"card-header",className:De("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 pt-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",n),...o})}function Bx({className:n,...o}){return g.jsx("h4",{"data-slot":"card-title",className:De("leading-none",n),...o})}function ju({className:n,...o}){return g.jsx("div",{"data-slot":"card-content",className:De("px-6 [&:last-child]:pb-6",n),...o})}const Ux=Ru("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function du({className:n,variant:o,asChild:l=!1,...s}){const a=l?fr:"span";return g.jsx(a,{"data-slot":"badge",className:De(Ux({variant:o}),n),...s})}const Xr=h.forwardRef(({className:n,type:o,...l},s)=>g.jsx("input",{type:o,"data-slot":"input",className:De("border-input bg-input-background focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive flex h-9 w-full rounded-md border px-3 py-1 text-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50",n),ref:s,...l}));Xr.displayName="Input";function Kx({className:n,...o}){return g.jsx("textarea",{"data-slot":"textarea",className:De("resize-none border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-input-background px-3 py-2 text-base transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",n),...o})}function lp(n,o){if(typeof n=="function")return n(o);n!=null&&(n.current=o)}function Qx(...n){return o=>{let l=!1;const s=n.map(a=>{const c=lp(a,o);return!l&&typeof c=="function"&&(l=!0),c});if(l)return()=>{for(let a=0;a<s.length;a++){const c=s[a];typeof c=="function"?c():lp(n[a],null)}}}}function Gx(n){const o=Yx(n),l=h.forwardRef((s,a)=>{const{children:c,...p}=s,f=h.Children.toArray(c),m=f.find(Zx);if(m){const y=m.props.children,w=f.map(x=>x===m?h.Children.count(y)>1?h.Children.only(null):h.isValidElement(y)?y.props.children:null:x);return g.jsx(o,{...p,ref:a,children:h.isValidElement(y)?h.cloneElement(y,void 0,w):null})}return g.jsx(o,{...p,ref:a,children:c})});return l.displayName=`${n}.Slot`,l}function Yx(n){const o=h.forwardRef((l,s)=>{const{children:a,...c}=l;if(h.isValidElement(a)){const p=Jx(a),f=qx(c,a.props);return a.type!==h.Fragment&&(f.ref=s?Qx(s,p):p),h.cloneElement(a,f)}return h.Children.count(a)>1?h.Children.only(null):null});return o.displayName=`${n}.SlotClone`,o}var Xx=Symbol("radix.slottable");function Zx(n){return h.isValidElement(n)&&typeof n.type=="function"&&"__radixId"in n.type&&n.type.__radixId===Xx}function qx(n,o){const l={...o};for(const s in o){const a=n[s],c=o[s];/^on[A-Z]/.test(s)?a&&c?l[s]=(...f)=>{const m=c(...f);return a(...f),m}:a&&(l[s]=a):s==="style"?l[s]={...a,...c}:s==="className"&&(l[s]=[a,c].filter(Boolean).join(" "))}return{...n,...l}}function Jx(n){var s,a;let o=(s=Object.getOwnPropertyDescriptor(n.props,"ref"))==null?void 0:s.get,l=o&&"isReactWarning"in o&&o.isReactWarning;return l?n.ref:(o=(a=Object.getOwnPropertyDescriptor(n,"ref"))==null?void 0:a.get,l=o&&"isReactWarning"in o&&o.isReactWarning,l?n.props.ref:n.props.ref||n.ref)}var ew=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"],tw=ew.reduce((n,o)=>{const l=Gx(`Primitive.${o}`),s=h.forwardRef((a,c)=>{const{asChild:p,...f}=a,m=p?l:o;return typeof window<"u"&&(window[Symbol.for("radix-ui")]=!0),g.jsx(m,{...f,ref:c})});return s.displayName=`Primitive.${o}`,{...n,[o]:s}},{}),nw="Separator",ip="horizontal",rw=["horizontal","vertical"],jm=h.forwardRef((n,o)=>{const{decorative:l,orientation:s=ip,...a}=n,c=ow(s)?s:ip,f=l?{role:"none"}:{"aria-orientation":c==="vertical"?c:void 0,role:"separator"};return g.jsx(tw.div,{"data-orientation":c,...f,...a,ref:o})});jm.displayName=nw;function ow(n){return rw.includes(n)}var lw=jm;function iw({className:n,orientation:o="horizontal",decorative:l=!0,...s}){return g.jsx(lw,{"data-slot":"separator-root",decorative:l,orientation:o,className:De("bg-border shrink-0 data-[orientation=horizontal]:h-px data-[orientation=horizontal]:w-full data-[orientation=vertical]:h-full data-[orientation=vertical]:w-px",n),...s})}function sw({currentTopic:n,onTopicChange:o,onClearAllSummaries:l,onAddSummary:s}){const[a,c]=h.useState(n),[p,f]=h.useState(""),[m,y]=h.useState(""),[w,x]=h.useState(""),S=()=>{a.trim()?(o(a.trim()),console.log("현재 주제가 적용되었습니다:",a.trim())):console.log("주제 적용 실패: 빈 텍스트입니다.")},E=N=>{N.key==="Enter"&&S()},P=()=>{l&&(l(),console.log("모든 구간별 요약이 삭제되었습니다."))},C=()=>{if(!p.trim()||!m.trim()||!w.trim()){console.log("요약 추가 실패: 모든 필드를 입력해주세요.");return}const N=w.split(`
`).map(j=>j.trim()).filter(j=>j!=="");s&&(s(p.trim(),m.trim(),N),f(""),y(""),x(""),console.log("새 구간별 요약이 추가되었습니다."))},k=p.trim()&&m.trim()&&w.trim();return g.jsx("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-3 shadow-sm",children:g.jsxs("div",{className:"space-y-4",children:[g.jsx("div",{className:"text-center",children:g.jsx(We,{className:"text-sm text-blue-700",children:"요약패널 디버그"})}),g.jsxs("div",{className:"space-y-2",children:[g.jsx(We,{className:"text-xs text-blue-600",children:"현재 주제 편집:"}),g.jsx(Xr,{value:a,onChange:N=>c(N.target.value),onKeyPress:E,placeholder:"새로운 주제를 입력하세요...",className:"text-sm bg-white border-blue-200 focus:border-blue-400"}),g.jsx("div",{className:"flex justify-center",children:g.jsx(zt,{onClick:S,size:"sm",className:"bg-blue-600 hover:bg-blue-700 text-white px-4 py-1",children:"현재 주제 적용"})})]}),g.jsx(iw,{className:"bg-blue-200"}),g.jsxs("div",{className:"space-y-3",children:[g.jsx(We,{className:"text-xs text-blue-600",children:"구간별 요약 관리:"}),g.jsx("div",{className:"flex justify-center",children:g.jsxs(zt,{onClick:P,variant:"destructive",size:"sm",className:"px-3 py-1",children:[g.jsx(Tm,{size:14,className:"mr-1"}),"구간별 요약 전체 지우기"]})}),g.jsxs("div",{className:"space-y-2",children:[g.jsx(We,{className:"text-xs text-blue-600",children:"새 구간별 요약 추가:"}),g.jsxs("div",{className:"space-y-1",children:[g.jsx(We,{className:"text-xs text-gray-600",children:"시간 범위:"}),g.jsx(Xr,{value:p,onChange:N=>f(N.target.value),placeholder:"예: 10:50-10:55",className:"text-sm bg-white border-blue-200 focus:border-blue-400"})]}),g.jsxs("div",{className:"space-y-1",children:[g.jsx(We,{className:"text-xs text-gray-600",children:"요약 주제:"}),g.jsx(Xr,{value:m,onChange:N=>y(N.target.value),placeholder:"요약 내용을 입력하세요...",className:"text-sm bg-white border-blue-200 focus:border-blue-400"})]}),g.jsxs("div",{className:"space-y-1",children:[g.jsx(We,{className:"text-xs text-gray-600",children:"주요 포인트 (각 줄마다 하나씩):"}),g.jsx(Kx,{value:w,onChange:N=>x(N.target.value),placeholder:`주요 포인트를 입력하세요...
각 줄마다 하나의 포인트를 입력하면 됩니다`,className:"text-sm bg-white border-blue-200 focus:border-blue-400 h-16 resize-none"})]}),g.jsx("div",{className:"flex justify-center pt-2",children:g.jsxs(zt,{onClick:C,disabled:!k,size:"sm",className:"bg-green-600 hover:bg-green-700 disabled:bg-gray-400 text-white px-4 py-1",children:[g.jsx(_m,{size:14,className:"mr-1"}),"구간별 요약 추가"]})})]})]})]})})}function aw({currentTopic:n,summaries:o,isSummaryDebugMode:l=!1,onTopicChange:s,onClearAllSummaries:a,onAddSummary:c,summaryScrollToBottom:p=!1,onSummaryScrolledToBottom:f}){const m=h.useRef(null),y=h.useRef(null);return h.useEffect(()=>{p&&setTimeout(()=>{let w=!1;if(y.current){const x=y.current.querySelector("[data-radix-scroll-area-viewport]");x&&(x.scrollTop=x.scrollHeight,w=!0,console.log("스크롤 실행됨 (방법 1): viewport.scrollHeight =",x.scrollHeight))}if(!w&&m.current){const x=m.current.closest("[data-radix-scroll-area-viewport]");x&&(x.scrollTop=x.scrollHeight,w=!0,console.log("스크롤 실행됨 (방법 2): scrollContainer.scrollHeight =",x.scrollHeight))}if(!w&&m.current){const x=m.current.parentElement;x&&x.hasAttribute("data-radix-scroll-area-viewport")&&(x.scrollTop=x.scrollHeight,w=!0,console.log("스크롤 실행됨 (방법 3): parent.scrollHeight =",x.scrollHeight))}w||console.log("스크롤 실행 실패: viewport를 찾을 수 없음"),f==null||f()},50)},[p,o.length,f]),g.jsxs("div",{className:"h-full flex flex-col bg-white border-l",children:[g.jsx("div",{className:"p-4 border-b bg-blue-50 shrink-0",children:g.jsxs("div",{className:"flex items-start gap-2",children:[g.jsx(jx,{size:16,className:"text-blue-600 mt-1 shrink-0"}),g.jsxs("div",{children:[g.jsx("h3",{className:"text-sm mb-1",children:"현재 주제"}),g.jsx("p",{className:"text-sm text-blue-800",children:n})]})]})}),g.jsx("div",{className:"p-4 border-b shrink-0",children:g.jsxs("div",{className:"flex items-center gap-2",children:[g.jsx(Rm,{size:16,className:"text-gray-600"}),g.jsx("h3",{className:"text-sm",children:"구간별 요약"})]})}),g.jsx("div",{className:`flex-1 min-h-0 ${l?"overflow-hidden":""}`,children:g.jsx("div",{ref:y,className:"h-full",children:g.jsx(hm,{className:"h-full",children:g.jsx("div",{ref:m,className:"p-4 space-y-3",children:o.map(w=>g.jsxs(Tu,{className:"shadow-sm",children:[g.jsx(Hx,{className:"pb-1",children:g.jsx(Bx,{className:"text-sm flex items-center gap-2",children:g.jsx(du,{variant:"outline",className:"text-xs",children:w.timeRange})})}),g.jsxs(ju,{className:"pt-1",children:[g.jsx("p",{className:"text-sm text-gray-800 mb-2",children:w.summary}),g.jsxs("div",{className:"space-y-1",children:[g.jsx("p",{className:"text-xs text-gray-600",children:"주요 포인트:"}),g.jsx("ul",{className:"space-y-0.5",children:w.keyPoints.map((x,S)=>g.jsxs("li",{className:"text-xs text-gray-700 flex items-start gap-1",children:[g.jsx("span",{className:"text-blue-500 mt-1",children:"•"}),g.jsx("span",{children:x})]},S))})]})]})]},w.id))})})})}),l&&s&&g.jsx("div",{className:"shrink-0 border-t bg-white animate-in slide-in-from-bottom-2 duration-300 max-h-[50vh] overflow-y-auto",children:g.jsx("div",{className:"p-4",children:g.jsx(sw,{currentTopic:n,onTopicChange:s,onClearAllSummaries:a,onAddSummary:c})})})]})}function sp(n,[o,l]){return Math.min(l,Math.max(o,n))}function He(n,o,{checkForDefaultPrevented:l=!0}={}){return function(a){if(n==null||n(a),l===!1||!a.defaultPrevented)return o==null?void 0:o(a)}}function zu(n,o=[]){let l=[];function s(c,p){const f=h.createContext(p),m=l.length;l=[...l,p];const y=x=>{var N;const{scope:S,children:E,...P}=x,C=((N=S==null?void 0:S[n])==null?void 0:N[m])||f,k=h.useMemo(()=>P,Object.values(P));return g.jsx(C.Provider,{value:k,children:E})};y.displayName=c+"Provider";function w(x,S){var C;const E=((C=S==null?void 0:S[n])==null?void 0:C[m])||f,P=h.useContext(E);if(P)return P;if(p!==void 0)return p;throw new Error(`\`${x}\` must be used within \`${c}\``)}return[y,w]}const a=()=>{const c=l.map(p=>h.createContext(p));return function(f){const m=(f==null?void 0:f[n])||c;return h.useMemo(()=>({[`__scope${n}`]:{...f,[n]:m}}),[f,m])}};return a.scopeName=n,[s,uw(a,...o)]}function uw(...n){const o=n[0];if(n.length===1)return o;const l=()=>{const s=n.map(a=>({useScope:a(),scopeName:a.scopeName}));return function(c){const p=s.reduce((f,{useScope:m,scopeName:y})=>{const x=m(c)[`__scope${y}`];return{...f,...x}},{});return h.useMemo(()=>({[`__scope${o.scopeName}`]:p}),[p])}};return l.scopeName=o.scopeName,l}function cw(n){const o=n+"CollectionProvider",[l,s]=zu(o),[a,c]=l(o,{collectionRef:{current:null},itemMap:new Map}),p=E=>{const{scope:P,children:C}=E,k=$n.useRef(null),N=$n.useRef(new Map).current;return g.jsx(a,{scope:P,itemMap:N,collectionRef:k,children:C})};p.displayName=o;const f=n+"CollectionSlot",m=$n.forwardRef((E,P)=>{const{scope:C,children:k}=E,N=c(f,C),j=et(P,N.collectionRef);return g.jsx(fr,{ref:j,children:k})});m.displayName=f;const y=n+"CollectionItemSlot",w="data-radix-collection-item",x=$n.forwardRef((E,P)=>{const{scope:C,children:k,...N}=E,j=$n.useRef(null),A=et(P,j),O=c(y,C);return $n.useEffect(()=>(O.itemMap.set(j,{ref:j,...N}),()=>void O.itemMap.delete(j))),g.jsx(fr,{[w]:"",ref:A,children:k})});x.displayName=y;function S(E){const P=c(n+"CollectionConsumer",E);return $n.useCallback(()=>{const k=P.collectionRef.current;if(!k)return[];const N=Array.from(k.querySelectorAll(`[${w}]`));return Array.from(P.itemMap.values()).sort((O,I)=>N.indexOf(O.ref.current)-N.indexOf(I.ref.current))},[P.collectionRef,P.itemMap])}return[{Provider:p,Slot:m,ItemSlot:x},S,s]}var dw=h.createContext(void 0);function fw(n){const o=h.useContext(dw);return n||o||"ltr"}var pw=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","span","svg","ul"],Be=pw.reduce((n,o)=>{const l=h.forwardRef((s,a)=>{const{asChild:c,...p}=s,f=c?fr:o;return typeof window<"u"&&(window[Symbol.for("radix-ui")]=!0),g.jsx(f,{...p,ref:a})});return l.displayName=`Primitive.${o}`,{...n,[o]:l}},{});function mw(n,o){n&&sl.flushSync(()=>n.dispatchEvent(o))}function pr(n){const o=h.useRef(n);return h.useEffect(()=>{o.current=n}),h.useMemo(()=>(...l)=>{var s;return(s=o.current)==null?void 0:s.call(o,...l)},[])}function hw(n,o=globalThis==null?void 0:globalThis.document){const l=pr(n);h.useEffect(()=>{const s=a=>{a.key==="Escape"&&l(a)};return o.addEventListener("keydown",s,{capture:!0}),()=>o.removeEventListener("keydown",s,{capture:!0})},[l,o])}var gw="DismissableLayer",fu="dismissableLayer.update",vw="dismissableLayer.pointerDownOutside",yw="dismissableLayer.focusOutside",ap,zm=h.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),Am=h.forwardRef((n,o)=>{const{disableOutsidePointerEvents:l=!1,onEscapeKeyDown:s,onPointerDownOutside:a,onFocusOutside:c,onInteractOutside:p,onDismiss:f,...m}=n,y=h.useContext(zm),[w,x]=h.useState(null),S=(w==null?void 0:w.ownerDocument)??(globalThis==null?void 0:globalThis.document),[,E]=h.useState({}),P=et(o,D=>x(D)),C=Array.from(y.layers),[k]=[...y.layersWithOutsidePointerEventsDisabled].slice(-1),N=C.indexOf(k),j=w?C.indexOf(w):-1,A=y.layersWithOutsidePointerEventsDisabled.size>0,O=j>=N,I=Sw(D=>{const oe=D.target,re=[...y.branches].some(ae=>ae.contains(oe));!O||re||(a==null||a(D),p==null||p(D),D.defaultPrevented||f==null||f())},S),B=Cw(D=>{const oe=D.target;[...y.branches].some(ae=>ae.contains(oe))||(c==null||c(D),p==null||p(D),D.defaultPrevented||f==null||f())},S);return hw(D=>{j===y.layers.size-1&&(s==null||s(D),!D.defaultPrevented&&f&&(D.preventDefault(),f()))},S),h.useEffect(()=>{if(w)return l&&(y.layersWithOutsidePointerEventsDisabled.size===0&&(ap=S.body.style.pointerEvents,S.body.style.pointerEvents="none"),y.layersWithOutsidePointerEventsDisabled.add(w)),y.layers.add(w),up(),()=>{l&&y.layersWithOutsidePointerEventsDisabled.size===1&&(S.body.style.pointerEvents=ap)}},[w,S,l,y]),h.useEffect(()=>()=>{w&&(y.layers.delete(w),y.layersWithOutsidePointerEventsDisabled.delete(w),up())},[w,y]),h.useEffect(()=>{const D=()=>E({});return document.addEventListener(fu,D),()=>document.removeEventListener(fu,D)},[]),g.jsx(Be.div,{...m,ref:P,style:{pointerEvents:A?O?"auto":"none":void 0,...n.style},onFocusCapture:He(n.onFocusCapture,B.onFocusCapture),onBlurCapture:He(n.onBlurCapture,B.onBlurCapture),onPointerDownCapture:He(n.onPointerDownCapture,I.onPointerDownCapture)})});Am.displayName=gw;var xw="DismissableLayerBranch",ww=h.forwardRef((n,o)=>{const l=h.useContext(zm),s=h.useRef(null),a=et(o,s);return h.useEffect(()=>{const c=s.current;if(c)return l.branches.add(c),()=>{l.branches.delete(c)}},[l.branches]),g.jsx(Be.div,{...n,ref:a})});ww.displayName=xw;function Sw(n,o=globalThis==null?void 0:globalThis.document){const l=pr(n),s=h.useRef(!1),a=h.useRef(()=>{});return h.useEffect(()=>{const c=f=>{if(f.target&&!s.current){let m=function(){Lm(vw,l,y,{discrete:!0})};const y={originalEvent:f};f.pointerType==="touch"?(o.removeEventListener("click",a.current),a.current=m,o.addEventListener("click",a.current,{once:!0})):m()}else o.removeEventListener("click",a.current);s.current=!1},p=window.setTimeout(()=>{o.addEventListener("pointerdown",c)},0);return()=>{window.clearTimeout(p),o.removeEventListener("pointerdown",c),o.removeEventListener("click",a.current)}},[o,l]),{onPointerDownCapture:()=>s.current=!0}}function Cw(n,o=globalThis==null?void 0:globalThis.document){const l=pr(n),s=h.useRef(!1);return h.useEffect(()=>{const a=c=>{c.target&&!s.current&&Lm(yw,l,{originalEvent:c},{discrete:!1})};return o.addEventListener("focusin",a),()=>o.removeEventListener("focusin",a)},[o,l]),{onFocusCapture:()=>s.current=!0,onBlurCapture:()=>s.current=!1}}function up(){const n=new CustomEvent(fu);document.dispatchEvent(n)}function Lm(n,o,l,{discrete:s}){const a=l.originalEvent.target,c=new CustomEvent(n,{bubbles:!1,cancelable:!0,detail:l});o&&a.addEventListener(n,o,{once:!0}),s?mw(a,c):a.dispatchEvent(c)}var Za=0;function Ew(){h.useEffect(()=>{const n=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",n[0]??cp()),document.body.insertAdjacentElement("beforeend",n[1]??cp()),Za++,()=>{Za===1&&document.querySelectorAll("[data-radix-focus-guard]").forEach(o=>o.remove()),Za--}},[])}function cp(){const n=document.createElement("span");return n.setAttribute("data-radix-focus-guard",""),n.tabIndex=0,n.style.outline="none",n.style.opacity="0",n.style.position="fixed",n.style.pointerEvents="none",n}var qa="focusScope.autoFocusOnMount",Ja="focusScope.autoFocusOnUnmount",dp={bubbles:!1,cancelable:!0},bw="FocusScope",Mm=h.forwardRef((n,o)=>{const{loop:l=!1,trapped:s=!1,onMountAutoFocus:a,onUnmountAutoFocus:c,...p}=n,[f,m]=h.useState(null),y=pr(a),w=pr(c),x=h.useRef(null),S=et(o,C=>m(C)),E=h.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;h.useEffect(()=>{if(s){let C=function(A){if(E.paused||!f)return;const O=A.target;f.contains(O)?x.current=O:Fn(x.current,{select:!0})},k=function(A){if(E.paused||!f)return;const O=A.relatedTarget;O!==null&&(f.contains(O)||Fn(x.current,{select:!0}))},N=function(A){if(document.activeElement===document.body)for(const I of A)I.removedNodes.length>0&&Fn(f)};document.addEventListener("focusin",C),document.addEventListener("focusout",k);const j=new MutationObserver(N);return f&&j.observe(f,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",C),document.removeEventListener("focusout",k),j.disconnect()}}},[s,f,E.paused]),h.useEffect(()=>{if(f){pp.add(E);const C=document.activeElement;if(!f.contains(C)){const N=new CustomEvent(qa,dp);f.addEventListener(qa,y),f.dispatchEvent(N),N.defaultPrevented||(kw(Tw(Im(f)),{select:!0}),document.activeElement===C&&Fn(f))}return()=>{f.removeEventListener(qa,y),setTimeout(()=>{const N=new CustomEvent(Ja,dp);f.addEventListener(Ja,w),f.dispatchEvent(N),N.defaultPrevented||Fn(C??document.body,{select:!0}),f.removeEventListener(Ja,w),pp.remove(E)},0)}}},[f,y,w,E]);const P=h.useCallback(C=>{if(!l&&!s||E.paused)return;const k=C.key==="Tab"&&!C.altKey&&!C.ctrlKey&&!C.metaKey,N=document.activeElement;if(k&&N){const j=C.currentTarget,[A,O]=Pw(j);A&&O?!C.shiftKey&&N===O?(C.preventDefault(),l&&Fn(A,{select:!0})):C.shiftKey&&N===A&&(C.preventDefault(),l&&Fn(O,{select:!0})):N===j&&C.preventDefault()}},[l,s,E.paused]);return g.jsx(Be.div,{tabIndex:-1,...p,ref:S,onKeyDown:P})});Mm.displayName=bw;function kw(n,{select:o=!1}={}){const l=document.activeElement;for(const s of n)if(Fn(s,{select:o}),document.activeElement!==l)return}function Pw(n){const o=Im(n),l=fp(o,n),s=fp(o.reverse(),n);return[l,s]}function Im(n){const o=[],l=document.createTreeWalker(n,NodeFilter.SHOW_ELEMENT,{acceptNode:s=>{const a=s.tagName==="INPUT"&&s.type==="hidden";return s.disabled||s.hidden||a?NodeFilter.FILTER_SKIP:s.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;l.nextNode();)o.push(l.currentNode);return o}function fp(n,o){for(const l of n)if(!Nw(l,{upTo:o}))return l}function Nw(n,{upTo:o}){if(getComputedStyle(n).visibility==="hidden")return!0;for(;n;){if(o!==void 0&&n===o)return!1;if(getComputedStyle(n).display==="none")return!0;n=n.parentElement}return!1}function Rw(n){return n instanceof HTMLInputElement&&"select"in n}function Fn(n,{select:o=!1}={}){if(n&&n.focus){const l=document.activeElement;n.focus({preventScroll:!0}),n!==l&&Rw(n)&&o&&n.select()}}var pp=_w();function _w(){let n=[];return{add(o){const l=n[0];o!==l&&(l==null||l.pause()),n=mp(n,o),n.unshift(o)},remove(o){var l;n=mp(n,o),(l=n[0])==null||l.resume()}}}function mp(n,o){const l=[...n],s=l.indexOf(o);return s!==-1&&l.splice(s,1),l}function Tw(n){return n.filter(o=>o.tagName!=="A")}var jw=globalThis!=null&&globalThis.document?h.useLayoutEffect:()=>{},zw=Wi.useId||(()=>{}),Aw=0;function Au(n){const[o,l]=h.useState(zw());return jw(()=>{l(s=>s??String(Aw++))},[n]),n||(o?`radix-${o}`:"")}const Lw=["top","right","bottom","left"],Vn=Math.min,Et=Math.max,Li=Math.round,Ei=Math.floor,en=n=>({x:n,y:n}),Mw={left:"right",right:"left",bottom:"top",top:"bottom"},Iw={start:"end",end:"start"};function pu(n,o,l){return Et(n,Vn(o,l))}function hn(n,o){return typeof n=="function"?n(o):n}function gn(n){return n.split("-")[0]}function io(n){return n.split("-")[1]}function Lu(n){return n==="x"?"y":"x"}function Mu(n){return n==="y"?"height":"width"}function Jt(n){return["top","bottom"].includes(gn(n))?"y":"x"}function Iu(n){return Lu(Jt(n))}function Ow(n,o,l){l===void 0&&(l=!1);const s=io(n),a=Iu(n),c=Mu(a);let p=a==="x"?s===(l?"end":"start")?"right":"left":s==="start"?"bottom":"top";return o.reference[c]>o.floating[c]&&(p=Mi(p)),[p,Mi(p)]}function Dw(n){const o=Mi(n);return[mu(n),o,mu(o)]}function mu(n){return n.replace(/start|end/g,o=>Iw[o])}function $w(n,o,l){const s=["left","right"],a=["right","left"],c=["top","bottom"],p=["bottom","top"];switch(n){case"top":case"bottom":return l?o?a:s:o?s:a;case"left":case"right":return o?c:p;default:return[]}}function Fw(n,o,l,s){const a=io(n);let c=$w(gn(n),l==="start",s);return a&&(c=c.map(p=>p+"-"+a),o&&(c=c.concat(c.map(mu)))),c}function Mi(n){return n.replace(/left|right|bottom|top/g,o=>Mw[o])}function Ww(n){return{top:0,right:0,bottom:0,left:0,...n}}function Om(n){return typeof n!="number"?Ww(n):{top:n,right:n,bottom:n,left:n}}function Ii(n){const{x:o,y:l,width:s,height:a}=n;return{width:s,height:a,top:l,left:o,right:o+s,bottom:l+a,x:o,y:l}}function hp(n,o,l){let{reference:s,floating:a}=n;const c=Jt(o),p=Iu(o),f=Mu(p),m=gn(o),y=c==="y",w=s.x+s.width/2-a.width/2,x=s.y+s.height/2-a.height/2,S=s[f]/2-a[f]/2;let E;switch(m){case"top":E={x:w,y:s.y-a.height};break;case"bottom":E={x:w,y:s.y+s.height};break;case"right":E={x:s.x+s.width,y:x};break;case"left":E={x:s.x-a.width,y:x};break;default:E={x:s.x,y:s.y}}switch(io(o)){case"start":E[p]-=S*(l&&y?-1:1);break;case"end":E[p]+=S*(l&&y?-1:1);break}return E}const Vw=async(n,o,l)=>{const{placement:s="bottom",strategy:a="absolute",middleware:c=[],platform:p}=l,f=c.filter(Boolean),m=await(p.isRTL==null?void 0:p.isRTL(o));let y=await p.getElementRects({reference:n,floating:o,strategy:a}),{x:w,y:x}=hp(y,s,m),S=s,E={},P=0;for(let C=0;C<f.length;C++){const{name:k,fn:N}=f[C],{x:j,y:A,data:O,reset:I}=await N({x:w,y:x,initialPlacement:s,placement:S,strategy:a,middlewareData:E,rects:y,platform:p,elements:{reference:n,floating:o}});w=j??w,x=A??x,E={...E,[k]:{...E[k],...O}},I&&P<=50&&(P++,typeof I=="object"&&(I.placement&&(S=I.placement),I.rects&&(y=I.rects===!0?await p.getElementRects({reference:n,floating:o,strategy:a}):I.rects),{x:w,y:x}=hp(y,S,m)),C=-1)}return{x:w,y:x,placement:S,strategy:a,middlewareData:E}};async function rl(n,o){var l;o===void 0&&(o={});const{x:s,y:a,platform:c,rects:p,elements:f,strategy:m}=n,{boundary:y="clippingAncestors",rootBoundary:w="viewport",elementContext:x="floating",altBoundary:S=!1,padding:E=0}=hn(o,n),P=Om(E),k=f[S?x==="floating"?"reference":"floating":x],N=Ii(await c.getClippingRect({element:(l=await(c.isElement==null?void 0:c.isElement(k)))==null||l?k:k.contextElement||await(c.getDocumentElement==null?void 0:c.getDocumentElement(f.floating)),boundary:y,rootBoundary:w,strategy:m})),j=x==="floating"?{x:s,y:a,width:p.floating.width,height:p.floating.height}:p.reference,A=await(c.getOffsetParent==null?void 0:c.getOffsetParent(f.floating)),O=await(c.isElement==null?void 0:c.isElement(A))?await(c.getScale==null?void 0:c.getScale(A))||{x:1,y:1}:{x:1,y:1},I=Ii(c.convertOffsetParentRelativeRectToViewportRelativeRect?await c.convertOffsetParentRelativeRectToViewportRelativeRect({elements:f,rect:j,offsetParent:A,strategy:m}):j);return{top:(N.top-I.top+P.top)/O.y,bottom:(I.bottom-N.bottom+P.bottom)/O.y,left:(N.left-I.left+P.left)/O.x,right:(I.right-N.right+P.right)/O.x}}const Hw=n=>({name:"arrow",options:n,async fn(o){const{x:l,y:s,placement:a,rects:c,platform:p,elements:f,middlewareData:m}=o,{element:y,padding:w=0}=hn(n,o)||{};if(y==null)return{};const x=Om(w),S={x:l,y:s},E=Iu(a),P=Mu(E),C=await p.getDimensions(y),k=E==="y",N=k?"top":"left",j=k?"bottom":"right",A=k?"clientHeight":"clientWidth",O=c.reference[P]+c.reference[E]-S[E]-c.floating[P],I=S[E]-c.reference[E],B=await(p.getOffsetParent==null?void 0:p.getOffsetParent(y));let D=B?B[A]:0;(!D||!await(p.isElement==null?void 0:p.isElement(B)))&&(D=f.floating[A]||c.floating[P]);const oe=O/2-I/2,re=D/2-C[P]/2-1,ae=Vn(x[N],re),me=Vn(x[j],re),Z=ae,ge=D-C[P]-me,se=D/2-C[P]/2+oe,ve=pu(Z,se,ge),le=!m.arrow&&io(a)!=null&&se!==ve&&c.reference[P]/2-(se<Z?ae:me)-C[P]/2<0,de=le?se<Z?se-Z:se-ge:0;return{[E]:S[E]+de,data:{[E]:ve,centerOffset:se-ve-de,...le&&{alignmentOffset:de}},reset:le}}}),Bw=function(n){return n===void 0&&(n={}),{name:"flip",options:n,async fn(o){var l,s;const{placement:a,middlewareData:c,rects:p,initialPlacement:f,platform:m,elements:y}=o,{mainAxis:w=!0,crossAxis:x=!0,fallbackPlacements:S,fallbackStrategy:E="bestFit",fallbackAxisSideDirection:P="none",flipAlignment:C=!0,...k}=hn(n,o);if((l=c.arrow)!=null&&l.alignmentOffset)return{};const N=gn(a),j=Jt(f),A=gn(f)===f,O=await(m.isRTL==null?void 0:m.isRTL(y.floating)),I=S||(A||!C?[Mi(f)]:Dw(f)),B=P!=="none";!S&&B&&I.push(...Fw(f,C,P,O));const D=[f,...I],oe=await rl(o,k),re=[];let ae=((s=c.flip)==null?void 0:s.overflows)||[];if(w&&re.push(oe[N]),x){const se=Ow(a,p,O);re.push(oe[se[0]],oe[se[1]])}if(ae=[...ae,{placement:a,overflows:re}],!re.every(se=>se<=0)){var me,Z;const se=(((me=c.flip)==null?void 0:me.index)||0)+1,ve=D[se];if(ve&&(!(x==="alignment"?j!==Jt(ve):!1)||ae.every($=>$.overflows[0]>0&&Jt($.placement)===j)))return{data:{index:se,overflows:ae},reset:{placement:ve}};let le=(Z=ae.filter(de=>de.overflows[0]<=0).sort((de,$)=>de.overflows[1]-$.overflows[1])[0])==null?void 0:Z.placement;if(!le)switch(E){case"bestFit":{var ge;const de=(ge=ae.filter($=>{if(B){const q=Jt($.placement);return q===j||q==="y"}return!0}).map($=>[$.placement,$.overflows.filter(q=>q>0).reduce((q,G)=>q+G,0)]).sort(($,q)=>$[1]-q[1])[0])==null?void 0:ge[0];de&&(le=de);break}case"initialPlacement":le=f;break}if(a!==le)return{reset:{placement:le}}}return{}}}};function gp(n,o){return{top:n.top-o.height,right:n.right-o.width,bottom:n.bottom-o.height,left:n.left-o.width}}function vp(n){return Lw.some(o=>n[o]>=0)}const Uw=function(n){return n===void 0&&(n={}),{name:"hide",options:n,async fn(o){const{rects:l}=o,{strategy:s="referenceHidden",...a}=hn(n,o);switch(s){case"referenceHidden":{const c=await rl(o,{...a,elementContext:"reference"}),p=gp(c,l.reference);return{data:{referenceHiddenOffsets:p,referenceHidden:vp(p)}}}case"escaped":{const c=await rl(o,{...a,altBoundary:!0}),p=gp(c,l.floating);return{data:{escapedOffsets:p,escaped:vp(p)}}}default:return{}}}}};async function Kw(n,o){const{placement:l,platform:s,elements:a}=n,c=await(s.isRTL==null?void 0:s.isRTL(a.floating)),p=gn(l),f=io(l),m=Jt(l)==="y",y=["left","top"].includes(p)?-1:1,w=c&&m?-1:1,x=hn(o,n);let{mainAxis:S,crossAxis:E,alignmentAxis:P}=typeof x=="number"?{mainAxis:x,crossAxis:0,alignmentAxis:null}:{mainAxis:x.mainAxis||0,crossAxis:x.crossAxis||0,alignmentAxis:x.alignmentAxis};return f&&typeof P=="number"&&(E=f==="end"?P*-1:P),m?{x:E*w,y:S*y}:{x:S*y,y:E*w}}const Qw=function(n){return n===void 0&&(n=0),{name:"offset",options:n,async fn(o){var l,s;const{x:a,y:c,placement:p,middlewareData:f}=o,m=await Kw(o,n);return p===((l=f.offset)==null?void 0:l.placement)&&(s=f.arrow)!=null&&s.alignmentOffset?{}:{x:a+m.x,y:c+m.y,data:{...m,placement:p}}}}},Gw=function(n){return n===void 0&&(n={}),{name:"shift",options:n,async fn(o){const{x:l,y:s,placement:a}=o,{mainAxis:c=!0,crossAxis:p=!1,limiter:f={fn:k=>{let{x:N,y:j}=k;return{x:N,y:j}}},...m}=hn(n,o),y={x:l,y:s},w=await rl(o,m),x=Jt(gn(a)),S=Lu(x);let E=y[S],P=y[x];if(c){const k=S==="y"?"top":"left",N=S==="y"?"bottom":"right",j=E+w[k],A=E-w[N];E=pu(j,E,A)}if(p){const k=x==="y"?"top":"left",N=x==="y"?"bottom":"right",j=P+w[k],A=P-w[N];P=pu(j,P,A)}const C=f.fn({...o,[S]:E,[x]:P});return{...C,data:{x:C.x-l,y:C.y-s,enabled:{[S]:c,[x]:p}}}}}},Yw=function(n){return n===void 0&&(n={}),{options:n,fn(o){const{x:l,y:s,placement:a,rects:c,middlewareData:p}=o,{offset:f=0,mainAxis:m=!0,crossAxis:y=!0}=hn(n,o),w={x:l,y:s},x=Jt(a),S=Lu(x);let E=w[S],P=w[x];const C=hn(f,o),k=typeof C=="number"?{mainAxis:C,crossAxis:0}:{mainAxis:0,crossAxis:0,...C};if(m){const A=S==="y"?"height":"width",O=c.reference[S]-c.floating[A]+k.mainAxis,I=c.reference[S]+c.reference[A]-k.mainAxis;E<O?E=O:E>I&&(E=I)}if(y){var N,j;const A=S==="y"?"width":"height",O=["top","left"].includes(gn(a)),I=c.reference[x]-c.floating[A]+(O&&((N=p.offset)==null?void 0:N[x])||0)+(O?0:k.crossAxis),B=c.reference[x]+c.reference[A]+(O?0:((j=p.offset)==null?void 0:j[x])||0)-(O?k.crossAxis:0);P<I?P=I:P>B&&(P=B)}return{[S]:E,[x]:P}}}},Xw=function(n){return n===void 0&&(n={}),{name:"size",options:n,async fn(o){var l,s;const{placement:a,rects:c,platform:p,elements:f}=o,{apply:m=()=>{},...y}=hn(n,o),w=await rl(o,y),x=gn(a),S=io(a),E=Jt(a)==="y",{width:P,height:C}=c.floating;let k,N;x==="top"||x==="bottom"?(k=x,N=S===(await(p.isRTL==null?void 0:p.isRTL(f.floating))?"start":"end")?"left":"right"):(N=x,k=S==="end"?"top":"bottom");const j=C-w.top-w.bottom,A=P-w.left-w.right,O=Vn(C-w[k],j),I=Vn(P-w[N],A),B=!o.middlewareData.shift;let D=O,oe=I;if((l=o.middlewareData.shift)!=null&&l.enabled.x&&(oe=A),(s=o.middlewareData.shift)!=null&&s.enabled.y&&(D=j),B&&!S){const ae=Et(w.left,0),me=Et(w.right,0),Z=Et(w.top,0),ge=Et(w.bottom,0);E?oe=P-2*(ae!==0||me!==0?ae+me:Et(w.left,w.right)):D=C-2*(Z!==0||ge!==0?Z+ge:Et(w.top,w.bottom))}await m({...o,availableWidth:oe,availableHeight:D});const re=await p.getDimensions(f.floating);return P!==re.width||C!==re.height?{reset:{rects:!0}}:{}}}};function Ui(){return typeof window<"u"}function so(n){return Dm(n)?(n.nodeName||"").toLowerCase():"#document"}function bt(n){var o;return(n==null||(o=n.ownerDocument)==null?void 0:o.defaultView)||window}function rn(n){var o;return(o=(Dm(n)?n.ownerDocument:n.document)||window.document)==null?void 0:o.documentElement}function Dm(n){return Ui()?n instanceof Node||n instanceof bt(n).Node:!1}function Ut(n){return Ui()?n instanceof Element||n instanceof bt(n).Element:!1}function tn(n){return Ui()?n instanceof HTMLElement||n instanceof bt(n).HTMLElement:!1}function yp(n){return!Ui()||typeof ShadowRoot>"u"?!1:n instanceof ShadowRoot||n instanceof bt(n).ShadowRoot}function cl(n){const{overflow:o,overflowX:l,overflowY:s,display:a}=Kt(n);return/auto|scroll|overlay|hidden|clip/.test(o+s+l)&&!["inline","contents"].includes(a)}function Zw(n){return["table","td","th"].includes(so(n))}function Ki(n){return[":popover-open",":modal"].some(o=>{try{return n.matches(o)}catch{return!1}})}function Ou(n){const o=Du(),l=Ut(n)?Kt(n):n;return["transform","translate","scale","rotate","perspective"].some(s=>l[s]?l[s]!=="none":!1)||(l.containerType?l.containerType!=="normal":!1)||!o&&(l.backdropFilter?l.backdropFilter!=="none":!1)||!o&&(l.filter?l.filter!=="none":!1)||["transform","translate","scale","rotate","perspective","filter"].some(s=>(l.willChange||"").includes(s))||["paint","layout","strict","content"].some(s=>(l.contain||"").includes(s))}function qw(n){let o=Hn(n);for(;tn(o)&&!no(o);){if(Ou(o))return o;if(Ki(o))return null;o=Hn(o)}return null}function Du(){return typeof CSS>"u"||!CSS.supports?!1:CSS.supports("-webkit-backdrop-filter","none")}function no(n){return["html","body","#document"].includes(so(n))}function Kt(n){return bt(n).getComputedStyle(n)}function Qi(n){return Ut(n)?{scrollLeft:n.scrollLeft,scrollTop:n.scrollTop}:{scrollLeft:n.scrollX,scrollTop:n.scrollY}}function Hn(n){if(so(n)==="html")return n;const o=n.assignedSlot||n.parentNode||yp(n)&&n.host||rn(n);return yp(o)?o.host:o}function $m(n){const o=Hn(n);return no(o)?n.ownerDocument?n.ownerDocument.body:n.body:tn(o)&&cl(o)?o:$m(o)}function ol(n,o,l){var s;o===void 0&&(o=[]),l===void 0&&(l=!0);const a=$m(n),c=a===((s=n.ownerDocument)==null?void 0:s.body),p=bt(a);if(c){const f=hu(p);return o.concat(p,p.visualViewport||[],cl(a)?a:[],f&&l?ol(f):[])}return o.concat(a,ol(a,[],l))}function hu(n){return n.parent&&Object.getPrototypeOf(n.parent)?n.frameElement:null}function Fm(n){const o=Kt(n);let l=parseFloat(o.width)||0,s=parseFloat(o.height)||0;const a=tn(n),c=a?n.offsetWidth:l,p=a?n.offsetHeight:s,f=Li(l)!==c||Li(s)!==p;return f&&(l=c,s=p),{width:l,height:s,$:f}}function $u(n){return Ut(n)?n:n.contextElement}function Zr(n){const o=$u(n);if(!tn(o))return en(1);const l=o.getBoundingClientRect(),{width:s,height:a,$:c}=Fm(o);let p=(c?Li(l.width):l.width)/s,f=(c?Li(l.height):l.height)/a;return(!p||!Number.isFinite(p))&&(p=1),(!f||!Number.isFinite(f))&&(f=1),{x:p,y:f}}const Jw=en(0);function Wm(n){const o=bt(n);return!Du()||!o.visualViewport?Jw:{x:o.visualViewport.offsetLeft,y:o.visualViewport.offsetTop}}function eS(n,o,l){return o===void 0&&(o=!1),!l||o&&l!==bt(n)?!1:o}function mr(n,o,l,s){o===void 0&&(o=!1),l===void 0&&(l=!1);const a=n.getBoundingClientRect(),c=$u(n);let p=en(1);o&&(s?Ut(s)&&(p=Zr(s)):p=Zr(n));const f=eS(c,l,s)?Wm(c):en(0);let m=(a.left+f.x)/p.x,y=(a.top+f.y)/p.y,w=a.width/p.x,x=a.height/p.y;if(c){const S=bt(c),E=s&&Ut(s)?bt(s):s;let P=S,C=hu(P);for(;C&&s&&E!==P;){const k=Zr(C),N=C.getBoundingClientRect(),j=Kt(C),A=N.left+(C.clientLeft+parseFloat(j.paddingLeft))*k.x,O=N.top+(C.clientTop+parseFloat(j.paddingTop))*k.y;m*=k.x,y*=k.y,w*=k.x,x*=k.y,m+=A,y+=O,P=bt(C),C=hu(P)}}return Ii({width:w,height:x,x:m,y})}function Fu(n,o){const l=Qi(n).scrollLeft;return o?o.left+l:mr(rn(n)).left+l}function Vm(n,o,l){l===void 0&&(l=!1);const s=n.getBoundingClientRect(),a=s.left+o.scrollLeft-(l?0:Fu(n,s)),c=s.top+o.scrollTop;return{x:a,y:c}}function tS(n){let{elements:o,rect:l,offsetParent:s,strategy:a}=n;const c=a==="fixed",p=rn(s),f=o?Ki(o.floating):!1;if(s===p||f&&c)return l;let m={scrollLeft:0,scrollTop:0},y=en(1);const w=en(0),x=tn(s);if((x||!x&&!c)&&((so(s)!=="body"||cl(p))&&(m=Qi(s)),tn(s))){const E=mr(s);y=Zr(s),w.x=E.x+s.clientLeft,w.y=E.y+s.clientTop}const S=p&&!x&&!c?Vm(p,m,!0):en(0);return{width:l.width*y.x,height:l.height*y.y,x:l.x*y.x-m.scrollLeft*y.x+w.x+S.x,y:l.y*y.y-m.scrollTop*y.y+w.y+S.y}}function nS(n){return Array.from(n.getClientRects())}function rS(n){const o=rn(n),l=Qi(n),s=n.ownerDocument.body,a=Et(o.scrollWidth,o.clientWidth,s.scrollWidth,s.clientWidth),c=Et(o.scrollHeight,o.clientHeight,s.scrollHeight,s.clientHeight);let p=-l.scrollLeft+Fu(n);const f=-l.scrollTop;return Kt(s).direction==="rtl"&&(p+=Et(o.clientWidth,s.clientWidth)-a),{width:a,height:c,x:p,y:f}}function oS(n,o){const l=bt(n),s=rn(n),a=l.visualViewport;let c=s.clientWidth,p=s.clientHeight,f=0,m=0;if(a){c=a.width,p=a.height;const y=Du();(!y||y&&o==="fixed")&&(f=a.offsetLeft,m=a.offsetTop)}return{width:c,height:p,x:f,y:m}}function lS(n,o){const l=mr(n,!0,o==="fixed"),s=l.top+n.clientTop,a=l.left+n.clientLeft,c=tn(n)?Zr(n):en(1),p=n.clientWidth*c.x,f=n.clientHeight*c.y,m=a*c.x,y=s*c.y;return{width:p,height:f,x:m,y}}function xp(n,o,l){let s;if(o==="viewport")s=oS(n,l);else if(o==="document")s=rS(rn(n));else if(Ut(o))s=lS(o,l);else{const a=Wm(n);s={x:o.x-a.x,y:o.y-a.y,width:o.width,height:o.height}}return Ii(s)}function Hm(n,o){const l=Hn(n);return l===o||!Ut(l)||no(l)?!1:Kt(l).position==="fixed"||Hm(l,o)}function iS(n,o){const l=o.get(n);if(l)return l;let s=ol(n,[],!1).filter(f=>Ut(f)&&so(f)!=="body"),a=null;const c=Kt(n).position==="fixed";let p=c?Hn(n):n;for(;Ut(p)&&!no(p);){const f=Kt(p),m=Ou(p);!m&&f.position==="fixed"&&(a=null),(c?!m&&!a:!m&&f.position==="static"&&!!a&&["absolute","fixed"].includes(a.position)||cl(p)&&!m&&Hm(n,p))?s=s.filter(w=>w!==p):a=f,p=Hn(p)}return o.set(n,s),s}function sS(n){let{element:o,boundary:l,rootBoundary:s,strategy:a}=n;const p=[...l==="clippingAncestors"?Ki(o)?[]:iS(o,this._c):[].concat(l),s],f=p[0],m=p.reduce((y,w)=>{const x=xp(o,w,a);return y.top=Et(x.top,y.top),y.right=Vn(x.right,y.right),y.bottom=Vn(x.bottom,y.bottom),y.left=Et(x.left,y.left),y},xp(o,f,a));return{width:m.right-m.left,height:m.bottom-m.top,x:m.left,y:m.top}}function aS(n){const{width:o,height:l}=Fm(n);return{width:o,height:l}}function uS(n,o,l){const s=tn(o),a=rn(o),c=l==="fixed",p=mr(n,!0,c,o);let f={scrollLeft:0,scrollTop:0};const m=en(0);function y(){m.x=Fu(a)}if(s||!s&&!c)if((so(o)!=="body"||cl(a))&&(f=Qi(o)),s){const E=mr(o,!0,c,o);m.x=E.x+o.clientLeft,m.y=E.y+o.clientTop}else a&&y();c&&!s&&a&&y();const w=a&&!s&&!c?Vm(a,f):en(0),x=p.left+f.scrollLeft-m.x-w.x,S=p.top+f.scrollTop-m.y-w.y;return{x,y:S,width:p.width,height:p.height}}function eu(n){return Kt(n).position==="static"}function wp(n,o){if(!tn(n)||Kt(n).position==="fixed")return null;if(o)return o(n);let l=n.offsetParent;return rn(n)===l&&(l=l.ownerDocument.body),l}function Bm(n,o){const l=bt(n);if(Ki(n))return l;if(!tn(n)){let a=Hn(n);for(;a&&!no(a);){if(Ut(a)&&!eu(a))return a;a=Hn(a)}return l}let s=wp(n,o);for(;s&&Zw(s)&&eu(s);)s=wp(s,o);return s&&no(s)&&eu(s)&&!Ou(s)?l:s||qw(n)||l}const cS=async function(n){const o=this.getOffsetParent||Bm,l=this.getDimensions,s=await l(n.floating);return{reference:uS(n.reference,await o(n.floating),n.strategy),floating:{x:0,y:0,width:s.width,height:s.height}}};function dS(n){return Kt(n).direction==="rtl"}const fS={convertOffsetParentRelativeRectToViewportRelativeRect:tS,getDocumentElement:rn,getClippingRect:sS,getOffsetParent:Bm,getElementRects:cS,getClientRects:nS,getDimensions:aS,getScale:Zr,isElement:Ut,isRTL:dS};function Um(n,o){return n.x===o.x&&n.y===o.y&&n.width===o.width&&n.height===o.height}function pS(n,o){let l=null,s;const a=rn(n);function c(){var f;clearTimeout(s),(f=l)==null||f.disconnect(),l=null}function p(f,m){f===void 0&&(f=!1),m===void 0&&(m=1),c();const y=n.getBoundingClientRect(),{left:w,top:x,width:S,height:E}=y;if(f||o(),!S||!E)return;const P=Ei(x),C=Ei(a.clientWidth-(w+S)),k=Ei(a.clientHeight-(x+E)),N=Ei(w),A={rootMargin:-P+"px "+-C+"px "+-k+"px "+-N+"px",threshold:Et(0,Vn(1,m))||1};let O=!0;function I(B){const D=B[0].intersectionRatio;if(D!==m){if(!O)return p();D?p(!1,D):s=setTimeout(()=>{p(!1,1e-7)},1e3)}D===1&&!Um(y,n.getBoundingClientRect())&&p(),O=!1}try{l=new IntersectionObserver(I,{...A,root:a.ownerDocument})}catch{l=new IntersectionObserver(I,A)}l.observe(n)}return p(!0),c}function mS(n,o,l,s){s===void 0&&(s={});const{ancestorScroll:a=!0,ancestorResize:c=!0,elementResize:p=typeof ResizeObserver=="function",layoutShift:f=typeof IntersectionObserver=="function",animationFrame:m=!1}=s,y=$u(n),w=a||c?[...y?ol(y):[],...ol(o)]:[];w.forEach(N=>{a&&N.addEventListener("scroll",l,{passive:!0}),c&&N.addEventListener("resize",l)});const x=y&&f?pS(y,l):null;let S=-1,E=null;p&&(E=new ResizeObserver(N=>{let[j]=N;j&&j.target===y&&E&&(E.unobserve(o),cancelAnimationFrame(S),S=requestAnimationFrame(()=>{var A;(A=E)==null||A.observe(o)})),l()}),y&&!m&&E.observe(y),E.observe(o));let P,C=m?mr(n):null;m&&k();function k(){const N=mr(n);C&&!Um(C,N)&&l(),C=N,P=requestAnimationFrame(k)}return l(),()=>{var N;w.forEach(j=>{a&&j.removeEventListener("scroll",l),c&&j.removeEventListener("resize",l)}),x==null||x(),(N=E)==null||N.disconnect(),E=null,m&&cancelAnimationFrame(P)}}const hS=Qw,gS=Gw,vS=Bw,yS=Xw,xS=Uw,Sp=Hw,wS=Yw,SS=(n,o,l)=>{const s=new Map,a={platform:fS,...l},c={...a.platform,_c:s};return Vw(n,o,{...a,platform:c})};var CS=typeof document<"u",ES=function(){},_i=CS?h.useLayoutEffect:ES;function Oi(n,o){if(n===o)return!0;if(typeof n!=typeof o)return!1;if(typeof n=="function"&&n.toString()===o.toString())return!0;let l,s,a;if(n&&o&&typeof n=="object"){if(Array.isArray(n)){if(l=n.length,l!==o.length)return!1;for(s=l;s--!==0;)if(!Oi(n[s],o[s]))return!1;return!0}if(a=Object.keys(n),l=a.length,l!==Object.keys(o).length)return!1;for(s=l;s--!==0;)if(!{}.hasOwnProperty.call(o,a[s]))return!1;for(s=l;s--!==0;){const c=a[s];if(!(c==="_owner"&&n.$$typeof)&&!Oi(n[c],o[c]))return!1}return!0}return n!==n&&o!==o}function Km(n){return typeof window>"u"?1:(n.ownerDocument.defaultView||window).devicePixelRatio||1}function Cp(n,o){const l=Km(n);return Math.round(o*l)/l}function tu(n){const o=h.useRef(n);return _i(()=>{o.current=n}),o}function bS(n){n===void 0&&(n={});const{placement:o="bottom",strategy:l="absolute",middleware:s=[],platform:a,elements:{reference:c,floating:p}={},transform:f=!0,whileElementsMounted:m,open:y}=n,[w,x]=h.useState({x:0,y:0,strategy:l,placement:o,middlewareData:{},isPositioned:!1}),[S,E]=h.useState(s);Oi(S,s)||E(s);const[P,C]=h.useState(null),[k,N]=h.useState(null),j=h.useCallback($=>{$!==B.current&&(B.current=$,C($))},[]),A=h.useCallback($=>{$!==D.current&&(D.current=$,N($))},[]),O=c||P,I=p||k,B=h.useRef(null),D=h.useRef(null),oe=h.useRef(w),re=m!=null,ae=tu(m),me=tu(a),Z=tu(y),ge=h.useCallback(()=>{if(!B.current||!D.current)return;const $={placement:o,strategy:l,middleware:S};me.current&&($.platform=me.current),SS(B.current,D.current,$).then(q=>{const G={...q,isPositioned:Z.current!==!1};se.current&&!Oi(oe.current,G)&&(oe.current=G,sl.flushSync(()=>{x(G)}))})},[S,o,l,me,Z]);_i(()=>{y===!1&&oe.current.isPositioned&&(oe.current.isPositioned=!1,x($=>({...$,isPositioned:!1})))},[y]);const se=h.useRef(!1);_i(()=>(se.current=!0,()=>{se.current=!1}),[]),_i(()=>{if(O&&(B.current=O),I&&(D.current=I),O&&I){if(ae.current)return ae.current(O,I,ge);ge()}},[O,I,ge,ae,re]);const ve=h.useMemo(()=>({reference:B,floating:D,setReference:j,setFloating:A}),[j,A]),le=h.useMemo(()=>({reference:O,floating:I}),[O,I]),de=h.useMemo(()=>{const $={position:l,left:0,top:0};if(!le.floating)return $;const q=Cp(le.floating,w.x),G=Cp(le.floating,w.y);return f?{...$,transform:"translate("+q+"px, "+G+"px)",...Km(le.floating)>=1.5&&{willChange:"transform"}}:{position:l,left:q,top:G}},[l,f,le.floating,w.x,w.y]);return h.useMemo(()=>({...w,update:ge,refs:ve,elements:le,floatingStyles:de}),[w,ge,ve,le,de])}const kS=n=>{function o(l){return{}.hasOwnProperty.call(l,"current")}return{name:"arrow",options:n,fn(l){const{element:s,padding:a}=typeof n=="function"?n(l):n;return s&&o(s)?s.current!=null?Sp({element:s.current,padding:a}).fn(l):{}:s?Sp({element:s,padding:a}).fn(l):{}}}},PS=(n,o)=>({...hS(n),options:[n,o]}),NS=(n,o)=>({...gS(n),options:[n,o]}),RS=(n,o)=>({...wS(n),options:[n,o]}),_S=(n,o)=>({...vS(n),options:[n,o]}),TS=(n,o)=>({...yS(n),options:[n,o]}),jS=(n,o)=>({...xS(n),options:[n,o]}),zS=(n,o)=>({...kS(n),options:[n,o]});var AS="Arrow",Qm=h.forwardRef((n,o)=>{const{children:l,width:s=10,height:a=5,...c}=n;return g.jsx(Be.svg,{...c,ref:o,width:s,height:a,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:n.asChild?l:g.jsx("polygon",{points:"0,0 30,0 15,10"})})});Qm.displayName=AS;var LS=Qm,At=globalThis!=null&&globalThis.document?h.useLayoutEffect:()=>{};function MS(n){const[o,l]=h.useState(void 0);return At(()=>{if(n){l({width:n.offsetWidth,height:n.offsetHeight});const s=new ResizeObserver(a=>{if(!Array.isArray(a)||!a.length)return;const c=a[0];let p,f;if("borderBoxSize"in c){const m=c.borderBoxSize,y=Array.isArray(m)?m[0]:m;p=y.inlineSize,f=y.blockSize}else p=n.offsetWidth,f=n.offsetHeight;l({width:p,height:f})});return s.observe(n,{box:"border-box"}),()=>s.unobserve(n)}else l(void 0)},[n]),o}var Wu="Popper",[Gm,Ym]=zu(Wu),[IS,Xm]=Gm(Wu),Zm=n=>{const{__scopePopper:o,children:l}=n,[s,a]=h.useState(null);return g.jsx(IS,{scope:o,anchor:s,onAnchorChange:a,children:l})};Zm.displayName=Wu;var qm="PopperAnchor",Jm=h.forwardRef((n,o)=>{const{__scopePopper:l,virtualRef:s,...a}=n,c=Xm(qm,l),p=h.useRef(null),f=et(o,p);return h.useEffect(()=>{c.onAnchorChange((s==null?void 0:s.current)||p.current)}),s?null:g.jsx(Be.div,{...a,ref:f})});Jm.displayName=qm;var Vu="PopperContent",[OS,DS]=Gm(Vu),eh=h.forwardRef((n,o)=>{var F,H,te,J,fe,we;const{__scopePopper:l,side:s="bottom",sideOffset:a=0,align:c="center",alignOffset:p=0,arrowPadding:f=0,avoidCollisions:m=!0,collisionBoundary:y=[],collisionPadding:w=0,sticky:x="partial",hideWhenDetached:S=!1,updatePositionStrategy:E="optimized",onPlaced:P,...C}=n,k=Xm(Vu,l),[N,j]=h.useState(null),A=et(o,Se=>j(Se)),[O,I]=h.useState(null),B=MS(O),D=(B==null?void 0:B.width)??0,oe=(B==null?void 0:B.height)??0,re=s+(c!=="center"?"-"+c:""),ae=typeof w=="number"?w:{top:0,right:0,bottom:0,left:0,...w},me=Array.isArray(y)?y:[y],Z=me.length>0,ge={padding:ae,boundary:me.filter(FS),altBoundary:Z},{refs:se,floatingStyles:ve,placement:le,isPositioned:de,middlewareData:$}=bS({strategy:"fixed",placement:re,whileElementsMounted:(...Se)=>mS(...Se,{animationFrame:E==="always"}),elements:{reference:k.anchor},middleware:[PS({mainAxis:a+oe,alignmentAxis:p}),m&&NS({mainAxis:!0,crossAxis:!1,limiter:x==="partial"?RS():void 0,...ge}),m&&_S({...ge}),TS({...ge,apply:({elements:Se,rects:Pe,availableWidth:tt,availableHeight:st})=>{const{width:Ze,height:Ue}=Pe.reference,Mt=Se.floating.style;Mt.setProperty("--radix-popper-available-width",`${tt}px`),Mt.setProperty("--radix-popper-available-height",`${st}px`),Mt.setProperty("--radix-popper-anchor-width",`${Ze}px`),Mt.setProperty("--radix-popper-anchor-height",`${Ue}px`)}}),O&&zS({element:O,padding:f}),WS({arrowWidth:D,arrowHeight:oe}),S&&jS({strategy:"referenceHidden",...ge})]}),[q,G]=rh(le),_=pr(P);At(()=>{de&&(_==null||_())},[de,_]);const W=(F=$.arrow)==null?void 0:F.x,V=(H=$.arrow)==null?void 0:H.y,Y=((te=$.arrow)==null?void 0:te.centerOffset)!==0,[ne,ee]=h.useState();return At(()=>{N&&ee(window.getComputedStyle(N).zIndex)},[N]),g.jsx("div",{ref:se.setFloating,"data-radix-popper-content-wrapper":"",style:{...ve,transform:de?ve.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:ne,"--radix-popper-transform-origin":[(J=$.transformOrigin)==null?void 0:J.x,(fe=$.transformOrigin)==null?void 0:fe.y].join(" "),...((we=$.hide)==null?void 0:we.referenceHidden)&&{visibility:"hidden",pointerEvents:"none"}},dir:n.dir,children:g.jsx(OS,{scope:l,placedSide:q,onArrowChange:I,arrowX:W,arrowY:V,shouldHideArrow:Y,children:g.jsx(Be.div,{"data-side":q,"data-align":G,...C,ref:A,style:{...C.style,animation:de?void 0:"none"}})})})});eh.displayName=Vu;var th="PopperArrow",$S={top:"bottom",right:"left",bottom:"top",left:"right"},nh=h.forwardRef(function(o,l){const{__scopePopper:s,...a}=o,c=DS(th,s),p=$S[c.placedSide];return g.jsx("span",{ref:c.onArrowChange,style:{position:"absolute",left:c.arrowX,top:c.arrowY,[p]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[c.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[c.placedSide],visibility:c.shouldHideArrow?"hidden":void 0},children:g.jsx(LS,{...a,ref:l,style:{...a.style,display:"block"}})})});nh.displayName=th;function FS(n){return n!==null}var WS=n=>({name:"transformOrigin",options:n,fn(o){var k,N,j;const{placement:l,rects:s,middlewareData:a}=o,p=((k=a.arrow)==null?void 0:k.centerOffset)!==0,f=p?0:n.arrowWidth,m=p?0:n.arrowHeight,[y,w]=rh(l),x={start:"0%",center:"50%",end:"100%"}[w],S=(((N=a.arrow)==null?void 0:N.x)??0)+f/2,E=(((j=a.arrow)==null?void 0:j.y)??0)+m/2;let P="",C="";return y==="bottom"?(P=p?x:`${S}px`,C=`${-m}px`):y==="top"?(P=p?x:`${S}px`,C=`${s.floating.height+m}px`):y==="right"?(P=`${-m}px`,C=p?x:`${E}px`):y==="left"&&(P=`${s.floating.width+m}px`,C=p?x:`${E}px`),{data:{x:P,y:C}}}});function rh(n){const[o,l="center"]=n.split("-");return[o,l]}var VS=Zm,HS=Jm,BS=eh,US=nh,KS="Portal",oh=h.forwardRef((n,o)=>{var f;const{container:l,...s}=n,[a,c]=h.useState(!1);At(()=>c(!0),[]);const p=l||a&&((f=globalThis==null?void 0:globalThis.document)==null?void 0:f.body);return p?gy.createPortal(g.jsx(Be.div,{...s,ref:o}),p):null});oh.displayName=KS;function lh(n){const o=h.useRef(n);return h.useEffect(()=>{o.current=n}),h.useMemo(()=>(...l)=>{var s;return(s=o.current)==null?void 0:s.call(o,...l)},[])}function Ep({prop:n,defaultProp:o,onChange:l=()=>{}}){const[s,a]=QS({defaultProp:o,onChange:l}),c=n!==void 0,p=c?n:s,f=lh(l),m=h.useCallback(y=>{if(c){const x=typeof y=="function"?y(n):y;x!==n&&f(x)}else a(y)},[c,n,a,f]);return[p,m]}function QS({defaultProp:n,onChange:o}){const l=h.useState(n),[s]=l,a=h.useRef(s),c=lh(o);return h.useEffect(()=>{a.current!==s&&(c(s),a.current=s)},[s,a,c]),l}function GS(n){const o=h.useRef({value:n,previous:n});return h.useMemo(()=>(o.current.value!==n&&(o.current.previous=o.current.value,o.current.value=n),o.current.previous),[n])}var YS="VisuallyHidden",ih=h.forwardRef((n,o)=>g.jsx(Be.span,{...n,ref:o,style:{position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal",...n.style}}));ih.displayName=YS;var XS=function(n){if(typeof document>"u")return null;var o=Array.isArray(n)?n[0]:n;return o.ownerDocument.body},Vr=new WeakMap,bi=new WeakMap,ki={},nu=0,sh=function(n){return n&&(n.host||sh(n.parentNode))},ZS=function(n,o){return o.map(function(l){if(n.contains(l))return l;var s=sh(l);return s&&n.contains(s)?s:(console.error("aria-hidden",l,"in not contained inside",n,". Doing nothing"),null)}).filter(function(l){return!!l})},qS=function(n,o,l,s){var a=ZS(o,Array.isArray(n)?n:[n]);ki[l]||(ki[l]=new WeakMap);var c=ki[l],p=[],f=new Set,m=new Set(a),y=function(x){!x||f.has(x)||(f.add(x),y(x.parentNode))};a.forEach(y);var w=function(x){!x||m.has(x)||Array.prototype.forEach.call(x.children,function(S){if(f.has(S))w(S);else try{var E=S.getAttribute(s),P=E!==null&&E!=="false",C=(Vr.get(S)||0)+1,k=(c.get(S)||0)+1;Vr.set(S,C),c.set(S,k),p.push(S),C===1&&P&&bi.set(S,!0),k===1&&S.setAttribute(l,"true"),P||S.setAttribute(s,"true")}catch(N){console.error("aria-hidden: cannot operate on ",S,N)}})};return w(o),f.clear(),nu++,function(){p.forEach(function(x){var S=Vr.get(x)-1,E=c.get(x)-1;Vr.set(x,S),c.set(x,E),S||(bi.has(x)||x.removeAttribute(s),bi.delete(x)),E||x.removeAttribute(l)}),nu--,nu||(Vr=new WeakMap,Vr=new WeakMap,bi=new WeakMap,ki={})}},JS=function(n,o,l){l===void 0&&(l="data-aria-hidden");var s=Array.from(Array.isArray(n)?n:[n]),a=XS(n);return a?(s.push.apply(s,Array.from(a.querySelectorAll("[aria-live], script"))),qS(s,a,l,"aria-hidden")):function(){return null}},qt=function(){return qt=Object.assign||function(o){for(var l,s=1,a=arguments.length;s<a;s++){l=arguments[s];for(var c in l)Object.prototype.hasOwnProperty.call(l,c)&&(o[c]=l[c])}return o},qt.apply(this,arguments)};function ah(n,o){var l={};for(var s in n)Object.prototype.hasOwnProperty.call(n,s)&&o.indexOf(s)<0&&(l[s]=n[s]);if(n!=null&&typeof Object.getOwnPropertySymbols=="function")for(var a=0,s=Object.getOwnPropertySymbols(n);a<s.length;a++)o.indexOf(s[a])<0&&Object.prototype.propertyIsEnumerable.call(n,s[a])&&(l[s[a]]=n[s[a]]);return l}function e1(n,o,l){if(l||arguments.length===2)for(var s=0,a=o.length,c;s<a;s++)(c||!(s in o))&&(c||(c=Array.prototype.slice.call(o,0,s)),c[s]=o[s]);return n.concat(c||Array.prototype.slice.call(o))}var Ti="right-scroll-bar-position",ji="width-before-scroll-bar",t1="with-scroll-bars-hidden",n1="--removed-body-scroll-bar-size";function ru(n,o){return typeof n=="function"?n(o):n&&(n.current=o),n}function r1(n,o){var l=h.useState(function(){return{value:n,callback:o,facade:{get current(){return l.value},set current(s){var a=l.value;a!==s&&(l.value=s,l.callback(s,a))}}}})[0];return l.callback=o,l.facade}var o1=typeof window<"u"?h.useLayoutEffect:h.useEffect,bp=new WeakMap;function l1(n,o){var l=r1(null,function(s){return n.forEach(function(a){return ru(a,s)})});return o1(function(){var s=bp.get(l);if(s){var a=new Set(s),c=new Set(n),p=l.current;a.forEach(function(f){c.has(f)||ru(f,null)}),c.forEach(function(f){a.has(f)||ru(f,p)})}bp.set(l,n)},[n]),l}function i1(n){return n}function s1(n,o){o===void 0&&(o=i1);var l=[],s=!1,a={read:function(){if(s)throw new Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return l.length?l[l.length-1]:n},useMedium:function(c){var p=o(c,s);return l.push(p),function(){l=l.filter(function(f){return f!==p})}},assignSyncMedium:function(c){for(s=!0;l.length;){var p=l;l=[],p.forEach(c)}l={push:function(f){return c(f)},filter:function(){return l}}},assignMedium:function(c){s=!0;var p=[];if(l.length){var f=l;l=[],f.forEach(c),p=l}var m=function(){var w=p;p=[],w.forEach(c)},y=function(){return Promise.resolve().then(m)};y(),l={push:function(w){p.push(w),y()},filter:function(w){return p=p.filter(w),l}}}};return a}function a1(n){n===void 0&&(n={});var o=s1(null);return o.options=qt({async:!0,ssr:!1},n),o}var uh=function(n){var o=n.sideCar,l=ah(n,["sideCar"]);if(!o)throw new Error("Sidecar: please provide `sideCar` property to import the right car");var s=o.read();if(!s)throw new Error("Sidecar medium not found");return h.createElement(s,qt({},l))};uh.isSideCarExport=!0;function u1(n,o){return n.useMedium(o),uh}var ch=a1(),ou=function(){},Gi=h.forwardRef(function(n,o){var l=h.useRef(null),s=h.useState({onScrollCapture:ou,onWheelCapture:ou,onTouchMoveCapture:ou}),a=s[0],c=s[1],p=n.forwardProps,f=n.children,m=n.className,y=n.removeScrollBar,w=n.enabled,x=n.shards,S=n.sideCar,E=n.noRelative,P=n.noIsolation,C=n.inert,k=n.allowPinchZoom,N=n.as,j=N===void 0?"div":N,A=n.gapMode,O=ah(n,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noRelative","noIsolation","inert","allowPinchZoom","as","gapMode"]),I=S,B=l1([l,o]),D=qt(qt({},O),a);return h.createElement(h.Fragment,null,w&&h.createElement(I,{sideCar:ch,removeScrollBar:y,shards:x,noRelative:E,noIsolation:P,inert:C,setCallbacks:c,allowPinchZoom:!!k,lockRef:l,gapMode:A}),p?h.cloneElement(h.Children.only(f),qt(qt({},D),{ref:B})):h.createElement(j,qt({},D,{className:m,ref:B}),f))});Gi.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1};Gi.classNames={fullWidth:ji,zeroRight:Ti};var c1=function(){if(typeof __webpack_nonce__<"u")return __webpack_nonce__};function d1(){if(!document)return null;var n=document.createElement("style");n.type="text/css";var o=c1();return o&&n.setAttribute("nonce",o),n}function f1(n,o){n.styleSheet?n.styleSheet.cssText=o:n.appendChild(document.createTextNode(o))}function p1(n){var o=document.head||document.getElementsByTagName("head")[0];o.appendChild(n)}var m1=function(){var n=0,o=null;return{add:function(l){n==0&&(o=d1())&&(f1(o,l),p1(o)),n++},remove:function(){n--,!n&&o&&(o.parentNode&&o.parentNode.removeChild(o),o=null)}}},h1=function(){var n=m1();return function(o,l){h.useEffect(function(){return n.add(o),function(){n.remove()}},[o&&l])}},dh=function(){var n=h1(),o=function(l){var s=l.styles,a=l.dynamic;return n(s,a),null};return o},g1={left:0,top:0,right:0,gap:0},lu=function(n){return parseInt(n||"",10)||0},v1=function(n){var o=window.getComputedStyle(document.body),l=o[n==="padding"?"paddingLeft":"marginLeft"],s=o[n==="padding"?"paddingTop":"marginTop"],a=o[n==="padding"?"paddingRight":"marginRight"];return[lu(l),lu(s),lu(a)]},y1=function(n){if(n===void 0&&(n="margin"),typeof window>"u")return g1;var o=v1(n),l=document.documentElement.clientWidth,s=window.innerWidth;return{left:o[0],top:o[1],right:o[2],gap:Math.max(0,s-l+o[2]-o[0])}},x1=dh(),qr="data-scroll-locked",w1=function(n,o,l,s){var a=n.left,c=n.top,p=n.right,f=n.gap;return l===void 0&&(l="margin"),`
  .`.concat(t1,` {
   overflow: hidden `).concat(s,`;
   padding-right: `).concat(f,"px ").concat(s,`;
  }
  body[`).concat(qr,`] {
    overflow: hidden `).concat(s,`;
    overscroll-behavior: contain;
    `).concat([o&&"position: relative ".concat(s,";"),l==="margin"&&`
    padding-left: `.concat(a,`px;
    padding-top: `).concat(c,`px;
    padding-right: `).concat(p,`px;
    margin-left:0;
    margin-top:0;
    margin-right: `).concat(f,"px ").concat(s,`;
    `),l==="padding"&&"padding-right: ".concat(f,"px ").concat(s,";")].filter(Boolean).join(""),`
  }
  
  .`).concat(Ti,` {
    right: `).concat(f,"px ").concat(s,`;
  }
  
  .`).concat(ji,` {
    margin-right: `).concat(f,"px ").concat(s,`;
  }
  
  .`).concat(Ti," .").concat(Ti,` {
    right: 0 `).concat(s,`;
  }
  
  .`).concat(ji," .").concat(ji,` {
    margin-right: 0 `).concat(s,`;
  }
  
  body[`).concat(qr,`] {
    `).concat(n1,": ").concat(f,`px;
  }
`)},kp=function(){var n=parseInt(document.body.getAttribute(qr)||"0",10);return isFinite(n)?n:0},S1=function(){h.useEffect(function(){return document.body.setAttribute(qr,(kp()+1).toString()),function(){var n=kp()-1;n<=0?document.body.removeAttribute(qr):document.body.setAttribute(qr,n.toString())}},[])},C1=function(n){var o=n.noRelative,l=n.noImportant,s=n.gapMode,a=s===void 0?"margin":s;S1();var c=h.useMemo(function(){return y1(a)},[a]);return h.createElement(x1,{styles:w1(c,!o,a,l?"":"!important")})},gu=!1;if(typeof window<"u")try{var Pi=Object.defineProperty({},"passive",{get:function(){return gu=!0,!0}});window.addEventListener("test",Pi,Pi),window.removeEventListener("test",Pi,Pi)}catch{gu=!1}var Hr=gu?{passive:!1}:!1,E1=function(n){return n.tagName==="TEXTAREA"},fh=function(n,o){if(!(n instanceof Element))return!1;var l=window.getComputedStyle(n);return l[o]!=="hidden"&&!(l.overflowY===l.overflowX&&!E1(n)&&l[o]==="visible")},b1=function(n){return fh(n,"overflowY")},k1=function(n){return fh(n,"overflowX")},Pp=function(n,o){var l=o.ownerDocument,s=o;do{typeof ShadowRoot<"u"&&s instanceof ShadowRoot&&(s=s.host);var a=ph(n,s);if(a){var c=mh(n,s),p=c[1],f=c[2];if(p>f)return!0}s=s.parentNode}while(s&&s!==l.body);return!1},P1=function(n){var o=n.scrollTop,l=n.scrollHeight,s=n.clientHeight;return[o,l,s]},N1=function(n){var o=n.scrollLeft,l=n.scrollWidth,s=n.clientWidth;return[o,l,s]},ph=function(n,o){return n==="v"?b1(o):k1(o)},mh=function(n,o){return n==="v"?P1(o):N1(o)},R1=function(n,o){return n==="h"&&o==="rtl"?-1:1},_1=function(n,o,l,s,a){var c=R1(n,window.getComputedStyle(o).direction),p=c*s,f=l.target,m=o.contains(f),y=!1,w=p>0,x=0,S=0;do{if(!f)break;var E=mh(n,f),P=E[0],C=E[1],k=E[2],N=C-k-c*P;(P||N)&&ph(n,f)&&(x+=N,S+=P);var j=f.parentNode;f=j&&j.nodeType===Node.DOCUMENT_FRAGMENT_NODE?j.host:j}while(!m&&f!==document.body||m&&(o.contains(f)||o===f));return(w&&Math.abs(x)<1||!w&&Math.abs(S)<1)&&(y=!0),y},Ni=function(n){return"changedTouches"in n?[n.changedTouches[0].clientX,n.changedTouches[0].clientY]:[0,0]},Np=function(n){return[n.deltaX,n.deltaY]},Rp=function(n){return n&&"current"in n?n.current:n},T1=function(n,o){return n[0]===o[0]&&n[1]===o[1]},j1=function(n){return`
  .block-interactivity-`.concat(n,` {pointer-events: none;}
  .allow-interactivity-`).concat(n,` {pointer-events: all;}
`)},z1=0,Br=[];function A1(n){var o=h.useRef([]),l=h.useRef([0,0]),s=h.useRef(),a=h.useState(z1++)[0],c=h.useState(dh)[0],p=h.useRef(n);h.useEffect(function(){p.current=n},[n]),h.useEffect(function(){if(n.inert){document.body.classList.add("block-interactivity-".concat(a));var C=e1([n.lockRef.current],(n.shards||[]).map(Rp),!0).filter(Boolean);return C.forEach(function(k){return k.classList.add("allow-interactivity-".concat(a))}),function(){document.body.classList.remove("block-interactivity-".concat(a)),C.forEach(function(k){return k.classList.remove("allow-interactivity-".concat(a))})}}},[n.inert,n.lockRef.current,n.shards]);var f=h.useCallback(function(C,k){if("touches"in C&&C.touches.length===2||C.type==="wheel"&&C.ctrlKey)return!p.current.allowPinchZoom;var N=Ni(C),j=l.current,A="deltaX"in C?C.deltaX:j[0]-N[0],O="deltaY"in C?C.deltaY:j[1]-N[1],I,B=C.target,D=Math.abs(A)>Math.abs(O)?"h":"v";if("touches"in C&&D==="h"&&B.type==="range")return!1;var oe=Pp(D,B);if(!oe)return!0;if(oe?I=D:(I=D==="v"?"h":"v",oe=Pp(D,B)),!oe)return!1;if(!s.current&&"changedTouches"in C&&(A||O)&&(s.current=I),!I)return!0;var re=s.current||I;return _1(re,k,C,re==="h"?A:O)},[]),m=h.useCallback(function(C){var k=C;if(!(!Br.length||Br[Br.length-1]!==c)){var N="deltaY"in k?Np(k):Ni(k),j=o.current.filter(function(I){return I.name===k.type&&(I.target===k.target||k.target===I.shadowParent)&&T1(I.delta,N)})[0];if(j&&j.should){k.cancelable&&k.preventDefault();return}if(!j){var A=(p.current.shards||[]).map(Rp).filter(Boolean).filter(function(I){return I.contains(k.target)}),O=A.length>0?f(k,A[0]):!p.current.noIsolation;O&&k.cancelable&&k.preventDefault()}}},[]),y=h.useCallback(function(C,k,N,j){var A={name:C,delta:k,target:N,should:j,shadowParent:L1(N)};o.current.push(A),setTimeout(function(){o.current=o.current.filter(function(O){return O!==A})},1)},[]),w=h.useCallback(function(C){l.current=Ni(C),s.current=void 0},[]),x=h.useCallback(function(C){y(C.type,Np(C),C.target,f(C,n.lockRef.current))},[]),S=h.useCallback(function(C){y(C.type,Ni(C),C.target,f(C,n.lockRef.current))},[]);h.useEffect(function(){return Br.push(c),n.setCallbacks({onScrollCapture:x,onWheelCapture:x,onTouchMoveCapture:S}),document.addEventListener("wheel",m,Hr),document.addEventListener("touchmove",m,Hr),document.addEventListener("touchstart",w,Hr),function(){Br=Br.filter(function(C){return C!==c}),document.removeEventListener("wheel",m,Hr),document.removeEventListener("touchmove",m,Hr),document.removeEventListener("touchstart",w,Hr)}},[]);var E=n.removeScrollBar,P=n.inert;return h.createElement(h.Fragment,null,P?h.createElement(c,{styles:j1(a)}):null,E?h.createElement(C1,{noRelative:n.noRelative,gapMode:n.gapMode}):null)}function L1(n){for(var o=null;n!==null;)n instanceof ShadowRoot&&(o=n.host,n=n.host),n=n.parentNode;return o}const M1=u1(ch,A1);var hh=h.forwardRef(function(n,o){return h.createElement(Gi,qt({},n,{ref:o,sideCar:M1}))});hh.classNames=Gi.classNames;var I1=[" ","Enter","ArrowUp","ArrowDown"],O1=[" ","Enter"],dl="Select",[Yi,Xi,D1]=cw(dl),[ao,nE]=zu(dl,[D1,Ym]),Zi=Ym(),[$1,Bn]=ao(dl),[F1,W1]=ao(dl),gh=n=>{const{__scopeSelect:o,children:l,open:s,defaultOpen:a,onOpenChange:c,value:p,defaultValue:f,onValueChange:m,dir:y,name:w,autoComplete:x,disabled:S,required:E,form:P}=n,C=Zi(o),[k,N]=h.useState(null),[j,A]=h.useState(null),[O,I]=h.useState(!1),B=fw(y),[D=!1,oe]=Ep({prop:s,defaultProp:a,onChange:c}),[re,ae]=Ep({prop:p,defaultProp:f,onChange:m}),me=h.useRef(null),Z=k?P||!!k.closest("form"):!0,[ge,se]=h.useState(new Set),ve=Array.from(ge).map(le=>le.props.value).join(";");return g.jsx(VS,{...C,children:g.jsxs($1,{required:E,scope:o,trigger:k,onTriggerChange:N,valueNode:j,onValueNodeChange:A,valueNodeHasChildren:O,onValueNodeHasChildrenChange:I,contentId:Au(),value:re,onValueChange:ae,open:D,onOpenChange:oe,dir:B,triggerPointerDownPosRef:me,disabled:S,children:[g.jsx(Yi.Provider,{scope:o,children:g.jsx(F1,{scope:n.__scopeSelect,onNativeOptionAdd:h.useCallback(le=>{se(de=>new Set(de).add(le))},[]),onNativeOptionRemove:h.useCallback(le=>{se(de=>{const $=new Set(de);return $.delete(le),$})},[]),children:l})}),Z?g.jsxs($h,{"aria-hidden":!0,required:E,tabIndex:-1,name:w,autoComplete:x,value:re,onChange:le=>ae(le.target.value),disabled:S,form:P,children:[re===void 0?g.jsx("option",{value:""}):null,Array.from(ge)]},ve):null]})})};gh.displayName=dl;var vh="SelectTrigger",yh=h.forwardRef((n,o)=>{const{__scopeSelect:l,disabled:s=!1,...a}=n,c=Zi(l),p=Bn(vh,l),f=p.disabled||s,m=et(o,p.onTriggerChange),y=Xi(l),w=h.useRef("touch"),[x,S,E]=Fh(C=>{const k=y().filter(A=>!A.disabled),N=k.find(A=>A.value===p.value),j=Wh(k,C,N);j!==void 0&&p.onValueChange(j.value)}),P=C=>{f||(p.onOpenChange(!0),E()),C&&(p.triggerPointerDownPosRef.current={x:Math.round(C.pageX),y:Math.round(C.pageY)})};return g.jsx(HS,{asChild:!0,...c,children:g.jsx(Be.button,{type:"button",role:"combobox","aria-controls":p.contentId,"aria-expanded":p.open,"aria-required":p.required,"aria-autocomplete":"none",dir:p.dir,"data-state":p.open?"open":"closed",disabled:f,"data-disabled":f?"":void 0,"data-placeholder":Dh(p.value)?"":void 0,...a,ref:m,onClick:He(a.onClick,C=>{C.currentTarget.focus(),w.current!=="mouse"&&P(C)}),onPointerDown:He(a.onPointerDown,C=>{w.current=C.pointerType;const k=C.target;k.hasPointerCapture(C.pointerId)&&k.releasePointerCapture(C.pointerId),C.button===0&&C.ctrlKey===!1&&C.pointerType==="mouse"&&(P(C),C.preventDefault())}),onKeyDown:He(a.onKeyDown,C=>{const k=x.current!=="";!(C.ctrlKey||C.altKey||C.metaKey)&&C.key.length===1&&S(C.key),!(k&&C.key===" ")&&I1.includes(C.key)&&(P(),C.preventDefault())})})})});yh.displayName=vh;var xh="SelectValue",wh=h.forwardRef((n,o)=>{const{__scopeSelect:l,className:s,style:a,children:c,placeholder:p="",...f}=n,m=Bn(xh,l),{onValueNodeHasChildrenChange:y}=m,w=c!==void 0,x=et(o,m.onValueNodeChange);return At(()=>{y(w)},[y,w]),g.jsx(Be.span,{...f,ref:x,style:{pointerEvents:"none"},children:Dh(m.value)?g.jsx(g.Fragment,{children:p}):c})});wh.displayName=xh;var V1="SelectIcon",Sh=h.forwardRef((n,o)=>{const{__scopeSelect:l,children:s,...a}=n;return g.jsx(Be.span,{"aria-hidden":!0,...a,ref:o,children:s||"▼"})});Sh.displayName=V1;var H1="SelectPortal",Ch=n=>g.jsx(oh,{asChild:!0,...n});Ch.displayName=H1;var hr="SelectContent",Eh=h.forwardRef((n,o)=>{const l=Bn(hr,n.__scopeSelect),[s,a]=h.useState();if(At(()=>{a(new DocumentFragment)},[]),!l.open){const c=s;return c?sl.createPortal(g.jsx(bh,{scope:n.__scopeSelect,children:g.jsx(Yi.Slot,{scope:n.__scopeSelect,children:g.jsx("div",{children:n.children})})}),c):null}return g.jsx(kh,{...n,ref:o})});Eh.displayName=hr;var Ht=10,[bh,Un]=ao(hr),B1="SelectContentImpl",kh=h.forwardRef((n,o)=>{const{__scopeSelect:l,position:s="item-aligned",onCloseAutoFocus:a,onEscapeKeyDown:c,onPointerDownOutside:p,side:f,sideOffset:m,align:y,alignOffset:w,arrowPadding:x,collisionBoundary:S,collisionPadding:E,sticky:P,hideWhenDetached:C,avoidCollisions:k,...N}=n,j=Bn(hr,l),[A,O]=h.useState(null),[I,B]=h.useState(null),D=et(o,F=>O(F)),[oe,re]=h.useState(null),[ae,me]=h.useState(null),Z=Xi(l),[ge,se]=h.useState(!1),ve=h.useRef(!1);h.useEffect(()=>{if(A)return JS(A)},[A]),Ew();const le=h.useCallback(F=>{const[H,...te]=Z().map(we=>we.ref.current),[J]=te.slice(-1),fe=document.activeElement;for(const we of F)if(we===fe||(we==null||we.scrollIntoView({block:"nearest"}),we===H&&I&&(I.scrollTop=0),we===J&&I&&(I.scrollTop=I.scrollHeight),we==null||we.focus(),document.activeElement!==fe))return},[Z,I]),de=h.useCallback(()=>le([oe,A]),[le,oe,A]);h.useEffect(()=>{ge&&de()},[ge,de]);const{onOpenChange:$,triggerPointerDownPosRef:q}=j;h.useEffect(()=>{if(A){let F={x:0,y:0};const H=J=>{var fe,we;F={x:Math.abs(Math.round(J.pageX)-(((fe=q.current)==null?void 0:fe.x)??0)),y:Math.abs(Math.round(J.pageY)-(((we=q.current)==null?void 0:we.y)??0))}},te=J=>{F.x<=10&&F.y<=10?J.preventDefault():A.contains(J.target)||$(!1),document.removeEventListener("pointermove",H),q.current=null};return q.current!==null&&(document.addEventListener("pointermove",H),document.addEventListener("pointerup",te,{capture:!0,once:!0})),()=>{document.removeEventListener("pointermove",H),document.removeEventListener("pointerup",te,{capture:!0})}}},[A,$,q]),h.useEffect(()=>{const F=()=>$(!1);return window.addEventListener("blur",F),window.addEventListener("resize",F),()=>{window.removeEventListener("blur",F),window.removeEventListener("resize",F)}},[$]);const[G,_]=Fh(F=>{const H=Z().filter(fe=>!fe.disabled),te=H.find(fe=>fe.ref.current===document.activeElement),J=Wh(H,F,te);J&&setTimeout(()=>J.ref.current.focus())}),W=h.useCallback((F,H,te)=>{const J=!ve.current&&!te;(j.value!==void 0&&j.value===H||J)&&(re(F),J&&(ve.current=!0))},[j.value]),V=h.useCallback(()=>A==null?void 0:A.focus(),[A]),Y=h.useCallback((F,H,te)=>{const J=!ve.current&&!te;(j.value!==void 0&&j.value===H||J)&&me(F)},[j.value]),ne=s==="popper"?vu:Ph,ee=ne===vu?{side:f,sideOffset:m,align:y,alignOffset:w,arrowPadding:x,collisionBoundary:S,collisionPadding:E,sticky:P,hideWhenDetached:C,avoidCollisions:k}:{};return g.jsx(bh,{scope:l,content:A,viewport:I,onViewportChange:B,itemRefCallback:W,selectedItem:oe,onItemLeave:V,itemTextRefCallback:Y,focusSelectedItem:de,selectedItemText:ae,position:s,isPositioned:ge,searchRef:G,children:g.jsx(hh,{as:fr,allowPinchZoom:!0,children:g.jsx(Mm,{asChild:!0,trapped:j.open,onMountAutoFocus:F=>{F.preventDefault()},onUnmountAutoFocus:He(a,F=>{var H;(H=j.trigger)==null||H.focus({preventScroll:!0}),F.preventDefault()}),children:g.jsx(Am,{asChild:!0,disableOutsidePointerEvents:!0,onEscapeKeyDown:c,onPointerDownOutside:p,onFocusOutside:F=>F.preventDefault(),onDismiss:()=>j.onOpenChange(!1),children:g.jsx(ne,{role:"listbox",id:j.contentId,"data-state":j.open?"open":"closed",dir:j.dir,onContextMenu:F=>F.preventDefault(),...N,...ee,onPlaced:()=>se(!0),ref:D,style:{display:"flex",flexDirection:"column",outline:"none",...N.style},onKeyDown:He(N.onKeyDown,F=>{const H=F.ctrlKey||F.altKey||F.metaKey;if(F.key==="Tab"&&F.preventDefault(),!H&&F.key.length===1&&_(F.key),["ArrowUp","ArrowDown","Home","End"].includes(F.key)){let J=Z().filter(fe=>!fe.disabled).map(fe=>fe.ref.current);if(["ArrowUp","End"].includes(F.key)&&(J=J.slice().reverse()),["ArrowUp","ArrowDown"].includes(F.key)){const fe=F.target,we=J.indexOf(fe);J=J.slice(we+1)}setTimeout(()=>le(J)),F.preventDefault()}})})})})})})});kh.displayName=B1;var U1="SelectItemAlignedPosition",Ph=h.forwardRef((n,o)=>{const{__scopeSelect:l,onPlaced:s,...a}=n,c=Bn(hr,l),p=Un(hr,l),[f,m]=h.useState(null),[y,w]=h.useState(null),x=et(o,D=>w(D)),S=Xi(l),E=h.useRef(!1),P=h.useRef(!0),{viewport:C,selectedItem:k,selectedItemText:N,focusSelectedItem:j}=p,A=h.useCallback(()=>{if(c.trigger&&c.valueNode&&f&&y&&C&&k&&N){const D=c.trigger.getBoundingClientRect(),oe=y.getBoundingClientRect(),re=c.valueNode.getBoundingClientRect(),ae=N.getBoundingClientRect();if(c.dir!=="rtl"){const fe=ae.left-oe.left,we=re.left-fe,Se=D.left-we,Pe=D.width+Se,tt=Math.max(Pe,oe.width),st=window.innerWidth-Ht,Ze=sp(we,[Ht,Math.max(Ht,st-tt)]);f.style.minWidth=Pe+"px",f.style.left=Ze+"px"}else{const fe=oe.right-ae.right,we=window.innerWidth-re.right-fe,Se=window.innerWidth-D.right-we,Pe=D.width+Se,tt=Math.max(Pe,oe.width),st=window.innerWidth-Ht,Ze=sp(we,[Ht,Math.max(Ht,st-tt)]);f.style.minWidth=Pe+"px",f.style.right=Ze+"px"}const me=S(),Z=window.innerHeight-Ht*2,ge=C.scrollHeight,se=window.getComputedStyle(y),ve=parseInt(se.borderTopWidth,10),le=parseInt(se.paddingTop,10),de=parseInt(se.borderBottomWidth,10),$=parseInt(se.paddingBottom,10),q=ve+le+ge+$+de,G=Math.min(k.offsetHeight*5,q),_=window.getComputedStyle(C),W=parseInt(_.paddingTop,10),V=parseInt(_.paddingBottom,10),Y=D.top+D.height/2-Ht,ne=Z-Y,ee=k.offsetHeight/2,F=k.offsetTop+ee,H=ve+le+F,te=q-H;if(H<=Y){const fe=me.length>0&&k===me[me.length-1].ref.current;f.style.bottom="0px";const we=y.clientHeight-C.offsetTop-C.offsetHeight,Se=Math.max(ne,ee+(fe?V:0)+we+de),Pe=H+Se;f.style.height=Pe+"px"}else{const fe=me.length>0&&k===me[0].ref.current;f.style.top="0px";const Se=Math.max(Y,ve+C.offsetTop+(fe?W:0)+ee)+te;f.style.height=Se+"px",C.scrollTop=H-Y+C.offsetTop}f.style.margin=`${Ht}px 0`,f.style.minHeight=G+"px",f.style.maxHeight=Z+"px",s==null||s(),requestAnimationFrame(()=>E.current=!0)}},[S,c.trigger,c.valueNode,f,y,C,k,N,c.dir,s]);At(()=>A(),[A]);const[O,I]=h.useState();At(()=>{y&&I(window.getComputedStyle(y).zIndex)},[y]);const B=h.useCallback(D=>{D&&P.current===!0&&(A(),j==null||j(),P.current=!1)},[A,j]);return g.jsx(Q1,{scope:l,contentWrapper:f,shouldExpandOnScrollRef:E,onScrollButtonChange:B,children:g.jsx("div",{ref:m,style:{display:"flex",flexDirection:"column",position:"fixed",zIndex:O},children:g.jsx(Be.div,{...a,ref:x,style:{boxSizing:"border-box",maxHeight:"100%",...a.style}})})})});Ph.displayName=U1;var K1="SelectPopperPosition",vu=h.forwardRef((n,o)=>{const{__scopeSelect:l,align:s="start",collisionPadding:a=Ht,...c}=n,p=Zi(l);return g.jsx(BS,{...p,...c,ref:o,align:s,collisionPadding:a,style:{boxSizing:"border-box",...c.style,"--radix-select-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-select-content-available-width":"var(--radix-popper-available-width)","--radix-select-content-available-height":"var(--radix-popper-available-height)","--radix-select-trigger-width":"var(--radix-popper-anchor-width)","--radix-select-trigger-height":"var(--radix-popper-anchor-height)"}})});vu.displayName=K1;var[Q1,Hu]=ao(hr,{}),yu="SelectViewport",Nh=h.forwardRef((n,o)=>{const{__scopeSelect:l,nonce:s,...a}=n,c=Un(yu,l),p=Hu(yu,l),f=et(o,c.onViewportChange),m=h.useRef(0);return g.jsxs(g.Fragment,{children:[g.jsx("style",{dangerouslySetInnerHTML:{__html:"[data-radix-select-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-select-viewport]::-webkit-scrollbar{display:none}"},nonce:s}),g.jsx(Yi.Slot,{scope:l,children:g.jsx(Be.div,{"data-radix-select-viewport":"",role:"presentation",...a,ref:f,style:{position:"relative",flex:1,overflow:"hidden auto",...a.style},onScroll:He(a.onScroll,y=>{const w=y.currentTarget,{contentWrapper:x,shouldExpandOnScrollRef:S}=p;if(S!=null&&S.current&&x){const E=Math.abs(m.current-w.scrollTop);if(E>0){const P=window.innerHeight-Ht*2,C=parseFloat(x.style.minHeight),k=parseFloat(x.style.height),N=Math.max(C,k);if(N<P){const j=N+E,A=Math.min(P,j),O=j-A;x.style.height=A+"px",x.style.bottom==="0px"&&(w.scrollTop=O>0?O:0,x.style.justifyContent="flex-end")}}}m.current=w.scrollTop})})})]})});Nh.displayName=yu;var Rh="SelectGroup",[G1,Y1]=ao(Rh),X1=h.forwardRef((n,o)=>{const{__scopeSelect:l,...s}=n,a=Au();return g.jsx(G1,{scope:l,id:a,children:g.jsx(Be.div,{role:"group","aria-labelledby":a,...s,ref:o})})});X1.displayName=Rh;var _h="SelectLabel",Z1=h.forwardRef((n,o)=>{const{__scopeSelect:l,...s}=n,a=Y1(_h,l);return g.jsx(Be.div,{id:a.id,...s,ref:o})});Z1.displayName=_h;var Di="SelectItem",[q1,Th]=ao(Di),jh=h.forwardRef((n,o)=>{const{__scopeSelect:l,value:s,disabled:a=!1,textValue:c,...p}=n,f=Bn(Di,l),m=Un(Di,l),y=f.value===s,[w,x]=h.useState(c??""),[S,E]=h.useState(!1),P=et(o,j=>{var A;return(A=m.itemRefCallback)==null?void 0:A.call(m,j,s,a)}),C=Au(),k=h.useRef("touch"),N=()=>{a||(f.onValueChange(s),f.onOpenChange(!1))};if(s==="")throw new Error("A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.");return g.jsx(q1,{scope:l,value:s,disabled:a,textId:C,isSelected:y,onItemTextChange:h.useCallback(j=>{x(A=>A||((j==null?void 0:j.textContent)??"").trim())},[]),children:g.jsx(Yi.ItemSlot,{scope:l,value:s,disabled:a,textValue:w,children:g.jsx(Be.div,{role:"option","aria-labelledby":C,"data-highlighted":S?"":void 0,"aria-selected":y&&S,"data-state":y?"checked":"unchecked","aria-disabled":a||void 0,"data-disabled":a?"":void 0,tabIndex:a?void 0:-1,...p,ref:P,onFocus:He(p.onFocus,()=>E(!0)),onBlur:He(p.onBlur,()=>E(!1)),onClick:He(p.onClick,()=>{k.current!=="mouse"&&N()}),onPointerUp:He(p.onPointerUp,()=>{k.current==="mouse"&&N()}),onPointerDown:He(p.onPointerDown,j=>{k.current=j.pointerType}),onPointerMove:He(p.onPointerMove,j=>{var A;k.current=j.pointerType,a?(A=m.onItemLeave)==null||A.call(m):k.current==="mouse"&&j.currentTarget.focus({preventScroll:!0})}),onPointerLeave:He(p.onPointerLeave,j=>{var A;j.currentTarget===document.activeElement&&((A=m.onItemLeave)==null||A.call(m))}),onKeyDown:He(p.onKeyDown,j=>{var O;((O=m.searchRef)==null?void 0:O.current)!==""&&j.key===" "||(O1.includes(j.key)&&N(),j.key===" "&&j.preventDefault())})})})})});jh.displayName=Di;var qo="SelectItemText",zh=h.forwardRef((n,o)=>{const{__scopeSelect:l,className:s,style:a,...c}=n,p=Bn(qo,l),f=Un(qo,l),m=Th(qo,l),y=W1(qo,l),[w,x]=h.useState(null),S=et(o,N=>x(N),m.onItemTextChange,N=>{var j;return(j=f.itemTextRefCallback)==null?void 0:j.call(f,N,m.value,m.disabled)}),E=w==null?void 0:w.textContent,P=h.useMemo(()=>g.jsx("option",{value:m.value,disabled:m.disabled,children:E},m.value),[m.disabled,m.value,E]),{onNativeOptionAdd:C,onNativeOptionRemove:k}=y;return At(()=>(C(P),()=>k(P)),[C,k,P]),g.jsxs(g.Fragment,{children:[g.jsx(Be.span,{id:m.textId,...c,ref:S}),m.isSelected&&p.valueNode&&!p.valueNodeHasChildren?sl.createPortal(c.children,p.valueNode):null]})});zh.displayName=qo;var Ah="SelectItemIndicator",Lh=h.forwardRef((n,o)=>{const{__scopeSelect:l,...s}=n;return Th(Ah,l).isSelected?g.jsx(Be.span,{"aria-hidden":!0,...s,ref:o}):null});Lh.displayName=Ah;var xu="SelectScrollUpButton",Mh=h.forwardRef((n,o)=>{const l=Un(xu,n.__scopeSelect),s=Hu(xu,n.__scopeSelect),[a,c]=h.useState(!1),p=et(o,s.onScrollButtonChange);return At(()=>{if(l.viewport&&l.isPositioned){let f=function(){const y=m.scrollTop>0;c(y)};const m=l.viewport;return f(),m.addEventListener("scroll",f),()=>m.removeEventListener("scroll",f)}},[l.viewport,l.isPositioned]),a?g.jsx(Oh,{...n,ref:p,onAutoScroll:()=>{const{viewport:f,selectedItem:m}=l;f&&m&&(f.scrollTop=f.scrollTop-m.offsetHeight)}}):null});Mh.displayName=xu;var wu="SelectScrollDownButton",Ih=h.forwardRef((n,o)=>{const l=Un(wu,n.__scopeSelect),s=Hu(wu,n.__scopeSelect),[a,c]=h.useState(!1),p=et(o,s.onScrollButtonChange);return At(()=>{if(l.viewport&&l.isPositioned){let f=function(){const y=m.scrollHeight-m.clientHeight,w=Math.ceil(m.scrollTop)<y;c(w)};const m=l.viewport;return f(),m.addEventListener("scroll",f),()=>m.removeEventListener("scroll",f)}},[l.viewport,l.isPositioned]),a?g.jsx(Oh,{...n,ref:p,onAutoScroll:()=>{const{viewport:f,selectedItem:m}=l;f&&m&&(f.scrollTop=f.scrollTop+m.offsetHeight)}}):null});Ih.displayName=wu;var Oh=h.forwardRef((n,o)=>{const{__scopeSelect:l,onAutoScroll:s,...a}=n,c=Un("SelectScrollButton",l),p=h.useRef(null),f=Xi(l),m=h.useCallback(()=>{p.current!==null&&(window.clearInterval(p.current),p.current=null)},[]);return h.useEffect(()=>()=>m(),[m]),At(()=>{var w;const y=f().find(x=>x.ref.current===document.activeElement);(w=y==null?void 0:y.ref.current)==null||w.scrollIntoView({block:"nearest"})},[f]),g.jsx(Be.div,{"aria-hidden":!0,...a,ref:o,style:{flexShrink:0,...a.style},onPointerDown:He(a.onPointerDown,()=>{p.current===null&&(p.current=window.setInterval(s,50))}),onPointerMove:He(a.onPointerMove,()=>{var y;(y=c.onItemLeave)==null||y.call(c),p.current===null&&(p.current=window.setInterval(s,50))}),onPointerLeave:He(a.onPointerLeave,()=>{m()})})}),J1="SelectSeparator",eC=h.forwardRef((n,o)=>{const{__scopeSelect:l,...s}=n;return g.jsx(Be.div,{"aria-hidden":!0,...s,ref:o})});eC.displayName=J1;var Su="SelectArrow",tC=h.forwardRef((n,o)=>{const{__scopeSelect:l,...s}=n,a=Zi(l),c=Bn(Su,l),p=Un(Su,l);return c.open&&p.position==="popper"?g.jsx(US,{...a,...s,ref:o}):null});tC.displayName=Su;function Dh(n){return n===""||n===void 0}var $h=h.forwardRef((n,o)=>{const{value:l,...s}=n,a=h.useRef(null),c=et(o,a),p=GS(l);return h.useEffect(()=>{const f=a.current,m=window.HTMLSelectElement.prototype,w=Object.getOwnPropertyDescriptor(m,"value").set;if(p!==l&&w){const x=new Event("change",{bubbles:!0});w.call(f,l),f.dispatchEvent(x)}},[p,l]),g.jsx(ih,{asChild:!0,children:g.jsx("select",{...s,ref:c,defaultValue:l})})});$h.displayName="BubbleSelect";function Fh(n){const o=pr(n),l=h.useRef(""),s=h.useRef(0),a=h.useCallback(p=>{const f=l.current+p;o(f),function m(y){l.current=y,window.clearTimeout(s.current),y!==""&&(s.current=window.setTimeout(()=>m(""),1e3))}(f)},[o]),c=h.useCallback(()=>{l.current="",window.clearTimeout(s.current)},[]);return h.useEffect(()=>()=>window.clearTimeout(s.current),[]),[l,a,c]}function Wh(n,o,l){const a=o.length>1&&Array.from(o).every(y=>y===o[0])?o[0]:o,c=l?n.indexOf(l):-1;let p=nC(n,Math.max(c,0));a.length===1&&(p=p.filter(y=>y!==l));const m=p.find(y=>y.textValue.toLowerCase().startsWith(a.toLowerCase()));return m!==l?m:void 0}function nC(n,o){return n.map((l,s)=>n[(o+s)%n.length])}var rC=gh,oC=yh,lC=wh,iC=Sh,sC=Ch,aC=Eh,uC=Nh,cC=jh,dC=zh,fC=Lh,pC=Mh,mC=Ih;function _p({...n}){return g.jsx(rC,{"data-slot":"select",...n})}function Tp({...n}){return g.jsx(lC,{"data-slot":"select-value",...n})}function jp({className:n,size:o="default",children:l,...s}){return g.jsxs(oC,{"data-slot":"select-trigger","data-size":o,className:De("border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-full items-center justify-between gap-2 rounded-md border bg-input-background px-3 py-2 text-sm whitespace-nowrap transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",n),...s,children:[l,g.jsx(iC,{asChild:!0,children:g.jsx(_u,{className:"size-4 opacity-50"})})]})}function zp({className:n,children:o,position:l="popper",...s}){return g.jsx(sC,{children:g.jsxs(aC,{"data-slot":"select-content",className:De("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md",l==="popper"&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",n),position:l,...s,children:[g.jsx(hC,{}),g.jsx(uC,{className:De("p-1",l==="popper"&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1"),children:o}),g.jsx(gC,{})]})})}function Ap({className:n,children:o,...l}){return g.jsxs(cC,{"data-slot":"select-item",className:De("focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2",n),...l,children:[g.jsx("span",{className:"absolute right-2 flex size-3.5 items-center justify-center",children:g.jsx(fC,{children:g.jsx(yx,{className:"size-4"})})}),g.jsx(dC,{children:o})]})}function hC({className:n,...o}){return g.jsx(pC,{"data-slot":"select-scroll-up-button",className:De("flex cursor-default items-center justify-center py-1",n),...o,children:g.jsx(Sx,{className:"size-4"})})}function gC({className:n,...o}){return g.jsx(mC,{"data-slot":"select-scroll-down-button",className:De("flex cursor-default items-center justify-center py-1",n),...o,children:g.jsx(_u,{className:"size-4"})})}const Dn=n=>({ko:"한국어",en:"English",ja:"日本語",zh:"中文",auto:"자동 감지"})[n]||n,vC=(n,o)=>{var a;return((a={en:{"분산 캐싱 구현 일정은 어떻게 되나요?":"What is the implementation schedule for distributed caching?","데이터베이스 샤딩 비용은 얼마나 들까요?":"How much would database sharding cost?","현재 시스템 모니터링 도구는 무엇을 사용하고 있나요?":"What system monitoring tools are currently being used?",성능:"performance",최적화:"optimization",데이터베이스:"database",캐시:"cache",모니터링:"monitoring","언제까지 완료될 예정인가요?":"When is it expected to be completed?","비용은 어느 정도 예상하시나요?":"How much cost do you estimate?"},ko:{"What is the implementation schedule for distributed caching?":"분산 캐싱 구현 일정은 어떻게 되나요?","How much would database sharding cost?":"데이터베이스 샤딩 비용은 얼마나 들까요?","What system monitoring tools are currently being used?":"현재 시스템 모니터링 도구는 무엇을 사용하고 있나요?",performance:"성능",optimization:"최적화",database:"데이터베이스",cache:"캐시",monitoring:"모니터링","When is it expected to be completed?":"언제까지 완료될 예정인가요?","How much cost do you estimate?":"비용은 어느 정도 예상하시나요?"},ja:{"분산 캐싱 구현 일정은 어떻게 되나요?":"分散キャッシングの実装スケジュールはどうなっていますか？","데이터베이스 샤딩 비용은 얼마나 들까요?":"データベースシャーディングのコストはどのくらいかかりますか？","현재 시스템 모니터링 도구는 무엇을 사용하고 있나요?":"現在のシステム監視ツールは何を使用していますか？",성능:"パフォーマンス",최적화:"最適化",데이터베이스:"データベース",캐시:"キャッシュ",모니터링:"モニタリング"},zh:{"분산 캐싱 구현 일정은 어떻게 되나요?":"分布式缓存实施进度如何？","데이터베이스 샤딩 비용은 얼마나 들까요?":"数据库分片的成本是多少？","현재 시스템 모니터링 도구는 무엇을 사용하고 있나요?":"目前使用什么系统监控工具？",성능:"性能",최적화:"优化",데이터베이스:"数据库",캐시:"缓存",모니터링:"监控"}}[o==="auto"?"en":o])==null?void 0:a[n])||n};function yC({onQuestionSubmit:n,suggestedQuestions:o=[],sourceLang:l,targetLang:s}){const[a,c]=h.useState(""),[p,f]=h.useState(!1),m=h.useRef(null),y=h.useMemo(()=>{if(!a.trim())return o.slice(0,3);const I=a.toLowerCase().split(" ").filter(B=>B.length>0);return o.filter(B=>I.some(D=>B.toLowerCase().includes(D))).slice(0,3)},[a,o]),[w,x]=h.useState(""),[S,E]=h.useState(""),P=h.useMemo(()=>{if(!a.trim())return w;const I=vC(a,l);return x(I),I},[a,l,w]),C=I=>{const B=I.target.value;c(B)},k=I=>{c(I)},N=()=>{a.trim()&&(E(a),f(!0),c(""))},j=()=>{f(!1)},A=I=>{if((I.metaKey||I.ctrlKey)&&I.key==="a"){I.preventDefault(),m.current&&m.current.select();return}I.key==="Enter"&&(I.preventDefault(),p?j():a.trim()&&N())},O=l==="auto"?"en":l;return g.jsxs("div",{className:"relative space-y-3",children:[g.jsxs("div",{className:"flex items-center gap-2",children:[g.jsx(_x,{size:16,className:"text-amber-500"}),g.jsx("h3",{className:"text-sm",children:"질문 도우미"}),g.jsxs(du,{variant:"outline",className:"text-xs bg-gray-100 text-gray-600",children:[Dn(s)," → ",Dn(O)]})]}),g.jsxs("div",{className:"space-y-2",children:[g.jsxs("div",{className:"flex gap-2",children:[g.jsx(Xr,{ref:m,value:a,onChange:C,onKeyDown:A,placeholder:`질문을 ${Dn(s)}로 입력하세요...`,className:"text-sm flex-1"}),g.jsxs(zt,{onClick:N,disabled:!a.trim(),variant:"outline",size:"sm",className:"shrink-0",children:[g.jsx(cu,{size:14,className:"mr-1"}),"번역"]})]}),g.jsxs("p",{className:"text-xs text-gray-500",children:[Dn(s),"로 입력하면 ",Dn(O),"로 번역해드립니다"]})]}),y.length>0&&g.jsxs("div",{className:"space-y-2",children:[g.jsx("p",{className:"text-xs text-gray-600",children:"추천 질문:"}),g.jsx("div",{className:"space-y-1",children:y.map((I,B)=>g.jsx("button",{onClick:()=>k(I),className:"w-full text-left p-2 text-xs bg-gray-50 hover:bg-gray-100 rounded border text-gray-700 transition-colors",children:I},B))})]}),p&&g.jsx("div",{className:"absolute inset-0 z-10 bg-white/95 backdrop-blur-sm rounded-lg border shadow-lg animate-in fade-in-0 duration-200",tabIndex:0,onKeyDown:I=>{(I.key==="Enter"||I.key==="Escape")&&(I.preventDefault(),j())},children:g.jsx(Tu,{className:"h-full bg-green-50 border-green-200 shadow-sm",children:g.jsxs(ju,{className:"p-4 h-full flex flex-col",children:[g.jsxs("div",{className:"flex items-center justify-between mb-3",children:[g.jsxs(du,{variant:"outline",className:"text-xs bg-green-100 text-green-700 border-green-300",children:[Dn(O),"로 번역됨"]}),g.jsx(zt,{variant:"ghost",size:"sm",onClick:j,className:"h-6 w-6 p-0 hover:bg-green-200 text-green-600",children:g.jsx(Wx,{size:14})})]}),g.jsxs("div",{className:"flex-1 flex flex-col justify-center space-y-3",children:[g.jsxs("div",{className:"text-center",children:[g.jsxs("p",{className:"text-xs text-green-500 opacity-70 mb-1",children:["원문 (",Dn(s),")"]}),g.jsx("p",{className:"text-xs text-green-600 opacity-80 leading-relaxed whitespace-normal break-words",children:S})]}),g.jsxs("div",{className:"flex items-center gap-2 px-4",children:[g.jsx("div",{className:"flex-1 h-px bg-green-200"}),g.jsx(cu,{size:12,className:"text-green-400"}),g.jsx("div",{className:"flex-1 h-px bg-green-200"})]}),g.jsxs("div",{className:"text-center",children:[g.jsxs("p",{className:"text-xs text-green-500 opacity-70 mb-1",children:["번역 (",Dn(O),")"]}),g.jsx("p",{className:"text-sm text-green-800 leading-relaxed whitespace-normal break-words",children:P})]})]}),g.jsx("div",{className:"text-center mt-3",children:g.jsx("p",{className:"text-xs text-green-600 opacity-75",children:"Enter 또는 ESC 키를 눌러 닫기"})})]})})})]})}function xC({onClearAll:n,onAddItem:o,onAddTranscription:l,onAddTranslation:s}){const[a,c]=h.useState(""),p=()=>{console.log("Debug: 전체 아이템 지우기"),n==null||n()},f=()=>{console.log("Debug: 빈 아이템 추가"),o==null||o(a)},m=()=>{console.log("Debug: 전사 추가 -",a),l==null||l(a)},y=()=>{console.log("Debug: 번역 추가 -",a),s==null||s(a)};return g.jsx(Tu,{className:"border-orange-200 bg-orange-50/50 shadow-sm",children:g.jsxs(ju,{className:"p-2 space-y-1",children:[g.jsx(Xr,{value:a,onChange:w=>c(w.target.value),placeholder:"테스트 텍스트 입력...",className:"border-orange-200 focus:border-orange-400 bg-white h-7 text-xs"}),g.jsxs("div",{className:"grid grid-cols-2 gap-1",children:[g.jsxs(zt,{onClick:p,variant:"destructive",size:"sm",className:"text-xs h-6 bg-red-500 hover:bg-red-600 px-2",children:[g.jsx(Tm,{size:10,className:"mr-1"}),"전체 지우기"]}),g.jsxs(zt,{onClick:f,variant:"outline",size:"sm",className:"text-xs h-6 border-orange-300 text-orange-700 hover:bg-orange-100 px-2",children:[g.jsx(_m,{size:10,className:"mr-1"}),"아이템 추가"]}),g.jsxs(zt,{onClick:m,disabled:!a.trim(),variant:"outline",size:"sm",className:"text-xs h-6 border-blue-300 text-blue-700 hover:bg-blue-100 px-2",children:[g.jsx(bx,{size:10,className:"mr-1"}),"전사 추가"]}),g.jsxs(zt,{onClick:y,disabled:!a.trim(),variant:"outline",size:"sm",className:"text-xs h-6 border-green-300 text-green-700 hover:bg-green-100 px-2",children:[g.jsx(cu,{size:10,className:"mr-1"}),"번역 추가"]})]})]})})}const Lp=[{value:"ko",label:"한국어"},{value:"en",label:"English"},{value:"ja",label:"日본語"},{value:"zh",label:"中文"}];function Mp({isRecording:n,onRecordingToggle:o,sourceLang:l,targetLang:s,onSourceLangChange:a,onTargetLangChange:c,isOnlineMode:p,onModeToggle:f,suggestedQuestions:m=[],isQuestionHelperVisible:y,onQuestionHelperToggle:w,isDebugMode:x,onDebugModeToggle:S,isSummaryDebugMode:E,onSummaryDebugModeToggle:P,onClearAllTranslations:C,onAddItem:k,onAddTranscription:N,onAddTranslation:j}){const A=()=>{a(s),c(l)},O=I=>{console.log("Question for reference:",I)};return g.jsxs("div",{className:"border-t bg-gray-50 p-4 space-y-4 shrink-0",children:[g.jsxs("div",{className:"bg-white rounded-lg p-4 border border-gray-200 shadow-sm",children:[g.jsxs("div",{className:"flex items-center justify-between mb-4",children:[g.jsxs("div",{className:"flex items-center gap-3",children:[g.jsx(We,{className:n?"text-sm text-muted-foreground":"text-sm",children:"Source:"}),g.jsxs(_p,{value:l,onValueChange:a,disabled:n,children:[g.jsx(jp,{className:"w-32",children:g.jsx(Tp,{})}),g.jsx(zp,{children:Lp.map(I=>g.jsx(Ap,{value:I.value,children:I.label},I.value))})]})]}),g.jsxs("div",{className:"flex items-center gap-4",children:[g.jsxs(zt,{onClick:o,variant:n?"destructive":"default",size:"lg",className:"relative shrink-0 h-14 w-14 rounded-full shadow-lg",children:[n?g.jsx(Ax,{size:24}):g.jsx(Mx,{size:24}),n&&g.jsx("span",{className:"absolute -top-1 -right-1 w-4 h-4 bg-red-500 rounded-full animate-pulse"})]}),g.jsx(zt,{onClick:A,variant:"outline",size:"sm",disabled:n,className:"shrink-0 h-8 w-8 rounded-full p-0",title:"언어 전환",children:g.jsx(Dx,{size:14})})]}),g.jsxs("div",{className:"flex items-center gap-3",children:[g.jsx(We,{className:n?"text-sm text-muted-foreground":"text-sm",children:"Target:"}),g.jsxs(_p,{value:s,onValueChange:c,disabled:n,children:[g.jsx(jp,{className:"w-32",children:g.jsx(Tp,{})}),g.jsx(zp,{children:Lp.map(I=>g.jsx(Ap,{value:I.value,children:I.label},I.value))})]})]})]}),g.jsx("div",{className:"flex justify-center pt-3 border-t border-gray-100",children:g.jsxs("div",{className:"flex items-center gap-3",children:[g.jsx(We,{className:"text-sm",children:"회의 모드:"}),g.jsxs("div",{className:"flex items-center gap-2",children:[g.jsx(We,{className:"text-xs text-muted-foreground",children:"오프라인"}),g.jsx(tl,{checked:p,onCheckedChange:f}),g.jsx(We,{className:"text-xs text-muted-foreground",children:"온라인"})]})]})})]}),y&&g.jsx("div",{className:"bg-white rounded-lg p-4 border border-gray-200 shadow-sm",children:g.jsx(yC,{onQuestionSubmit:O,suggestedQuestions:m,sourceLang:l,targetLang:s})}),g.jsx("div",{className:"bg-white rounded-lg px-4 py-2 border border-gray-200 shadow-sm",children:g.jsxs("div",{className:"flex items-center justify-center gap-6",children:[g.jsx(gx,{size:18,className:"text-muted-foreground"}),g.jsxs("div",{className:"flex items-center gap-2",children:[g.jsx(We,{className:"text-sm text-muted-foreground",children:"실시간 통역"}),g.jsx(tl,{checked:x,onCheckedChange:S,className:"data-[state=checked]:bg-orange-600"})]}),g.jsxs("div",{className:"flex items-center gap-2",children:[g.jsx(We,{className:"text-sm text-muted-foreground",children:"요약패널"}),g.jsx(tl,{checked:E,onCheckedChange:P,className:"data-[state=checked]:bg-blue-600"})]})]})}),x&&g.jsx("div",{className:"animate-in slide-in-from-top-2 duration-300",children:g.jsx(xC,{onClearAll:C,onAddItem:k,onAddTranscription:N,onAddTranslation:j})})]})}const{createElement:ro,createContext:wC,forwardRef:Vh,useCallback:gt,useContext:Hh,useEffect:cr,useImperativeHandle:Bh,useLayoutEffect:SC,useMemo:CC,useRef:vt,useState:Jr}=Wi,Ip=Wi[`useId${Math.random()}`.slice(0,5)],EC=SC,qi=wC(null);qi.displayName="PanelGroupContext";const dr=EC,bC=typeof Ip=="function"?Ip:()=>null;let kC=0;function Bu(n=null){const o=bC(),l=vt(n||o||null);return l.current===null&&(l.current=""+kC++),n??l.current}function Uh({children:n,className:o="",collapsedSize:l,collapsible:s,defaultSize:a,forwardedRef:c,id:p,maxSize:f,minSize:m,onCollapse:y,onExpand:w,onResize:x,order:S,style:E,tagName:P="div",...C}){const k=Hh(qi);if(k===null)throw Error("Panel components must be rendered within a PanelGroup container");const{collapsePanel:N,expandPanel:j,getPanelSize:A,getPanelStyle:O,groupId:I,isPanelCollapsed:B,reevaluatePanelConstraints:D,registerPanel:oe,resizePanel:re,unregisterPanel:ae}=k,me=Bu(p),Z=vt({callbacks:{onCollapse:y,onExpand:w,onResize:x},constraints:{collapsedSize:l,collapsible:s,defaultSize:a,maxSize:f,minSize:m},id:me,idIsFromProps:p!==void 0,order:S});vt({didLogMissingDefaultSizeWarning:!1}),dr(()=>{const{callbacks:se,constraints:ve}=Z.current,le={...ve};Z.current.id=me,Z.current.idIsFromProps=p!==void 0,Z.current.order=S,se.onCollapse=y,se.onExpand=w,se.onResize=x,ve.collapsedSize=l,ve.collapsible=s,ve.defaultSize=a,ve.maxSize=f,ve.minSize=m,(le.collapsedSize!==ve.collapsedSize||le.collapsible!==ve.collapsible||le.maxSize!==ve.maxSize||le.minSize!==ve.minSize)&&D(Z.current,le)}),dr(()=>{const se=Z.current;return oe(se),()=>{ae(se)}},[S,me,oe,ae]),Bh(c,()=>({collapse:()=>{N(Z.current)},expand:se=>{j(Z.current,se)},getId(){return me},getSize(){return A(Z.current)},isCollapsed(){return B(Z.current)},isExpanded(){return!B(Z.current)},resize:se=>{re(Z.current,se)}}),[N,j,A,B,me,re]);const ge=O(Z.current,a);return ro(P,{...C,children:n,className:o,id:p,style:{...ge,...E},"data-panel":"","data-panel-collapsible":s||void 0,"data-panel-group-id":I,"data-panel-id":me,"data-panel-size":parseFloat(""+ge.flexGrow).toFixed(1)})}const Kh=Vh((n,o)=>ro(Uh,{...n,forwardedRef:o}));Uh.displayName="Panel";Kh.displayName="forwardRef(Panel)";let Cu=null,ar=null;function PC(n,o){if(o){const l=(o&Zh)!==0,s=(o&qh)!==0,a=(o&Jh)!==0,c=(o&eg)!==0;if(l)return a?"se-resize":c?"ne-resize":"e-resize";if(s)return a?"sw-resize":c?"nw-resize":"w-resize";if(a)return"s-resize";if(c)return"n-resize"}switch(n){case"horizontal":return"ew-resize";case"intersection":return"move";case"vertical":return"ns-resize"}}function NC(){ar!==null&&(document.head.removeChild(ar),Cu=null,ar=null)}function iu(n,o){const l=PC(n,o);Cu!==l&&(Cu=l,ar===null&&(ar=document.createElement("style"),document.head.appendChild(ar)),ar.innerHTML=`*{cursor: ${l}!important;}`)}function Qh(n){return n.type==="keydown"}function Gh(n){return n.type.startsWith("pointer")}function Yh(n){return n.type.startsWith("mouse")}function Ji(n){if(Gh(n)){if(n.isPrimary)return{x:n.clientX,y:n.clientY}}else if(Yh(n))return{x:n.clientX,y:n.clientY};return{x:1/0,y:1/0}}function RC(){if(typeof matchMedia=="function")return matchMedia("(pointer:coarse)").matches?"coarse":"fine"}function _C(n,o,l){return n.x<o.x+o.width&&n.x+n.width>o.x&&n.y<o.y+o.height&&n.y+n.height>o.y}function TC(n,o){if(n===o)throw new Error("Cannot compare node with itself");const l={a:$p(n),b:$p(o)};let s;for(;l.a.at(-1)===l.b.at(-1);)n=l.a.pop(),o=l.b.pop(),s=n;be(s,"Stacking order can only be calculated for elements with a common ancestor");const a={a:Dp(Op(l.a)),b:Dp(Op(l.b))};if(a.a===a.b){const c=s.childNodes,p={a:l.a.at(-1),b:l.b.at(-1)};let f=c.length;for(;f--;){const m=c[f];if(m===p.a)return 1;if(m===p.b)return-1}}return Math.sign(a.a-a.b)}const jC=/\b(?:position|zIndex|opacity|transform|webkitTransform|mixBlendMode|filter|webkitFilter|isolation)\b/;function zC(n){var o;const l=getComputedStyle((o=Xh(n))!==null&&o!==void 0?o:n).display;return l==="flex"||l==="inline-flex"}function AC(n){const o=getComputedStyle(n);return!!(o.position==="fixed"||o.zIndex!=="auto"&&(o.position!=="static"||zC(n))||+o.opacity<1||"transform"in o&&o.transform!=="none"||"webkitTransform"in o&&o.webkitTransform!=="none"||"mixBlendMode"in o&&o.mixBlendMode!=="normal"||"filter"in o&&o.filter!=="none"||"webkitFilter"in o&&o.webkitFilter!=="none"||"isolation"in o&&o.isolation==="isolate"||jC.test(o.willChange)||o.webkitOverflowScrolling==="touch")}function Op(n){let o=n.length;for(;o--;){const l=n[o];if(be(l,"Missing node"),AC(l))return l}return null}function Dp(n){return n&&Number(getComputedStyle(n).zIndex)||0}function $p(n){const o=[];for(;n;)o.push(n),n=Xh(n);return o}function Xh(n){const{parentNode:o}=n;return o&&o instanceof ShadowRoot?o.host:o}const Zh=1,qh=2,Jh=4,eg=8,LC=RC()==="coarse";let Bt=[],eo=!1,Wn=new Map,es=new Map;const ll=new Set;function MC(n,o,l,s,a){var c;const{ownerDocument:p}=o,f={direction:l,element:o,hitAreaMargins:s,setResizeHandlerState:a},m=(c=Wn.get(p))!==null&&c!==void 0?c:0;return Wn.set(p,m+1),ll.add(f),$i(),function(){var w;es.delete(n),ll.delete(f);const x=(w=Wn.get(p))!==null&&w!==void 0?w:1;if(Wn.set(p,x-1),$i(),x===1&&Wn.delete(p),Bt.includes(f)){const S=Bt.indexOf(f);S>=0&&Bt.splice(S,1),Ku(),a("up",!0,null)}}}function Fp(n){const{target:o}=n,{x:l,y:s}=Ji(n);eo=!0,Uu({target:o,x:l,y:s}),$i(),Bt.length>0&&(Fi("down",n),n.preventDefault(),n.stopPropagation())}function Zo(n){const{x:o,y:l}=Ji(n);if(eo&&n.buttons===0&&(eo=!1,Fi("up",n)),!eo){const{target:s}=n;Uu({target:s,x:o,y:l})}Fi("move",n),Ku(),Bt.length>0&&n.preventDefault()}function Ur(n){const{target:o}=n,{x:l,y:s}=Ji(n);es.clear(),eo=!1,Bt.length>0&&n.preventDefault(),Fi("up",n),Uu({target:o,x:l,y:s}),Ku(),$i()}function Uu({target:n,x:o,y:l}){Bt.splice(0);let s=null;(n instanceof HTMLElement||n instanceof SVGElement)&&(s=n),ll.forEach(a=>{const{element:c,hitAreaMargins:p}=a,f=c.getBoundingClientRect(),{bottom:m,left:y,right:w,top:x}=f,S=LC?p.coarse:p.fine;if(o>=y-S&&o<=w+S&&l>=x-S&&l<=m+S){if(s!==null&&document.contains(s)&&c!==s&&!c.contains(s)&&!s.contains(c)&&TC(s,c)>0){let P=s,C=!1;for(;P&&!P.contains(c);){if(_C(P.getBoundingClientRect(),f)){C=!0;break}P=P.parentElement}if(C)return}Bt.push(a)}})}function su(n,o){es.set(n,o)}function Ku(){let n=!1,o=!1;Bt.forEach(s=>{const{direction:a}=s;a==="horizontal"?n=!0:o=!0});let l=0;es.forEach(s=>{l|=s}),n&&o?iu("intersection",l):n?iu("horizontal",l):o?iu("vertical",l):NC()}function $i(){Wn.forEach((n,o)=>{const{body:l}=o;l.removeEventListener("contextmenu",Ur),l.removeEventListener("pointerdown",Fp),l.removeEventListener("pointerleave",Zo),l.removeEventListener("pointermove",Zo)}),window.removeEventListener("pointerup",Ur),window.removeEventListener("pointercancel",Ur),ll.size>0&&(eo?(Bt.length>0&&Wn.forEach((n,o)=>{const{body:l}=o;n>0&&(l.addEventListener("contextmenu",Ur),l.addEventListener("pointerleave",Zo),l.addEventListener("pointermove",Zo))}),window.addEventListener("pointerup",Ur),window.addEventListener("pointercancel",Ur)):Wn.forEach((n,o)=>{const{body:l}=o;n>0&&(l.addEventListener("pointerdown",Fp,{capture:!0}),l.addEventListener("pointermove",Zo))}))}function Fi(n,o){ll.forEach(l=>{const{setResizeHandlerState:s}=l,a=Bt.includes(l);s(n,a,o)})}function IC(){const[n,o]=Jr(0);return gt(()=>o(l=>l+1),[])}function be(n,o){if(!n)throw console.error(o),Error(o)}const Qu=10;function gr(n,o,l=Qu){return n.toFixed(l)===o.toFixed(l)?0:n>o?1:-1}function mn(n,o,l=Qu){return gr(n,o,l)===0}function Ct(n,o,l){return gr(n,o,l)===0}function OC(n,o,l){if(n.length!==o.length)return!1;for(let s=0;s<n.length;s++){const a=n[s],c=o[s];if(!Ct(a,c,l))return!1}return!0}function Gr({panelConstraints:n,panelIndex:o,size:l}){const s=n[o];be(s!=null,`Panel constraints not found for index ${o}`);let{collapsedSize:a=0,collapsible:c,maxSize:p=100,minSize:f=0}=s;if(gr(l,f)<0)if(c){const m=(a+f)/2;gr(l,m)<0?l=a:l=f}else l=f;return l=Math.min(p,l),l=parseFloat(l.toFixed(Qu)),l}function Jo({delta:n,initialLayout:o,panelConstraints:l,pivotIndices:s,prevLayout:a,trigger:c}){if(Ct(n,0))return o;const p=[...o],[f,m]=s;be(f!=null,"Invalid first pivot index"),be(m!=null,"Invalid second pivot index");let y=0;if(c==="keyboard"){{const x=n<0?m:f,S=l[x];be(S,`Panel constraints not found for index ${x}`);const{collapsedSize:E=0,collapsible:P,minSize:C=0}=S;if(P){const k=o[x];if(be(k!=null,`Previous layout not found for panel index ${x}`),Ct(k,E)){const N=C-k;gr(N,Math.abs(n))>0&&(n=n<0?0-N:N)}}}{const x=n<0?f:m,S=l[x];be(S,`No panel constraints found for index ${x}`);const{collapsedSize:E=0,collapsible:P,minSize:C=0}=S;if(P){const k=o[x];if(be(k!=null,`Previous layout not found for panel index ${x}`),Ct(k,C)){const N=k-E;gr(N,Math.abs(n))>0&&(n=n<0?0-N:N)}}}}{const x=n<0?1:-1;let S=n<0?m:f,E=0;for(;;){const C=o[S];be(C!=null,`Previous layout not found for panel index ${S}`);const N=Gr({panelConstraints:l,panelIndex:S,size:100})-C;if(E+=N,S+=x,S<0||S>=l.length)break}const P=Math.min(Math.abs(n),Math.abs(E));n=n<0?0-P:P}{let S=n<0?f:m;for(;S>=0&&S<l.length;){const E=Math.abs(n)-Math.abs(y),P=o[S];be(P!=null,`Previous layout not found for panel index ${S}`);const C=P-E,k=Gr({panelConstraints:l,panelIndex:S,size:C});if(!Ct(P,k)&&(y+=P-k,p[S]=k,y.toPrecision(3).localeCompare(Math.abs(n).toPrecision(3),void 0,{numeric:!0})>=0))break;n<0?S--:S++}}if(OC(a,p))return a;{const x=n<0?m:f,S=o[x];be(S!=null,`Previous layout not found for panel index ${x}`);const E=S+y,P=Gr({panelConstraints:l,panelIndex:x,size:E});if(p[x]=P,!Ct(P,E)){let C=E-P,N=n<0?m:f;for(;N>=0&&N<l.length;){const j=p[N];be(j!=null,`Previous layout not found for panel index ${N}`);const A=j+C,O=Gr({panelConstraints:l,panelIndex:N,size:A});if(Ct(j,O)||(C-=O-j,p[N]=O),Ct(C,0))break;n>0?N--:N++}}}const w=p.reduce((x,S)=>S+x,0);return Ct(w,100)?p:a}function DC({layout:n,panelsArray:o,pivotIndices:l}){let s=0,a=100,c=0,p=0;const f=l[0];be(f!=null,"No pivot index found"),o.forEach((x,S)=>{const{constraints:E}=x,{maxSize:P=100,minSize:C=0}=E;S===f?(s=C,a=P):(c+=C,p+=P)});const m=Math.min(a,100-c),y=Math.max(s,100-p),w=n[f];return{valueMax:m,valueMin:y,valueNow:w}}function il(n,o=document){return Array.from(o.querySelectorAll(`[data-panel-resize-handle-id][data-panel-group-id="${n}"]`))}function tg(n,o,l=document){const a=il(n,l).findIndex(c=>c.getAttribute("data-panel-resize-handle-id")===o);return a??null}function ng(n,o,l){const s=tg(n,o,l);return s!=null?[s,s+1]:[-1,-1]}function rg(n,o=document){var l;if(o instanceof HTMLElement&&(o==null||(l=o.dataset)===null||l===void 0?void 0:l.panelGroupId)==n)return o;const s=o.querySelector(`[data-panel-group][data-panel-group-id="${n}"]`);return s||null}function ts(n,o=document){const l=o.querySelector(`[data-panel-resize-handle-id="${n}"]`);return l||null}function $C(n,o,l,s=document){var a,c,p,f;const m=ts(o,s),y=il(n,s),w=m?y.indexOf(m):-1,x=(a=(c=l[w])===null||c===void 0?void 0:c.id)!==null&&a!==void 0?a:null,S=(p=(f=l[w+1])===null||f===void 0?void 0:f.id)!==null&&p!==void 0?p:null;return[x,S]}function FC({committedValuesRef:n,eagerValuesRef:o,groupId:l,layout:s,panelDataArray:a,panelGroupElement:c,setLayout:p}){vt({didWarnAboutMissingResizeHandle:!1}),dr(()=>{if(!c)return;const f=il(l,c);for(let m=0;m<a.length-1;m++){const{valueMax:y,valueMin:w,valueNow:x}=DC({layout:s,panelsArray:a,pivotIndices:[m,m+1]}),S=f[m];if(S!=null){const E=a[m];be(E,`No panel data found for index "${m}"`),S.setAttribute("aria-controls",E.id),S.setAttribute("aria-valuemax",""+Math.round(y)),S.setAttribute("aria-valuemin",""+Math.round(w)),S.setAttribute("aria-valuenow",x!=null?""+Math.round(x):"")}}return()=>{f.forEach((m,y)=>{m.removeAttribute("aria-controls"),m.removeAttribute("aria-valuemax"),m.removeAttribute("aria-valuemin"),m.removeAttribute("aria-valuenow")})}},[l,s,a,c]),cr(()=>{if(!c)return;const f=o.current;be(f,"Eager values not found");const{panelDataArray:m}=f,y=rg(l,c);be(y!=null,`No group found for id "${l}"`);const w=il(l,c);be(w,`No resize handles found for group id "${l}"`);const x=w.map(S=>{const E=S.getAttribute("data-panel-resize-handle-id");be(E,"Resize handle element has no handle id attribute");const[P,C]=$C(l,E,m,c);if(P==null||C==null)return()=>{};const k=N=>{if(!N.defaultPrevented)switch(N.key){case"Enter":{N.preventDefault();const j=m.findIndex(A=>A.id===P);if(j>=0){const A=m[j];be(A,`No panel data found for index ${j}`);const O=s[j],{collapsedSize:I=0,collapsible:B,minSize:D=0}=A.constraints;if(O!=null&&B){const oe=Jo({delta:Ct(O,I)?D-I:I-O,initialLayout:s,panelConstraints:m.map(re=>re.constraints),pivotIndices:ng(l,E,c),prevLayout:s,trigger:"keyboard"});s!==oe&&p(oe)}}break}}};return S.addEventListener("keydown",k),()=>{S.removeEventListener("keydown",k)}});return()=>{x.forEach(S=>S())}},[c,n,o,l,s,a,p])}function Wp(n,o){if(n.length!==o.length)return!1;for(let l=0;l<n.length;l++)if(n[l]!==o[l])return!1;return!0}function og(n,o){const l=n==="horizontal",{x:s,y:a}=Ji(o);return l?s:a}function WC(n,o,l,s,a){const c=l==="horizontal",p=ts(o,a);be(p,`No resize handle element found for id "${o}"`);const f=p.getAttribute("data-panel-group-id");be(f,"Resize handle element has no group id attribute");let{initialCursorPosition:m}=s;const y=og(l,n),w=rg(f,a);be(w,`No group element found for id "${f}"`);const x=w.getBoundingClientRect(),S=c?x.width:x.height;return(y-m)/S*100}function VC(n,o,l,s,a,c){if(Qh(n)){const p=l==="horizontal";let f=0;n.shiftKey?f=100:a!=null?f=a:f=10;let m=0;switch(n.key){case"ArrowDown":m=p?0:f;break;case"ArrowLeft":m=p?-f:0;break;case"ArrowRight":m=p?f:0;break;case"ArrowUp":m=p?0:-f;break;case"End":m=100;break;case"Home":m=-100;break}return m}else return s==null?0:WC(n,o,l,s,c)}function HC({panelDataArray:n}){const o=Array(n.length),l=n.map(c=>c.constraints);let s=0,a=100;for(let c=0;c<n.length;c++){const p=l[c];be(p,`Panel constraints not found for index ${c}`);const{defaultSize:f}=p;f!=null&&(s++,o[c]=f,a-=f)}for(let c=0;c<n.length;c++){const p=l[c];be(p,`Panel constraints not found for index ${c}`);const{defaultSize:f}=p;if(f!=null)continue;const m=n.length-s,y=a/m;s++,o[c]=y,a-=y}return o}function Kr(n,o,l){o.forEach((s,a)=>{const c=n[a];be(c,`Panel data not found for index ${a}`);const{callbacks:p,constraints:f,id:m}=c,{collapsedSize:y=0,collapsible:w}=f,x=l[m];if(x==null||s!==x){l[m]=s;const{onCollapse:S,onExpand:E,onResize:P}=p;P&&P(s,x),w&&(S||E)&&(E&&(x==null||mn(x,y))&&!mn(s,y)&&E(),S&&(x==null||!mn(x,y))&&mn(s,y)&&S())}})}function Ri(n,o){if(n.length!==o.length)return!1;for(let l=0;l<n.length;l++)if(n[l]!=o[l])return!1;return!0}function BC({defaultSize:n,dragState:o,layout:l,panelData:s,panelIndex:a,precision:c=3}){const p=l[a];let f;return p==null?f=n!=null?n.toPrecision(c):"1":s.length===1?f="1":f=p.toPrecision(c),{flexBasis:0,flexGrow:f,flexShrink:1,overflow:"hidden",pointerEvents:o!==null?"none":void 0}}function UC(n,o=10){let l=null;return(...a)=>{l!==null&&clearTimeout(l),l=setTimeout(()=>{n(...a)},o)}}function Vp(n){try{if(typeof localStorage<"u")n.getItem=o=>localStorage.getItem(o),n.setItem=(o,l)=>{localStorage.setItem(o,l)};else throw new Error("localStorage not supported in this environment")}catch(o){console.error(o),n.getItem=()=>null,n.setItem=()=>{}}}function lg(n){return`react-resizable-panels:${n}`}function ig(n){return n.map(o=>{const{constraints:l,id:s,idIsFromProps:a,order:c}=o;return a?s:c?`${c}:${JSON.stringify(l)}`:JSON.stringify(l)}).sort((o,l)=>o.localeCompare(l)).join(",")}function sg(n,o){try{const l=lg(n),s=o.getItem(l);if(s){const a=JSON.parse(s);if(typeof a=="object"&&a!=null)return a}}catch{}return null}function KC(n,o,l){var s,a;const c=(s=sg(n,l))!==null&&s!==void 0?s:{},p=ig(o);return(a=c[p])!==null&&a!==void 0?a:null}function QC(n,o,l,s,a){var c;const p=lg(n),f=ig(o),m=(c=sg(n,a))!==null&&c!==void 0?c:{};m[f]={expandToSizes:Object.fromEntries(l.entries()),layout:s};try{a.setItem(p,JSON.stringify(m))}catch(y){console.error(y)}}function Hp({layout:n,panelConstraints:o}){const l=[...n],s=l.reduce((c,p)=>c+p,0);if(l.length!==o.length)throw Error(`Invalid ${o.length} panel layout: ${l.map(c=>`${c}%`).join(", ")}`);if(!Ct(s,100)&&l.length>0)for(let c=0;c<o.length;c++){const p=l[c];be(p!=null,`No layout data found for index ${c}`);const f=100/s*p;l[c]=f}let a=0;for(let c=0;c<o.length;c++){const p=l[c];be(p!=null,`No layout data found for index ${c}`);const f=Gr({panelConstraints:o,panelIndex:c,size:p});p!=f&&(a+=p-f,l[c]=f)}if(!Ct(a,0))for(let c=0;c<o.length;c++){const p=l[c];be(p!=null,`No layout data found for index ${c}`);const f=p+a,m=Gr({panelConstraints:o,panelIndex:c,size:f});if(p!==m&&(a-=m-p,l[c]=m,Ct(a,0)))break}return l}const GC=100,el={getItem:n=>(Vp(el),el.getItem(n)),setItem:(n,o)=>{Vp(el),el.setItem(n,o)}},Bp={};function ag({autoSaveId:n=null,children:o,className:l="",direction:s,forwardedRef:a,id:c=null,onLayout:p=null,keyboardResizeBy:f=null,storage:m=el,style:y,tagName:w="div",...x}){const S=Bu(c),E=vt(null),[P,C]=Jr(null),[k,N]=Jr([]),j=IC(),A=vt({}),O=vt(new Map),I=vt(0),B=vt({autoSaveId:n,direction:s,dragState:P,id:S,keyboardResizeBy:f,onLayout:p,storage:m}),D=vt({layout:k,panelDataArray:[],panelDataArrayChanged:!1});vt({didLogIdAndOrderWarning:!1,didLogPanelConstraintsWarning:!1,prevPanelIds:[]}),Bh(a,()=>({getId:()=>B.current.id,getLayout:()=>{const{layout:V}=D.current;return V},setLayout:V=>{const{onLayout:Y}=B.current,{layout:ne,panelDataArray:ee}=D.current,F=Hp({layout:V,panelConstraints:ee.map(H=>H.constraints)});Wp(ne,F)||(N(F),D.current.layout=F,Y&&Y(F),Kr(ee,F,A.current))}}),[]),dr(()=>{B.current.autoSaveId=n,B.current.direction=s,B.current.dragState=P,B.current.id=S,B.current.onLayout=p,B.current.storage=m}),FC({committedValuesRef:B,eagerValuesRef:D,groupId:S,layout:k,panelDataArray:D.current.panelDataArray,setLayout:N,panelGroupElement:E.current}),cr(()=>{const{panelDataArray:V}=D.current;if(n){if(k.length===0||k.length!==V.length)return;let Y=Bp[n];Y==null&&(Y=UC(QC,GC),Bp[n]=Y);const ne=[...V],ee=new Map(O.current);Y(n,ne,ee,k,m)}},[n,k,m]),cr(()=>{});const oe=gt(V=>{const{onLayout:Y}=B.current,{layout:ne,panelDataArray:ee}=D.current;if(V.constraints.collapsible){const F=ee.map(fe=>fe.constraints),{collapsedSize:H=0,panelSize:te,pivotIndices:J}=ir(ee,V,ne);if(be(te!=null,`Panel size not found for panel "${V.id}"`),!mn(te,H)){O.current.set(V.id,te);const we=Qr(ee,V)===ee.length-1?te-H:H-te,Se=Jo({delta:we,initialLayout:ne,panelConstraints:F,pivotIndices:J,prevLayout:ne,trigger:"imperative-api"});Ri(ne,Se)||(N(Se),D.current.layout=Se,Y&&Y(Se),Kr(ee,Se,A.current))}}},[]),re=gt((V,Y)=>{const{onLayout:ne}=B.current,{layout:ee,panelDataArray:F}=D.current;if(V.constraints.collapsible){const H=F.map(Pe=>Pe.constraints),{collapsedSize:te=0,panelSize:J=0,minSize:fe=0,pivotIndices:we}=ir(F,V,ee),Se=Y??fe;if(mn(J,te)){const Pe=O.current.get(V.id),tt=Pe!=null&&Pe>=Se?Pe:Se,Ze=Qr(F,V)===F.length-1?J-tt:tt-J,Ue=Jo({delta:Ze,initialLayout:ee,panelConstraints:H,pivotIndices:we,prevLayout:ee,trigger:"imperative-api"});Ri(ee,Ue)||(N(Ue),D.current.layout=Ue,ne&&ne(Ue),Kr(F,Ue,A.current))}}},[]),ae=gt(V=>{const{layout:Y,panelDataArray:ne}=D.current,{panelSize:ee}=ir(ne,V,Y);return be(ee!=null,`Panel size not found for panel "${V.id}"`),ee},[]),me=gt((V,Y)=>{const{panelDataArray:ne}=D.current,ee=Qr(ne,V);return BC({defaultSize:Y,dragState:P,layout:k,panelData:ne,panelIndex:ee})},[P,k]),Z=gt(V=>{const{layout:Y,panelDataArray:ne}=D.current,{collapsedSize:ee=0,collapsible:F,panelSize:H}=ir(ne,V,Y);return be(H!=null,`Panel size not found for panel "${V.id}"`),F===!0&&mn(H,ee)},[]),ge=gt(V=>{const{layout:Y,panelDataArray:ne}=D.current,{collapsedSize:ee=0,collapsible:F,panelSize:H}=ir(ne,V,Y);return be(H!=null,`Panel size not found for panel "${V.id}"`),!F||gr(H,ee)>0},[]),se=gt(V=>{const{panelDataArray:Y}=D.current;Y.push(V),Y.sort((ne,ee)=>{const F=ne.order,H=ee.order;return F==null&&H==null?0:F==null?-1:H==null?1:F-H}),D.current.panelDataArrayChanged=!0,j()},[j]);dr(()=>{if(D.current.panelDataArrayChanged){D.current.panelDataArrayChanged=!1;const{autoSaveId:V,onLayout:Y,storage:ne}=B.current,{layout:ee,panelDataArray:F}=D.current;let H=null;if(V){const J=KC(V,F,ne);J&&(O.current=new Map(Object.entries(J.expandToSizes)),H=J.layout)}H==null&&(H=HC({panelDataArray:F}));const te=Hp({layout:H,panelConstraints:F.map(J=>J.constraints)});Wp(ee,te)||(N(te),D.current.layout=te,Y&&Y(te),Kr(F,te,A.current))}}),dr(()=>{const V=D.current;return()=>{V.layout=[]}},[]);const ve=gt(V=>{let Y=!1;const ne=E.current;return ne&&window.getComputedStyle(ne,null).getPropertyValue("direction")==="rtl"&&(Y=!0),function(F){F.preventDefault();const H=E.current;if(!H)return()=>null;const{direction:te,dragState:J,id:fe,keyboardResizeBy:we,onLayout:Se}=B.current,{layout:Pe,panelDataArray:tt}=D.current,{initialLayout:st}=J??{},Ze=ng(fe,V,H);let Ue=VC(F,V,te,J,we,H);const Mt=te==="horizontal";Mt&&Y&&(Ue=-Ue);const Kn=tt.map(fl=>fl.constraints),kt=Jo({delta:Ue,initialLayout:st??Pe,panelConstraints:Kn,pivotIndices:Ze,prevLayout:Pe,trigger:Qh(F)?"keyboard":"mouse-or-touch"}),vr=!Ri(Pe,kt);(Gh(F)||Yh(F))&&I.current!=Ue&&(I.current=Ue,!vr&&Ue!==0?Mt?su(V,Ue<0?Zh:qh):su(V,Ue<0?Jh:eg):su(V,0)),vr&&(N(kt),D.current.layout=kt,Se&&Se(kt),Kr(tt,kt,A.current))}},[]),le=gt((V,Y)=>{const{onLayout:ne}=B.current,{layout:ee,panelDataArray:F}=D.current,H=F.map(Pe=>Pe.constraints),{panelSize:te,pivotIndices:J}=ir(F,V,ee);be(te!=null,`Panel size not found for panel "${V.id}"`);const we=Qr(F,V)===F.length-1?te-Y:Y-te,Se=Jo({delta:we,initialLayout:ee,panelConstraints:H,pivotIndices:J,prevLayout:ee,trigger:"imperative-api"});Ri(ee,Se)||(N(Se),D.current.layout=Se,ne&&ne(Se),Kr(F,Se,A.current))},[]),de=gt((V,Y)=>{const{layout:ne,panelDataArray:ee}=D.current,{collapsedSize:F=0,collapsible:H}=Y,{collapsedSize:te=0,collapsible:J,maxSize:fe=100,minSize:we=0}=V.constraints,{panelSize:Se}=ir(ee,V,ne);Se!=null&&(H&&J&&mn(Se,F)?mn(F,te)||le(V,te):Se<we?le(V,we):Se>fe&&le(V,fe))},[le]),$=gt((V,Y)=>{const{direction:ne}=B.current,{layout:ee}=D.current;if(!E.current)return;const F=ts(V,E.current);be(F,`Drag handle element not found for id "${V}"`);const H=og(ne,Y);C({dragHandleId:V,dragHandleRect:F.getBoundingClientRect(),initialCursorPosition:H,initialLayout:ee})},[]),q=gt(()=>{C(null)},[]),G=gt(V=>{const{panelDataArray:Y}=D.current,ne=Qr(Y,V);ne>=0&&(Y.splice(ne,1),delete A.current[V.id],D.current.panelDataArrayChanged=!0,j())},[j]),_=CC(()=>({collapsePanel:oe,direction:s,dragState:P,expandPanel:re,getPanelSize:ae,getPanelStyle:me,groupId:S,isPanelCollapsed:Z,isPanelExpanded:ge,reevaluatePanelConstraints:de,registerPanel:se,registerResizeHandle:ve,resizePanel:le,startDragging:$,stopDragging:q,unregisterPanel:G,panelGroupElement:E.current}),[oe,P,s,re,ae,me,S,Z,ge,de,se,ve,le,$,q,G]),W={display:"flex",flexDirection:s==="horizontal"?"row":"column",height:"100%",overflow:"hidden",width:"100%"};return ro(qi.Provider,{value:_},ro(w,{...x,children:o,className:l,id:c,ref:E,style:{...W,...y},"data-panel-group":"","data-panel-group-direction":s,"data-panel-group-id":S}))}const ug=Vh((n,o)=>ro(ag,{...n,forwardedRef:o}));ag.displayName="PanelGroup";ug.displayName="forwardRef(PanelGroup)";function Qr(n,o){return n.findIndex(l=>l===o||l.id===o.id)}function ir(n,o,l){const s=Qr(n,o),c=s===n.length-1?[s-1,s]:[s,s+1],p=l[s];return{...o.constraints,panelSize:p,pivotIndices:c}}function YC({disabled:n,handleId:o,resizeHandler:l,panelGroupElement:s}){cr(()=>{if(n||l==null||s==null)return;const a=ts(o,s);if(a==null)return;const c=p=>{if(!p.defaultPrevented)switch(p.key){case"ArrowDown":case"ArrowLeft":case"ArrowRight":case"ArrowUp":case"End":case"Home":{p.preventDefault(),l(p);break}case"F6":{p.preventDefault();const f=a.getAttribute("data-panel-group-id");be(f,`No group element found for id "${f}"`);const m=il(f,s),y=tg(f,o,s);be(y!==null,`No resize element found for id "${o}"`);const w=p.shiftKey?y>0?y-1:m.length-1:y+1<m.length?y+1:0;m[w].focus();break}}};return a.addEventListener("keydown",c),()=>{a.removeEventListener("keydown",c)}},[s,n,o,l])}function cg({children:n=null,className:o="",disabled:l=!1,hitAreaMargins:s,id:a,onBlur:c,onDragging:p,onFocus:f,style:m={},tabIndex:y=0,tagName:w="div",...x}){var S,E;const P=vt(null),C=vt({onDragging:p});cr(()=>{C.current.onDragging=p});const k=Hh(qi);if(k===null)throw Error("PanelResizeHandle components must be rendered within a PanelGroup container");const{direction:N,groupId:j,registerResizeHandle:A,startDragging:O,stopDragging:I,panelGroupElement:B}=k,D=Bu(a),[oe,re]=Jr("inactive"),[ae,me]=Jr(!1),[Z,ge]=Jr(null),se=vt({state:oe});dr(()=>{se.current.state=oe}),cr(()=>{if(l)ge(null);else{const $=A(D);ge(()=>$)}},[l,D,A]);const ve=(S=s==null?void 0:s.coarse)!==null&&S!==void 0?S:15,le=(E=s==null?void 0:s.fine)!==null&&E!==void 0?E:5;return cr(()=>{if(l||Z==null)return;const $=P.current;return be($,"Element ref not attached"),MC(D,$,N,{coarse:ve,fine:le},(G,_,W)=>{if(_)switch(G){case"down":{re("drag"),be(W,'Expected event to be defined for "down" action'),O(D,W);const{onDragging:V}=C.current;V&&V(!0);break}case"move":{const{state:V}=se.current;V!=="drag"&&re("hover"),be(W,'Expected event to be defined for "move" action'),Z(W);break}case"up":{re("hover"),I();const{onDragging:V}=C.current;V&&V(!1);break}}else re("inactive")})},[ve,N,l,le,A,D,Z,O,I]),YC({disabled:l,handleId:D,resizeHandler:Z,panelGroupElement:B}),ro(w,{...x,children:n,className:o,id:a,onBlur:()=>{me(!1),c==null||c()},onFocus:()=>{me(!0),f==null||f()},ref:P,role:"separator",style:{...{touchAction:"none",userSelect:"none"},...m},tabIndex:y,"data-panel-group-direction":N,"data-panel-group-id":j,"data-resize-handle":"","data-resize-handle-active":oe==="drag"?"pointer":ae?"keyboard":void 0,"data-resize-handle-state":oe,"data-panel-resize-handle-enabled":!l,"data-panel-resize-handle-id":D})}cg.displayName="PanelResizeHandle";function XC({className:n,...o}){return g.jsx(ug,{"data-slot":"resizable-panel-group",className:De("flex h-full w-full data-[panel-group-direction=vertical]:flex-col",n),...o})}function Up({...n}){return g.jsx(Kh,{"data-slot":"resizable-panel",...n})}function ZC({withHandle:n,className:o,...l}){return g.jsx(cg,{"data-slot":"resizable-handle",className:De("bg-border focus-visible:ring-ring relative flex w-px items-center justify-center after:absolute after:inset-y-0 after:left-1/2 after:w-1 after:-translate-x-1/2 focus-visible:ring-1 focus-visible:ring-offset-1 focus-visible:outline-hidden data-[panel-group-direction=vertical]:h-px data-[panel-group-direction=vertical]:w-full data-[panel-group-direction=vertical]:after:left-0 data-[panel-group-direction=vertical]:after:h-1 data-[panel-group-direction=vertical]:after:w-full data-[panel-group-direction=vertical]:after:-translate-y-1/2 data-[panel-group-direction=vertical]:after:translate-x-0 [&[data-panel-group-direction=vertical]>div]:rotate-90",o),...l,children:n&&g.jsx("div",{className:"bg-border z-10 flex h-4 w-3 items-center justify-center rounded-xs border",children:g.jsx(Px,{className:"size-2.5"})})})}function qC({title:n="Voice Interpretation App"}){return g.jsxs("div",{className:"h-8 bg-gray-50 border-b border-gray-200 flex items-center justify-center relative select-none",style:{WebkitAppRegion:"drag",paddingLeft:"70px"},children:[g.jsx("div",{className:"flex items-center gap-2",children:g.jsx("h1",{className:"text-xs font-medium text-gray-700",children:n})}),g.jsx("div",{className:"absolute left-0 top-0 w-16 h-full",style:{WebkitAppRegion:"no-drag"}})]})}function JC(){const[n,o]=h.useState(!1),[l,s]=h.useState("ko"),[a,c]=h.useState("en"),[p,f]=h.useState(!1),[m,y]=h.useState(!0),[w,x]=h.useState(!1),[S,E]=h.useState(!1),[P,C]=h.useState(!1),[k,N]=h.useState([]),[j,A]=h.useState(""),[O,I]=h.useState([]),[B,D]=h.useState([]),[oe,re]=h.useState(!1),[ae,me]=h.useState(!1),Z=h.useRef(null),ge=h.useRef(null);h.useEffect(()=>{N([]),I([]),A(""),D([])},[]);const se=async()=>{var H,te;if(n)try{(H=Z.current)==null||H.stop(),(te=ge.current)==null||te.getTracks().forEach(J=>J.stop()),Z.current=null,ge.current=null,o(!1),console.log("녹음 중지")}catch(J){console.error("녹음 중지 중 오류 발생:",J)}else try{let J=await window.electronAPI.checkAudioPermissions();if(!J&&(J=await window.electronAPI.requestAudioPermissions(),!J)){alert("마이크 접근 권한이 필요합니다. 시스템 설정에서 권한을 허용해주세요.");return}await window.electronAPI.enableLoopbackAudio(),await window.electronAPI.startRecording();const fe=await navigator.mediaDevices.getDisplayMedia({audio:!0,video:!0});fe.getVideoTracks().forEach(Pe=>{Pe.stop(),fe.removeTrack(Pe)}),await window.electronAPI.disableLoopbackAudio(),ge.current=fe;const we=[];let Se;try{Se=new MediaRecorder(fe,{mimeType:"audio/webm;codecs=opus"})}catch{Se=new MediaRecorder(fe)}Se.ondataavailable=Pe=>{Pe.data.size>0&&we.push(Pe.data)},Se.onstop=async()=>{const tt=await new Blob(we,{type:"audio/webm"}).arrayBuffer(),{success:st,filePath:Ze}=await window.electronAPI.stopRecording();st&&Ze?(await window.electronAPI.saveAudioFile(Ze,new Uint8Array(tt)),console.log("오디오 파일이 저장되었습니다:",Ze)):console.error("녹음 파일 경로를 가져오는데 실패했습니다.")},Se.start(1e3),Z.current=Se,o(!0),console.log("녹음 시작")}catch(J){console.error("녹음 시작 중 오류 발생:",J),await window.electronAPI.disableLoopbackAudio()}},ve=()=>{y(!m)},le=()=>{x(!w)},de=()=>{E(!S)},$=()=>{C(!P)},q=()=>new Date().toTimeString().slice(0,8),G=H=>H.map((te,J)=>{if(te.id.startsWith("debug-")){const we=J===H.length-1&&H.filter(Se=>Se.id.startsWith("debug-")).length>0;return{...te,isDebugLatest:we}}return{...te,isDebugLatest:!1}}),_=()=>{N([]),console.log("모든 번역 아이템이 삭제되었습니다.")},W=H=>{const te={id:`debug-${Date.now()}`,timestamp:q(),originalText:"",translatedText:"",originalLang:l.toUpperCase(),targetLang:a.toUpperCase(),confidence:"high",isDebugLatest:!0};N(J=>{const fe=[...J,te];return G(fe)}),re(!0),console.log("빈 아이템이 추가되었습니다:",te)},V=H=>{if(k.length===0){console.log("전사 추가 실패: 추가할 아이템이 없습니다. 먼저 '아이템 추가'를 클릭하세요.");return}if(!H.trim()){console.log("전사 추가 실패: 텍스트가 비어있습니다.");return}N(te=>{const J=[...te],fe=J.length-1;return J[fe]={...J[fe],originalText:H.trim()},G(J)}),console.log("전사가 마지막 아이템에 추가되었습니다:",H)},Y=H=>{if(k.length===0){console.log("번역 추가 실패: 추가할 아이템이 없습니다. 먼저 '아이템 추가'를 클릭하세요.");return}if(!H.trim()){console.log("번역 추가 실패: 텍스트가 비어있습니다.");return}N(te=>{const J=[...te],fe=J.length-1;return J[fe]={...J[fe],translatedText:H.trim()},G(J)}),console.log("번역이 마지막 아이템에 추가되었습니다:",H)},ne=H=>{A(H),console.log("현재 주제가 변경되었습니다:",H)},ee=()=>{I([]),console.log("모든 구간별 요약이 삭제되었습니다.")},F=(H,te,J)=>{const fe={id:`summary-${Date.now()}`,timeRange:H.trim(),summary:te.trim(),keyPoints:J.filter(we=>we.trim()!=="")};I(we=>[...we,fe]),me(!0),console.log("새 구간별 요약이 추가되었습니다:",fe)};return g.jsxs("div",{className:"h-screen flex flex-col bg-gray-50 overflow-hidden",children:[g.jsx(qC,{title:"Voice Interpretation App"}),g.jsx("div",{className:"flex-1 flex bg-gray-50 overflow-hidden",children:m?g.jsxs(XC,{direction:"horizontal",className:"h-full w-full",children:[g.jsx(Up,{defaultSize:70,minSize:50,maxSize:85,children:g.jsxs("div",{className:"flex-1 flex flex-col min-h-0 overflow-hidden h-full",children:[g.jsx("div",{className:"flex-1 min-h-0 overflow-hidden",children:g.jsx(op,{translations:k,isSummaryPanelVisible:m,onSummaryPanelToggle:ve,isQuestionHelperVisible:w,onQuestionHelperToggle:le,scrollToBottom:oe,onScrolledToBottom:()=>re(!1)})}),g.jsx("div",{className:"shrink-0 overflow-visible",children:g.jsx(Mp,{isRecording:n,onRecordingToggle:se,sourceLang:l,targetLang:a,onSourceLangChange:s,onTargetLangChange:c,isOnlineMode:p,onModeToggle:()=>f(!p),suggestedQuestions:B,isQuestionHelperVisible:w,onQuestionHelperToggle:le,isDebugMode:S,onDebugModeToggle:de,isSummaryDebugMode:P,onSummaryDebugModeToggle:$,onClearAllTranslations:_,onAddItem:W,onAddTranscription:V,onAddTranslation:Y})})]})}),g.jsx(ZC,{withHandle:!0,className:"bg-gray-200 hover:bg-gray-300 transition-colors duration-200"}),g.jsx(Up,{defaultSize:30,minSize:15,maxSize:50,children:g.jsx("div",{className:"h-full overflow-hidden",children:g.jsx(aw,{currentTopic:j,summaries:O,isSummaryDebugMode:P,onTopicChange:ne,onClearAllSummaries:ee,onAddSummary:F,summaryScrollToBottom:ae,onSummaryScrolledToBottom:()=>me(!1)})})})]}):g.jsxs("div",{className:"flex-1 flex flex-col min-h-0 overflow-hidden",children:[g.jsx("div",{className:"flex-1 min-h-0 overflow-hidden",children:g.jsx(op,{translations:k,isSummaryPanelVisible:m,onSummaryPanelToggle:ve,isQuestionHelperVisible:w,onQuestionHelperToggle:le,scrollToBottom:oe,onScrolledToBottom:()=>re(!1)})}),g.jsx("div",{className:"shrink-0 overflow-visible",children:g.jsx(Mp,{isRecording:n,onRecordingToggle:se,sourceLang:l,targetLang:a,onSourceLangChange:s,onTargetLangChange:c,isOnlineMode:p,onModeToggle:()=>f(!p),suggestedQuestions:B,isQuestionHelperVisible:w,onQuestionHelperToggle:le,isDebugMode:S,onDebugModeToggle:de,isSummaryDebugMode:P,onSummaryDebugModeToggle:$,onClearAllTranslations:_,onAddItem:W,onAddTranscription:V,onAddTranslation:Y})})]})})]})}hy.createRoot(document.getElementById("root")).render(g.jsx(h.StrictMode,{children:g.jsx(JC,{})}));
