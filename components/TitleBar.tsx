import React from 'react';

interface TitleBarProps {
  title?: string;
}

export function TitleBar({ title = "Voice Interpretation App" }: TitleBarProps) {
  return (
    <div 
      className="h-8 bg-gray-50 border-b border-gray-200 flex items-center justify-center relative select-none"
      style={{ 
        WebkitAppRegion: 'drag',
        paddingLeft: '70px', // Space for traffic light buttons
      } as React.CSSProperties}
    >
      {/* App Title */}
      <div className="flex items-center gap-2">
        <h1 className="text-xs font-medium text-gray-700">{title}</h1>
      </div>
      
      {/* Traffic Light Buttons Spacer */}
      <div 
        className="absolute left-0 top-0 w-16 h-full"
        style={{ WebkitAppRegion: 'no-drag' } as React.CSSProperties}
      />
    </div>
  );
} 