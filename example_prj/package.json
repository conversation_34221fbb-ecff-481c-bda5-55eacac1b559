{"name": "electron-audio-loopback", "version": "1.0.3", "description": "Capture system audio loopback on macOS 12.3+, Windows 10+ and Linux", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc", "dev": "tsc --watch", "clean": "<PERSON><PERSON><PERSON> dist", "prepublishOnly": "npm run clean && npm run build", "test": "npm run build && electron example/index.js", "lint": "eslint src --ext .ts", "lint:fix": "eslint src --ext .ts --fix"}, "keywords": ["electron", "audio", "system", "plugin", "loopback", "capture"], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "devDependencies": {"@types/node": "^20.0.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "electron": "^31.0.1", "eslint": "^8.0.0", "jest": "^29.0.0", "rimraf": "^5.0.0", "typescript": "^5.0.0"}, "peerDependencies": {"electron": ">=31.0.1"}, "files": ["dist", "README.md"], "repository": {"type": "git", "url": "git://github.com/alectrocute/electron-audio-loopback"}}