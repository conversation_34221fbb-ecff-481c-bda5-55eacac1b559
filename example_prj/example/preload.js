const { getLoopbackAudioMediaStream } = require('../dist/index.js');

document.addEventListener('DOMContentLoaded', async () => {
    const audioPlayer = document.getElementById('audio');
    const status = document.getElementById('status');
    const canvas = document.getElementById('canvas');
    const ctx = canvas.getContext('2d');
    const recordBtn = document.getElementById('recordBtn');
    const downloadLink = document.getElementById('downloadLink');
    const recordingIndicator = document.getElementById('recordingIndicator');

    // Set canvas size
    canvas.width = window.innerWidth - 25;
    canvas.height = 100;

    // Recording variables
    let mediaRecorder = null;
    let recordedChunks = [];
    let isRecording = false;

    function drawVisualizer(ctx, analyser, dataArray, canvas) {
        requestAnimationFrame(() => drawVisualizer(ctx, analyser, dataArray, canvas));
    
        analyser.getByteFrequencyData(dataArray);
    
        ctx.fillStyle = '#f3f3f3';
        ctx.fillRect(0, 0, canvas.width, canvas.height);
    
        const bufferLength = analyser.frequencyBinCount;
        const barWidth = (canvas.width / bufferLength) * 2.5;
        let barHeight;
        let x = 0;
    
        for (let i = 0; i < bufferLength; i++) {
            barHeight = dataArray[i] / 2;
    
            const hue = (i / bufferLength) * 360;
            ctx.fillStyle = `hsl(${hue}, 70%, 50%)`;
            ctx.fillRect(x, canvas.height - barHeight, barWidth, barHeight);
    
            x += barWidth + 1;
        }
    }

    function startRecording(stream) {
        recordedChunks = [];
        
        // MediaRecorder options for better quality
        const options = {
            mimeType: 'audio/webm;codecs=opus',
            audioBitsPerSecond: 128000
        };

        try {
            mediaRecorder = new MediaRecorder(stream, options);
        } catch (err) {
            console.log('Failed with opus codec, trying default...');
            mediaRecorder = new MediaRecorder(stream);
        }

        mediaRecorder.ondataavailable = (event) => {
            if (event.data.size > 0) {
                recordedChunks.push(event.data);
            }
        };

        mediaRecorder.onstop = () => {
            const blob = new Blob(recordedChunks, { type: 'audio/webm' });
            const url = URL.createObjectURL(blob);
            
            downloadLink.href = url;
            downloadLink.style.display = 'inline-block';
            
            // Create timestamp for filename
            const now = new Date();
            const timestamp = now.toISOString().replace(/[:.]/g, '-').slice(0, -5);
            downloadLink.download = `system_audio_${timestamp}.webm`;
            
            console.log('Recording saved, size:', blob.size, 'bytes');
        };

        mediaRecorder.start(1000); // Collect data every 1 second
        isRecording = true;
        
        recordBtn.textContent = 'Stop Recording';
        recordBtn.classList.add('recording');
        recordingIndicator.style.display = 'inline';
        downloadLink.style.display = 'none';
    }

    function stopRecording() {
        if (mediaRecorder && isRecording) {
            mediaRecorder.stop();
            isRecording = false;
            
            recordBtn.textContent = 'Start Recording';
            recordBtn.classList.remove('recording');
            recordingIndicator.style.display = 'none';
        }
    }

    try {
        const mediaStream = await getLoopbackAudioMediaStream();

        console.log('Audio stream obtained:', mediaStream);

        audioPlayer.srcObject = mediaStream;
        audioPlayer.play();
        audioPlayer.volume = 0;

        status.textContent = 'Stream obtained! Click "Start Recording" to save audio to file.'

        // Setup recording button event
        recordBtn.addEventListener('click', () => {
            if (!isRecording) {
                startRecording(mediaStream);
            } else {
                stopRecording();
            }
        });

        // Create WebAudio visualizer
        const audioContext = new (window.AudioContext || window.webkitAudioContext)();
        const source = audioContext.createMediaStreamSource(mediaStream);
        const analyser = audioContext.createAnalyser();

        analyser.fftSize = 256;
        const bufferLength = analyser.frequencyBinCount;
        const dataArray = new Uint8Array(bufferLength);

        source.connect(analyser);

        drawVisualizer(ctx, analyser, dataArray, canvas);

    } catch (error) {
        console.error('Error getting audio stream:', error);
        status.textContent = 'Error getting audio stream';
    }
});
