<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0"><dict><key>CFBundleDisplayName</key><string>Voice Interpretation App</string><key>CFBundleExecutable</key><string>Voice Interpretation App</string><key>CFBundleIconFile</key><string>electron.icns</string><key>CFBundleIdentifier</key><string>com.voiceinterpretation.app</string><key>CFBundleInfoDictionaryVersion</key><string>6.0</string><key>CFBundleName</key><string>Voice Interpretation App</string><key>CFBundlePackageType</key><string>APPL</string><key>CFBundleShortVersionString</key><string>1.0.0</string><key>CFBundleVersion</key><string>1.0.0</string><key>DTCompiler</key><string>com.apple.compilers.llvm.clang.1_0</string><key>DTSDKBuild</key><string>23F73</string><key>DTSDKName</key><string>macosx14.5</string><key>DTXcode</key><string>1540</string><key>DTXcodeBuild</key><string>15F31d</string><key>ElectronAsarIntegrity</key><dict><key>Resources/app.asar</key><dict><key>algorithm</key><string>SHA256</string><key>hash</key><string>bc524f80cfc8698d36d7b0d5856deb9b034e507ff5bdf75c006ad4debab4d824</string></dict></dict><key>LSApplicationCategoryType</key><string>public.app-category.productivity</string><key>LSEnvironment</key><dict><key>MallocNanoZone</key><string>0</string></dict><key>LSMinimumSystemVersion</key><string>11.0</string><key>NSAppTransportSecurity</key><dict><key>NSAllowsArbitraryLoads</key><true/><key>NSAllowsLocalNetworking</key><true/><key>NSExceptionDomains</key><dict><key>127.0.0.1</key><dict><key>NSIncludesSubdomains</key><false/><key>NSTemporaryExceptionAllowsInsecureHTTPLoads</key><true/><key>NSTemporaryExceptionAllowsInsecureHTTPSLoads</key><false/><key>NSTemporaryExceptionMinimumTLSVersion</key><string>1.0</string><key>NSTemporaryExceptionRequiresForwardSecrecy</key><false/></dict><key>localhost</key><dict><key>NSIncludesSubdomains</key><false/><key>NSTemporaryExceptionAllowsInsecureHTTPLoads</key><true/><key>NSTemporaryExceptionAllowsInsecureHTTPSLoads</key><false/><key>NSTemporaryExceptionMinimumTLSVersion</key><string>1.0</string><key>NSTemporaryExceptionRequiresForwardSecrecy</key><false/></dict></dict></dict><key>NSBluetoothAlwaysUsageDescription</key><string>This app needs access to Bluetooth</string><key>NSBluetoothPeripheralUsageDescription</key><string>This app needs access to Bluetooth</string><key>NSCameraUsageDescription</key><string>This app needs access to the camera</string><key>NSHighResolutionCapable</key><true/><key>NSHumanReadableCopyright</key><string>Copyright © 2025 Voice Interpretation Team</string><key>NSMainNibFile</key><string>MainMenu</string><key>NSMicrophoneUsageDescription</key><string>This app needs access to the microphone</string><key>NSPrefersDisplaySafeAreaCompatibilityMode</key><false/><key>NSPrincipalClass</key><string>AtomApplication</string><key>NSQuitAlwaysKeepsWindows</key><false/><key>NSRequiresAquaSystemAppearance</key><false/><key>NSSupportsAutomaticGraphicsSwitching</key><true/></dict></plist>