import { useState, useCallback, useRef } from 'react';

interface RecordingState {
  isRecording: boolean;
  isLoading: boolean;
  error: string | null;
  hasPermission: boolean | null;
}

interface UseSystemAudioRecordingReturn {
  recordingState: RecordingState;
  startRecording: () => Promise<void>;
  stopRecording: () => Promise<string | null>;
  checkPermissions: () => Promise<void>;
  requestPermissions: () => Promise<void>;
}

export function useSystemAudioRecording(): UseSystemAudioRecordingReturn {
  const [recordingState, setRecordingState] = useState<RecordingState>({
    isRecording: false,
    isLoading: false,
    error: null,
    hasPermission: null,
  });

  const mediaStreamRef = useRef<MediaStream | null>(null);
  const mediaRecorderRef = useRef<MediaRecorder | null>(null);
  const recordedChunksRef = useRef<Blob[]>([]);

  const checkPermissions = useCallback(async () => {
    try {
      setRecordingState(prev => ({ ...prev, isLoading: true, error: null }));
      
      const hasPermission = await window.electronAPI.checkAudioPermissions();
      
      setRecordingState(prev => ({ 
        ...prev, 
        hasPermission, 
        isLoading: false 
      }));
    } catch (error) {
      console.error('Error checking permissions:', error);
      setRecordingState(prev => ({ 
        ...prev, 
        hasPermission: false, 
        isLoading: false,
        error: 'Failed to check permissions'
      }));
    }
  }, []);

  const requestPermissions = useCallback(async () => {
    try {
      setRecordingState(prev => ({ ...prev, isLoading: true, error: null }));
      
      const granted = await window.electronAPI.requestAudioPermissions();
      
      setRecordingState(prev => ({ 
        ...prev, 
        hasPermission: granted, 
        isLoading: false,
        error: granted ? null : 'Permission denied'
      }));
    } catch (error) {
      console.error('Error requesting permissions:', error);
      setRecordingState(prev => ({ 
        ...prev, 
        hasPermission: false, 
        isLoading: false,
        error: 'Failed to request permissions'
      }));
    }
  }, []);

  const startRecording = useCallback(async () => {
    try {
      setRecordingState(prev => ({ ...prev, isLoading: true, error: null }));

      // Check permissions first
      if (recordingState.hasPermission === null) {
        await checkPermissions();
      }

      if (recordingState.hasPermission === false) {
        throw new Error('Audio permissions not granted');
      }

      // Enable loopback audio in main process
      const enableResult = await window.electronAPI.enableLoopbackAudio();
      if (!enableResult.success) {
        throw new Error(enableResult.message || 'Failed to enable loopback audio');
      }

      // Get system audio stream using getDisplayMedia
      const stream = await navigator.mediaDevices.getDisplayMedia({
        video: true,
        audio: true,
      });

      // Remove video tracks as we only need audio
      const videoTracks = stream.getVideoTracks();
      videoTracks.forEach(track => {
        track.stop();
        stream.removeTrack(track);
      });

      mediaStreamRef.current = stream;

      // Setup MediaRecorder
      const mediaRecorder = new MediaRecorder(stream, {
        mimeType: 'audio/webm;codecs=opus'
      });

      recordedChunksRef.current = [];

      mediaRecorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          recordedChunksRef.current.push(event.data);
        }
      };

      mediaRecorder.start(1000); // Collect data every second
      mediaRecorderRef.current = mediaRecorder;

      setRecordingState(prev => ({ 
        ...prev, 
        isRecording: true, 
        isLoading: false 
      }));

    } catch (error) {
      console.error('Error starting recording:', error);
      
      // Cleanup on error
      await window.electronAPI.disableLoopbackAudio();
      
      setRecordingState(prev => ({ 
        ...prev, 
        isRecording: false, 
        isLoading: false,
        error: error instanceof Error ? error.message : 'Failed to start recording'
      }));
    }
  }, [recordingState.hasPermission, checkPermissions]);

  const stopRecording = useCallback(async (): Promise<string | null> => {
    try {
      setRecordingState(prev => ({ ...prev, isLoading: true, error: null }));

      if (!mediaRecorderRef.current || !mediaStreamRef.current) {
        throw new Error('No active recording found');
      }

      return new Promise((resolve, reject) => {
        const mediaRecorder = mediaRecorderRef.current!;
        
        mediaRecorder.onstop = async () => {
          try {
            // Create blob from recorded chunks
            const blob = new Blob(recordedChunksRef.current, { 
              type: 'audio/webm;codecs=opus' 
            });

            // Generate filename with timestamp
            const timestamp = new Date().toISOString().replace(/:/g, '-').replace(/\./g, '-');
            const fileName = `system-audio-${timestamp}.webm`;
            const downloadsPath = `${require('os').homedir()}/Downloads/${fileName}`;

            // Convert blob to array buffer
            const arrayBuffer = await blob.arrayBuffer();

            // Save file through main process
            const saveResult = await window.electronAPI.saveAudioFile(downloadsPath, arrayBuffer);
            
            if (!saveResult.success) {
              throw new Error(saveResult.message || 'Failed to save audio file');
            }

            // Cleanup
            mediaStreamRef.current?.getTracks().forEach(track => track.stop());
            mediaStreamRef.current = null;
            mediaRecorderRef.current = null;
            recordedChunksRef.current = [];

            // Disable loopback audio
            await window.electronAPI.disableLoopbackAudio();

            setRecordingState(prev => ({ 
              ...prev, 
              isRecording: false, 
              isLoading: false 
            }));

            resolve(downloadsPath);
          } catch (error) {
            console.error('Error in onstop handler:', error);
            reject(error);
          }
        };

        mediaRecorder.stop();
      });

    } catch (error) {
      console.error('Error stopping recording:', error);
      
      // Cleanup on error
      mediaStreamRef.current?.getTracks().forEach(track => track.stop());
      mediaStreamRef.current = null;
      mediaRecorderRef.current = null;
      await window.electronAPI.disableLoopbackAudio();
      
      setRecordingState(prev => ({ 
        ...prev, 
        isRecording: false, 
        isLoading: false,
        error: error instanceof Error ? error.message : 'Failed to stop recording'
      }));

      return null;
    }
  }, []);

  return {
    recordingState,
    startRecording,
    stopRecording,
    checkPermissions,
    requestPermissions,
  };
}
