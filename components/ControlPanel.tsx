import { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> } from "lucide-react";
import { But<PERSON> } from "./ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "./ui/select";
import { Switch } from "./ui/switch";
import { Label } from "./ui/label";
import { QuestionHelper } from "./QuestionHelper";
import { DebugPanel } from "./DebugPanel";

interface ControlPanelProps {
  isRecording: boolean;
  onRecordingToggle: () => void;
  sourceLang: string;
  targetLang: string;
  onSourceLangChange: (lang: string) => void;
  onTargetLangChange: (lang: string) => void;
  isOnlineMode: boolean;
  onModeToggle: () => void;
  suggestedQuestions?: string[];
  isQuestionHelperVisible: boolean;
  onQuestionHelperToggle: () => void;
  isDebugMode: boolean;
  onDebugModeToggle: () => void;
  isSummaryDebugMode: boolean;
  onSummaryDebugModeToggle: () => void;
  onClearAllTranslations: () => void;
  onAddItem: (text: string) => void;
  onAddTranscription: (text: string) => void;
  onAddTranslation: (text: string) => void;
  isLoading?: boolean;
  recordingError?: string | null;
  hasPermission?: boolean | null;
}

const languages = [
  { value: 'ko', label: '한국어' },
  { value: 'en', label: 'English' },
  { value: 'ja', label: '日본語' },
  { value: 'zh', label: '中文' }
];

export function ControlPanel({
  isRecording,
  onRecordingToggle,
  sourceLang,
  targetLang,
  onSourceLangChange,
  onTargetLangChange,
  isOnlineMode,
  onModeToggle,
  suggestedQuestions = [],
  isQuestionHelperVisible,
  onQuestionHelperToggle,
  isDebugMode,
  onDebugModeToggle,
  isSummaryDebugMode,
  onSummaryDebugModeToggle,
  onClearAllTranslations,
  onAddItem,
  onAddTranscription,
  onAddTranslation,
  isLoading = false,
  recordingError = null,
  hasPermission = null
}: ControlPanelProps) {
  const swapLanguages = () => {
    onSourceLangChange(targetLang);
    onTargetLangChange(sourceLang);
  };

  const handleQuestionSubmit = (question: string) => {
    // 질문 도우미는 참고용이므로 실제 전송 기능은 사용하지 않음
    console.log("Question for reference:", question);
  };

  return (
    <div className="border-t bg-gray-50 p-4 space-y-4 shrink-0">
      {/* 에러 메시지 표시 */}
      {recordingError && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-3">
          <div className="flex items-center gap-2 text-red-800">
            <span className="text-sm font-medium">오류:</span>
            <span className="text-sm">{recordingError}</span>
          </div>
        </div>
      )}

      {/* 권한 상태 표시 */}
      {hasPermission === false && (
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
          <div className="flex items-center gap-2 text-yellow-800">
            <span className="text-sm font-medium">권한 필요:</span>
            <span className="text-sm">시스템 오디오 녹음을 위해 마이크 권한이 필요합니다.</span>
          </div>
        </div>
      )}
      {/* 메인 컨트롤 그룹: 언어 선택 + 녹음 버튼 + 회의 모드 */}
      <div className="bg-white rounded-lg p-4 border border-gray-200 shadow-sm">
        {/* 상단: 언어 선택과 녹음 컨트롤 */}
        <div className="flex items-center justify-between mb-4">
          {/* 좌측: Source 언어 선택 */}
          <div className="flex items-center gap-3">
            <Label className={isRecording ? "text-sm text-muted-foreground" : "text-sm"}>Source:</Label>
            <Select value={sourceLang} onValueChange={onSourceLangChange} disabled={isRecording}>
              <SelectTrigger className="w-32">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {languages.map((lang) => (
                  <SelectItem key={lang.value} value={lang.value}>
                    {lang.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* 중앙: 녹음 버튼과 언어 전환 버튼 */}
          <div className="flex items-center gap-4">
            {/* 녹음 버튼 */}
            <Button
              onClick={onRecordingToggle}
              variant={isRecording ? "destructive" : (hasPermission === false ? "outline" : "default")}
              size="lg"
              className="relative shrink-0 h-14 w-14 rounded-full shadow-lg"
              disabled={isLoading}
              title={
                hasPermission === false
                  ? "오디오 권한이 필요합니다. 클릭하여 권한을 요청하세요."
                  : isRecording
                    ? "녹음 중지"
                    : "시스템 오디오 녹음 시작"
              }
            >
              {isLoading ? (
                <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-current"></div>
              ) : isRecording ? (
                <MicOff size={24} />
              ) : (
                <Mic size={24} />
              )}
              {isRecording && !isLoading && (
                <span className="absolute -top-1 -right-1 w-4 h-4 bg-red-500 rounded-full animate-pulse"></span>
              )}
            </Button>

            {/* 언어 전환 버튼 */}
            <Button
              onClick={swapLanguages}
              variant="outline"
              size="sm"
              disabled={isRecording}
              className="shrink-0 h-8 w-8 rounded-full p-0"
              title="언어 전환"
            >
              <RotateCcw size={14} />
            </Button>
          </div>

          {/* 우측: Target 언어 선택 */}
          <div className="flex items-center gap-3">
            <Label className={isRecording ? "text-sm text-muted-foreground" : "text-sm"}>Target:</Label>
            <Select value={targetLang} onValueChange={onTargetLangChange} disabled={isRecording}>
              <SelectTrigger className="w-32">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {languages.map((lang) => (
                  <SelectItem key={lang.value} value={lang.value}>
                    {lang.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>

        {/* 하단: 회의 모드 토글 */}
        <div className="flex justify-center pt-3 border-t border-gray-100">
          <div className="flex items-center gap-3">
            <Label className="text-sm">회의 모드:</Label>
            <div className="flex items-center gap-2">
              <Label className="text-xs text-muted-foreground">오프라인</Label>
              <Switch checked={isOnlineMode} onCheckedChange={onModeToggle} />
              <Label className="text-xs text-muted-foreground">온라인</Label>
            </div>
          </div>
        </div>
      </div>

      {/* 질문 도우미 그룹 - 조건부 렌더링 */}
      {isQuestionHelperVisible && (
        <div className="bg-white rounded-lg p-4 border border-gray-200 shadow-sm">
          <QuestionHelper
            onQuestionSubmit={handleQuestionSubmit}
            suggestedQuestions={suggestedQuestions}
            sourceLang={sourceLang}
            targetLang={targetLang}
          />
        </div>
      )}

      {/* 디버그 모드 토글 그룹 */}
      <div className="bg-white rounded-lg px-4 py-2 border border-gray-200 shadow-sm">
        <div className="flex items-center justify-center gap-6">
          {/* 디버그 아이콘 */}
          <Bug size={18} className="text-muted-foreground" />
          
          {/* 실시간 통역 디버그 */}
          <div className="flex items-center gap-2">
            <Label className="text-sm text-muted-foreground">실시간 통역</Label>
            <Switch 
              checked={isDebugMode} 
              onCheckedChange={onDebugModeToggle}
              className="data-[state=checked]:bg-orange-600"
            />
          </div>

          {/* 요약패널 디버그 */}
          <div className="flex items-center gap-2">
            <Label className="text-sm text-muted-foreground">요약패널</Label>
            <Switch 
              checked={isSummaryDebugMode} 
              onCheckedChange={onSummaryDebugModeToggle}
              className="data-[state=checked]:bg-blue-600"
            />
          </div>
        </div>
      </div>

      {/* 실시간 통역 디버그 패널 - 조건부 렌더링 */}
      {isDebugMode && (
        <div className="animate-in slide-in-from-top-2 duration-300">
          <DebugPanel
            onClearAll={onClearAllTranslations}
            onAddItem={onAddItem}
            onAddTranscription={onAddTranscription}
            onAddTranslation={onAddTranslation}
          />
        </div>
      )}
    </div>
  );
}