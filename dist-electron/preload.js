"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const electron_1 = require("electron");
const audio_config_js_1 = require("./audio-config.js");
// Expose protected methods that allow the renderer process to use
// the ipcRenderer without exposing the entire object
electron_1.contextBridge.exposeInMainWorld('electronAPI', {
    // Add any APIs you want to expose to your React app here
    platform: process.platform,
    versions: process.versions,
    // Audio recording APIs
    checkAudioPermissions: () => electron_1.ipcRenderer.invoke(audio_config_js_1.ipcEvents.checkPermissions),
    requestAudioPermissions: () => electron_1.ipcRenderer.invoke(audio_config_js_1.ipcEvents.requestPermissions),
    enableLoopbackAudio: () => electron_1.ipcRenderer.invoke(audio_config_js_1.ipcEvents.enableLoopbackAudio),
    disableLoopbackAudio: () => electron_1.ipcRenderer.invoke(audio_config_js_1.ipcEvents.disableLoopbackAudio),
    saveAudioFile: (filePath, arrayBuffer) => electron_1.ipcRenderer.invoke(audio_config_js_1.ipcEvents.saveAudioFile, filePath, arrayBuffer),
});
// Remove this line to stop exposing dangerous node APIs to the renderer
// window.eval = global.eval = function () {
//   throw new Error(`Sorry, this app does not support window.eval().`)
// } 
