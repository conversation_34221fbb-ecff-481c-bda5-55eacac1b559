import { execSync } from 'child_process';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

console.log('🚀 Building Electron app...');

try {
  // 1. Build React app
  console.log('📦 Building React app...');
  execSync('npm run build', { stdio: 'inherit' });

  // 2. Build Electron main process
  console.log('⚡ Building Electron main process...');
  execSync('tsc -p electron/tsconfig.json', { stdio: 'inherit' });

  // 3. Copy preload script
  const preloadSrc = path.join(__dirname, '../electron/preload.ts');
  const preloadDest = path.join(__dirname, '../dist-electron/preload.js');
  
  // Ensure dist-electron directory exists
  const distElectronDir = path.join(__dirname, '../dist-electron');
  if (!fs.existsSync(distElectronDir)) {
    fs.mkdirSync(distElectronDir, { recursive: true });
  }

  // Compile preload.ts to preload.js
  execSync(`npx tsc ${preloadSrc} --outDir ${distElectronDir} --target ES2020 --module CommonJS`, { stdio: 'inherit' });

  console.log('✅ Electron build completed!');
  console.log('💡 Run "npm run electron:pack" to create distributable package');

} catch (error) {
  console.error('❌ Build failed:', error.message);
  process.exit(1);
} 