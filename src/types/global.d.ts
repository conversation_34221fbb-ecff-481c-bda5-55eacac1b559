// Global type definitions for Electron API exposed through preload script

interface ElectronAPI {
  platform: string;
  versions: NodeJS.ProcessVersions;
  
  // System audio recording APIs
  checkAudioPermissions: () => Promise<boolean>;
  requestAudioPermissions: () => Promise<boolean>;
  enableLoopbackAudio: () => Promise<{ success: boolean; message?: string }>;
  disableLoopbackAudio: () => Promise<{ success: boolean; message?: string }>;
  startRecording: () => Promise<{ success: boolean; message: string }>;
  stopRecording: () => Promise<{ success: boolean; filePath?: string; message: string }>;
  saveAudioFile: (filePath: string, data: ArrayBuffer) => Promise<{ success: boolean; message?: string }>;
}

declare global {
  interface Window {
    electronAPI: ElectronAPI;
  }
}

export {};
